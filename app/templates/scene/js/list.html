{% load  compress %}
{% compress js inline %}
<script>
    $(document).ready(function () {

        $('.fa-download').off().on('click', function (e) {
            e.stopPropagation();
            let comment_id = $(this).attr('data-comment-id');
            $.ajax({
                type: "GET",
                url: "/top/get_file_download_link",
                data: {
                    "comment_id": comment_id,
                    'type': 'scene'
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    window.location.href = response.url;
                },
                fail: function (response) {
                    toastr.error('エラーが発生しました', 'ファイルをダウンロード');
                }
            })
        });

        $('.textarea').attr({
            accept: 'image/*',
            oninvalid: "this.setCustomValidity('このフィールドは必須項目です。')",
            oninput: "setCustomValidity('')"
        });

        var is_rating = '{{ is_rating }}';

        $('.massenger__videoicon.ic-null').on('mouseover', function(){
            var favorite_rule = '{{is_favorite}}';
            if(favorite_rule == 'True'){
                $(this).find('.null').hide();
                $(this).find('.half').show();
            }
        }).on('mouseout', function(){
            $(this).find('.null').show();
            $(this).find('.half').hide();
        });

        $('.massenger__videoicon.ic-done').on('mouseover', function(){
            var favorite_rule = '{{is_favorite}}';
            if(favorite_rule == 'True'){
                $(this).find('.done').hide();
                $(this).find('.half').show();
            }
        }).on('mouseout', function(){
            $(this).find('.done').show();
            $(this).find('.half').hide();
        });

        $('.stars li').on('mouseover', function(){
            var onStar = parseInt($(this).data('value'), 10); // The star currently mouse on

            // Now highlight all the stars that's not after the current hovered star
            $(this).parent().children('li.star').each(function(e){
                if (e < onStar) {
                    $(this).addClass('hover');
                    if($(this).find('.fas')){
                        $(this).find('i').removeClass('fas');
                        $(this).find('i').addClass('far');
                    }
                }
                else {
                    $(this).removeClass('hover');
                }
            });

        }).on('mouseout', function(){
            $(this).parent().children('li.star').each(function(e){
                $(this).removeClass('hover');
                if($(this).find('.far') && $(this).hasClass('selected')){
                    $(this).find('i').removeClass('far');
                    $(this).find('i').addClass('fas');
                }
            });
        });


        /* 2. Action to perform on click */
        $('.stars li').on('click', function(){
            var onStar = parseInt($(this).data('value'), 10); // The star currently selected
            var stars = $(this).parent().children('li.star');
            if(is_rating === "False") {
                alert("評価権限がないので、システム管理者にご連絡ください。")
                return false;
            }
            $.ajax({
                url: '{% url 'app:rating_scene'  %}',
                type: 'POST',
                data: {
                    value: onStar,
                    scene_id: $(this).parents('.massenger__video').children('.carousel').attr('id').replace('videoCarousel-',''),
                }
            })
            .done(function() {
                console.log("success");
                for (i = 0; i < stars.length; i++) {
                    $(stars[i]).removeClass('selected');
                    $(stars[i].querySelector('i')).removeClass('fas');
                    $(stars[i].querySelector('i')).addClass('far');
                }

                for (i = 0; i < onStar; i++) {
                    $(stars[i]).addClass('selected');
                    $(stars[i].querySelector('i')).addClass('fas');
                }
            })
            .fail(function() {
                console.log("error");
            })
        });

        $('.comment__textarea-emoticonimg').on('click', function(event){
            event.preventDefault();
            var comment_content = $(this).find('img').attr('src');
            url = $(this).parents('form').attr('action');
            if (!comment_content) {
                return;
            }
            $(this).parents('.comment__textarea-emoticoninfo').find('a').attr('aria-disabled', true)
            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    comment: comment_content,
                    owner_id: $(this).parents('form').find('#id_owner_id').val(),
                    scene: url.replace('/scene/comment/', '').replace('/',''),
                    stamp: true
                },
            })
            .done(function() {
                location.reload();
            })
            .fail(function() {
                alert("error");
            })
        });

        let current_filter = '{{ filter }}';
        let current_sort = '{{ sort }}';
        let current_order = '{{ order }}';
        let current_title = '{{ scene_title }}';
        let product_scene_id = '{{ product_scene.pk }}';
        let user_id = '{{ user.id }}';

        $.ajax({
            url: '/ajax/get_button/',
            type: 'POST',
            data: {
                title: current_title,
                filter: current_filter,
                sort: current_sort,
                order: current_order,
                product_scene_id: product_scene_id,
                user_id: user_id,
                'csrfmiddlewaretoken': '{{ csrf_token }}',
            },
            success: function(result) {
                if(result.new_scene) {
                    result.new_scene.forEach(function(e,i) {
                        let element = $('#new-' + e)
                        if(element.length) {
                           element.addClass('label-new');
                           element[0].innerText = 'new'
                        }
                    })
                }
                if (result.next != null) {
                   let target_next = $('.fa-caret-right').parent();
                    target_next.attr('href', "{% url 'app:scene_list' %}?product_id={{ product.pk }}&title_id=" +
                        result.next + "&product_scene_id={{ product_scene.pk }}" + "&filter=" +
                        '{{ filter }}' + "&sort=" + '{{ sort }}' + "&order=" + '{{ order }}');
                    target_next.removeClass('disable-click')
                }

                if (result.prev != null) {
                    let target_prev = $('.fa-caret-left').parent();
                    target_prev.attr('href', "{% url 'app:scene_list' %}?product_id={{ product.pk }}&title_id=" +
                        result.prev + "&product_scene_id={{ product_scene.pk }}" + "&filter=" +
                        '{{ filter }}' + "&sort=" + '{{ sort }}' + "&order=" + '{{ order }}');
                    target_prev.removeClass('disable-click')
                }
            }
        });

        let carousel_list = $('.massenger__column')
        $.each(carousel_list, function(i,v) {
            if(i < carousel_list.length - 1) {
                defaultActiveAudioPin(v);
            }
        });

        let comment_desc = $('.comment__content-desc, .comment__reply-desc');
        $.each(comment_desc, function(i,v) {
            let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
            v.innerHTML = v.innerHTML.replace(regex, "<a href=$&>$&</a>");
        })
    });

    let checkCarousel = function(video) {
        if(currentVideo && (currentVideo === video || $(video).parents('.carousel-inner')[0] === $(currentVideo).parents('.carousel-inner')[0])) {
            return true;
        }
        return false;
    };

    let hasAudio  = function (video) {
        return video.mozHasAudio ||
        Boolean(video.webkitAudioDecodedByteCount) ||
        Boolean(video.audioTracks && video.audioTracks.length);
    };

    let pinComment = function (movieId, obj) {
        sceneId = $(obj).parents('.massenger__column').find('.carousel-indicators li.active,.heart-active').data('scene');
        let _VIDEO = document.getElementById('movie-' + sceneId),
            _PIN = $('.pin-' + movieId),
            _COMMENT = $('.movie-' + movieId),
            _PIN_VIDEO = $('.pin_video-' + movieId);
        _VIDEO.pause();

        let currentTime = msToTime(_VIDEO.currentTime);
        let textComment = _COMMENT.val().replace(_PIN.val() + '\n', '');
        _COMMENT.val(currentTime + '\n' + textComment);
        _PIN.val(currentTime);
        _PIN_VIDEO.val(sceneId);
        $('.comment__textarea-box').trigger('keydown').trigger('keyup');
    };

    let audioElement = 'false';
    let currentVideo;
    let seekToTime = function (movieId, time, pin_video="None", this_dom) {
        let movie_id = movieId;
        let carousel_active = $('#videoCarousel-' + movieId).find('.carousel-indicators li').first();

        if(pin_video !== "None") {
            movie_id = pin_video;
            carousel_active = $('#videoCarousel-' + movieId).find('.carousel-indicators [data-scene="'+ pin_video +'"]');
        }
        let _VIDEO = document.getElementById('movie-' + movie_id);
        $('#videoCarousel-' + movie_id).find('.carousel-indicators').find('li.active').removeClass('active');
        if(!carousel_active.hasClass('heart-active')) {
            carousel_active.addClass('active');
        }
        $(_VIDEO).parents('.carousel-inner').find('.item').removeClass('active');
        $(_VIDEO).parent('.item').addClass('active');


        let audio_playing_state = _VIDEO.getAttribute('play_audio');
        let new_audio = $(this_dom).find('.hide audio')[0];
        if(new_audio === audioElement && currentVideo === _VIDEO && audio_playing_state === 'playing') {
            $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
            $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
            _VIDEO.pause();
            audioElement.pause();
            _VIDEO.setAttribute('play_audio', 'pause');
        } else if(new_audio === audioElement && currentVideo === _VIDEO && audio_playing_state === 'pause') {
            let video_has_audio = hasAudio(_VIDEO);
            let click_play_button = this_dom.getAttribute('clicked_play');
            if(video_has_audio && click_play_button !== 'true') {
                $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                $(audioElement).parents('.comment__pintime').removeClass('active-item');
                _VIDEO.setAttribute('play_audio', 'false');
                _VIDEO.setAttribute('audio_play', 'false');
                _VIDEO.muted = false;
                audioElement.pause();
                audioElement.currentTime = 0;
                audioElement.ended = true;
                audioElement = 'false';
                currentVideo = null;
                _VIDEO.pause()
            } else {
                if(!video_has_audio) {
                    _VIDEO.setAttribute('video_no_sound', 'true');
                }
                _VIDEO.setAttribute('play_from_seek', 'false');
                _VIDEO.play()
            }
        } else if(!(new_audio === audioElement && currentVideo === _VIDEO && audio_playing_state === 'buffering')){
            if(audio_playing_state !== 'buffered') {
                $('video').each(function(index, video) {
                    video.setAttribute('pause_by_other', 'true');
                    delete video.dataset.pin_time;
                    video.pause();
                });

                let active_audio_pin = checkCarouselActiveAudioPin(_VIDEO);
                let same_carousel_different_audio_pin = active_audio_pin && active_audio_pin !== new_audio;
                if(audioElement !== 'false') {
                    let old = audioElement;
                    audioElement = 'false';
                    old.setAttribute('pause_by_other', 'true');
                    old.muted = true;
                    old.currentTime = 0;
                    old.pause();

                    if(checkCarousel(_VIDEO)) {
                        $(old).parents('.comment__pintime').removeClass('active-item');
                    } else if (same_carousel_different_audio_pin) {
                        $(active_audio_pin).removeClass('active-item');
                    }
                    $(old).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                    $(old).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                } else {
                    if(same_carousel_different_audio_pin) {
                        $(active_audio_pin).removeClass('active-item');
                    }
                }
            }
            let file_comment = $(this_dom).find('.comment__download');
            let fps = $(this_dom).parents('.massenger__column').find('.carousel-indicators li.active,.heart-active').data('fps-movie');
            _VIDEO.setAttribute('pause_by_other', 'false');
            if(file_comment.length !== 0){
                let file = file_comment.find('.comment__download-icon-down');
                if (file && file.text().match(/\.(mp3|wav|ogg|MP3|WAV|OGG)$/)){
                    currentVideo = _VIDEO;
                    audioElement = file.siblings('.hide').children()[0];
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').addClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').removeClass('hide');
                    _VIDEO.setAttribute('audio_play', 'true');
                    audioElement.setAttribute('readyStateAudio', audioElement.readyState);
                    audioElement.setAttribute('readyStateVideo', _VIDEO.readyState);
                    if (audioElement.readyState >= 4 && _VIDEO.readyState >= 4) {
                        let video_pin_time = timeToSecond(time, fps);
                        _VIDEO.dataset.pin_time = video_pin_time.toString();
                        _VIDEO.muted = true;
                        audioElement.currentTime = 0;
                        _VIDEO.currentTime = video_pin_time;
                    } else {
                        if('{{ request.user_agent.os.family }}' === 'iOS') {
                            _VIDEO.setAttribute('play_audio', 'buffering');
                        }
                        if(_VIDEO.readyState < 2) {
                            _VIDEO.load();
                        }
                        if(audioElement.readyState < 2) {
                            audioElement.load();
                        }
                    }
                    $(this_dom).addClass('active-item');
                }
            } else {
                _VIDEO.setAttribute('audio_playing', 'false');
                _VIDEO.currentTime = timeToSecond(time, fps);
                _VIDEO.play();
            }
        }

        this_dom.setAttribute('clicked_play', 'false')
    };

    let canplaythrough = function(id, pin_video="None", time, fps, e) {
        console.log('can play', e.getAttribute('readyStateAudio'),e.getAttribute('readyStateVideo'));
        let videoRS = e.getAttribute('readyStateVideo');
        let audioRS = e.getAttribute('readyStateAudio');
        if(videoRS && audioRS && (videoRS < 4 || audioRS < 4)) {
            let _VIDEO = document.getElementById('movie-' + id);
            if(pin_video !== "None") {
                _VIDEO = document.getElementById('movie-' + pin_video);
            }
            e.setAttribute('readyStateVideo', _VIDEO.readyState);
            e.setAttribute('readyStateAudio', e.readyState);
            console.log('vars', _VIDEO.readyState, e.readyState);
            if('{{ request.user_agent.os.family }}' === 'iOS') {
                alert('Audio completely loaded. Please click pin button again');
                _VIDEO.setAttribute('play_audio', 'buffered');
            } else {
                let video_pin_time = timeToSecond(time, fps);
                _VIDEO.dataset.pin_time = video_pin_time.toString();
                _VIDEO.muted = true;
                _VIDEO.currentTime = video_pin_time;
                e.currentTime = 0;
            }
        }
    };

    let playing = function(dom){
        let audio_pin = checkCarouselActiveAudioPin(dom);
        let new_video = !currentVideo && audio_pin;
        let video_in_different_carousel = currentVideo && dom !== currentVideo;
        if(new_video || video_in_different_carousel) {
            $('video').each(function(index, video) {
                video.setAttribute('pause_by_other', 'true');
                delete video.dataset.pin_time;
                video.pause();
            });

            if(audioElement !== 'false') {
                audioElement.pause();
                audioElement.currentTime = 0;
                audioElement.ended = true;
            }

            if(audio_pin) {
                if('{{ request.user_agent.os.family }}' === 'iOS') {
                    alert('オーディオピンを選択しください。')
                } else {
                    audio_pin[0].onclick()
                }
            } else {
                currentVideo = dom;
                dom.muted = false;
                dom.play();
            }
        } else {
            let seeked = dom.getAttribute('play_from_seek');
            console.log('check', seeked);
            let audio_state = dom.getAttribute('play_audio');
            if(seeked === 'true') {
                dom.setAttribute('play_from_seek', 'false');
            } else {
                let no_sound = dom.getAttribute('video_no_sound');
                if(audio_state === 'pause') {
                    let pin_time = dom.dataset['pin_time'];
                    if(pin_time) {
                        let currentDomTime = Math.round(dom.currentTime * 100) / 100;
                        let before_pintime = pin_time > currentDomTime;
                        let larger_than_audio_length = (Math.round(dom.currentTime * 100) / 100) > audioElement.duration;
                        let matching_with_audio = currentDomTime - audioElement.currentTime === parseFloat(pin_time);
                        if(!before_pintime && !larger_than_audio_length && !matching_with_audio) {
                            dom.setAttribute('play_audio', 'playing');
                            console.log('video', dom.currentTime)
                            dom.play();
                            audioElement.muted = false;
                            audioElement.play();
                            $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').addClass('hide');
                            $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').removeClass('hide');
                        } else {
                            $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                            $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                        }
                    }
                } else if(no_sound === 'true') {
                    audioElement.play()
                }
            }

        }
        if ($(dom).siblings('.label-new')[0]) {
            $(dom).siblings('.label-new')[0].remove();
            $.ajax({
                url: '/ajax/preview_video/',
                type: 'POST',
                data: {
                    scene_id: $(dom).data('scene'),
                    user_id: {{ user.id }},
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                }
            });
        }
    };

    let pausing = function(dom){
        let pause = dom.getAttribute('pause_by_other');
        if(pause !== 'true' && dom === currentVideo) {
            if(audioElement !== 'false' && dom.getAttribute('play_audio') === 'playing') {
                if(!dom.ended) {
                    audioElement.pause();
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                    dom.setAttribute('play_audio', 'pause');
                }
            }
        } else if (pause === 'true') {
            dom.setAttribute('pause_by_other', 'done');
        }
    };

    let end_audio = function(e) {
        $(e).parents('.comment__download').siblings('.fa-play-circle').removeClass('hide');
        $(e).parents('.comment__download').siblings('.fa-pause-circle').addClass('hide');
        currentVideo.setAttribute('play_audio', 'ended');
    };

    let seek = function(dom){
        dom.setAttribute('play_from_seek', 'true');
        let pin_time = dom.dataset['pin_time'];
        if(dom.getAttribute('audio_play') === 'true' && audioElement !== 'false'){
            if(pin_time) {
                let currentDomTime = Math.round(dom.currentTime * 100) / 100;
                if(pin_time > currentDomTime) {
                    audioElement.ended = true;
                    dom.muted = false;
                    dom.play();
                    console.log('timeout', (pin_time - currentDomTime)*1000);
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                    dom.setAttribute('play_audio', 'pause');
                    audioElement.muted = true;
                    var time_out_id = window.setTimeout(function() {
                        dom.muted = true;
                        audioElement.muted = false;
                        audioElement.currentTime = 0;
                        audioElement.play();
                        $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').addClass('hide');
                        $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').removeClass('hide');
                        dom.setAttribute('play_audio', 'playing');
                    }, (pin_time - currentDomTime)*1000);

                    while(time_out_id--) {
                        window.clearTimeout(time_out_id);
                    }
                } else if(currentDomTime > (pin_time +audioElement.duration)) {
                    console.log('1');
                    audioElement.ended = true;
                    audioElement.muted = true;
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').removeClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').addClass('hide');
                    dom.setAttribute('play_audio', 'pause');
                } else if(currentDomTime - audioElement.currentTime === parseFloat(pin_time)) {
                    audioElement.muted = false;
                    audioElement.play();
                    dom.play();
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').addClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').removeClass('hide');
                    dom.setAttribute('play_audio', 'playing');
                } else {
                    audioElement.muted = false;
                    audioElement.currentTime = currentDomTime - pin_time;
                    audioElement.play();
                    dom.play();
                    $(audioElement).parents('.comment__download').siblings('i.fa-play-circle').addClass('hide');
                    $(audioElement).parents('.comment__download').siblings('i.fa-pause-circle').removeClass('hide');
                    dom.setAttribute('play_audio', 'playing');
                }
            }
        } else {
            dom.muted = false;
            dom.play();
        }
    };

    let msToTime = function (duration) {
        let d = Number(duration),
            h = Math.floor(d / 3600),
            m = Math.floor(d % 3600 / 60),
            s = Math.floor(d % 3600 % 60);

        let hDisplay = h > 0 ? (h + ":") : "",
            mDisplay = m > 0 ? (m + ":") : "0:",
            sDisplay = s > 0 ? s < 10 ? "0" + s : s : "00";

        return hDisplay + mDisplay + sDisplay;
    };

    let timeToSecond = function (time, fps) {
        if(time === 'None') return 0;
        let p = time.split(':'),
            s = 0, m = 1;

        while (p.length > 0) {
            s += m * parseInt(p.pop(), 10);
            m *= 60;
        }

        let ml = time.split('.')
        if(ml.length > 1){
            let z = 0;
            if(fps !== "None") {
                z = parseInt(ml.pop(), 10) * 99 / parseInt(fps,10);
            }
            s = s + '.' + parseInt(z,10);
        }

        return s;
    };

    let updateTag = function (obj) {
        let parent = $(obj).parents('.massenger__left')
        let active = parent.find('.carousel-indicators li.active,.heart-active');
        let list_li = parent.find('.carousel-indicators');
        let scene_id = active.data('scene');
        let tag = $(obj).data('tag');
        let active_pin = $(obj).parents('.col-md-6').next().find('.active-item');
        let comment_id = null;
        let pin_video = null;
        if(active_pin.length === 1) {
            let pin_class = active_pin[0].className.match('pin-[a-zA-Z0-9-]*');
            if (pin_class) {
                pin_video = pin_class[0].replace('pin-', '')
            }
            comment_id = active_pin.find('.comment__download audio')[0].getAttribute('id');
        }

        if(tag === 2) {
            $(obj).removeClass('ic-done');
            $(obj).addClass('ic-null');
            $(obj).data('tag', '1');
        } else {
            $(obj).removeClass('ic-null');
            $(obj).addClass('ic-done');
            $(obj).data('tag', '2');
        }

        $.ajax({
            method: 'POST',
            url: "tag/update/"+ scene_id + "/?tag=" + tag,
            data: {
                comment: comment_id,
                pin_video: pin_video
            },
            success: function (result) {
                location.reload();

                switch(result.tag){
                    case 1:
                        list_li.find('li.disable-click').removeClass('disable-click');
                        break;
                    case 2:
                        list_li.find('li.disable-click').removeClass('disable-click');
                        list_li.find('li').not('.disable-click').not('.active').addClass('disable-click');
                        break;
                }
            }
        });
    };

    let submit = function (ojb) {
        ojb.closest('form').submit();
    };

    $('.massenger__left').click(function () {
        $('.massenger__remove, .massenger__change').removeClass('show-item');
        $(this).find('.massenger__remove, .massenger__change').toggleClass('show-item');
    });

    $(document).on('change', '#id_file', function (e) {
        let fileName = e.target.files[0].name;
        let clear_file_dom = "clear_" + $(e.target)[0].classList[1];
        let comment_box = $(this).closest('.comment__textarea');
        if (comment_box.find('.comment__textarea-file').length > 0) {
            comment_box.find('.comment__textarea-file span').text(fileName);
        } else {
            comment_box.find('.comment__textarea-link').prepend('<div class="comment__textarea-file"><span>' + fileName +
                '</span><button type="button" class="close '+ clear_file_dom +'" id="clear_file"  aria-hidden="true">×</button></div>')
        }
    });

    $('.comment__textarea-box').keydown(function () {
        var el = this;
        setTimeout(function () {
            el.style.cssText = 'height: auto';
            el.style.cssText = 'height:' + el.scrollHeight + 'px';
        }, 0);
    });

    $('.comment__textarea-box').keyup(function () {
        var comment_content = $(this).val();
        $(this).siblings('.comment__textarea-submit').find('button').css('color',"#258BCF");
        console.log("comment_content = ", comment_content);
        if (!comment_content) {
            $(this).siblings('.comment__textarea-submit').find('button').css('color',"#707070");
            return;
        }
        let class_movie_time = $(this).attr("class").match(/movie-[\w-]*\b/)[0];
        let class_pin_time = class_movie_time.replace('movie-', '');
        let scene_active = $(this).parents('.massenger__column').find('.carousel-indicators li.active,.heart-active').data('scene');
        let pin_time = comment_content.match(/^(\d?\d:)?([0-5]?\d):([0-5]\d)(.([0-9]?\d))?/);
        if (!pin_time) {
             $('.pin-' + class_pin_time).val('');
             $('.pin_video-' + class_pin_time).val('');
        }

        if (pin_time && pin_time[0]) {
            $('.pin-' + class_pin_time).val(pin_time[0]);
            $('.pin_video-' + class_pin_time).val(scene_active);
        }
    });

    $(document).on('click', '#clear_file', function () {
        $(this).parents('.comment__textarea-link').find('.comment__textarea-file').remove();
        let file_dom =  $(this)[0].classList[1].replace('clear_','');
        $('.' + file_dom).val('');
    });

    $('.setting-config').click(function () {
        $('.massenger__config').toggleClass('show-item');
        $('.setting-config').toggleClass('active');
        if ($('.massenger__config').hasClass('show-item')) {
            $('video').removeAttr('controls');
        } else {
            $('video').attr('controls', '');
        }
    });

    $('.delete-emoticon').on('click', function() {
        $(this).siblings('.delete-emoticon-trash').toggleClass('hide')
    });

    let editScene = function(obj){
        let sceneId = $(obj).parents('.massenger__column').find('.carousel-indicators li.active,.heart-active').data('scene');
        window.location.href = 'change/' + sceneId;
    };

    let defaultActiveAudioPin = function(carousel) {
        if ($(carousel).find('.videoicon.ic-null').length) {
            if (!$(carousel).find('.col-md-6.col-sm-6 .comment .comment__top-pin .active-item').length) {
                let audio_pin = $(carousel).find('.col-md-6.col-sm-6 .comment .comment__top-pin');
                if (audio_pin.length) {
                    audio_pin.last().find('.comment__pintime').addClass('active-item');
                }
            }
        }
    };

    let checkCarouselActiveAudioPin = function (video) {
        let active_items = $(video).parents('.col-sm-6').next().find('.comment__top-pin .fixed-item, .comment__top-pin .active-item');
        if(active_items.length) {
            return active_items.last();
        } else {
            return false;
        }
    };


    $('.fa-play-circle').on('click', function(e) {
        e.target.parentElement.setAttribute('clicked_play', 'true');
    });

    window.history.forward();
</script>
{% endcompress %}