{% load static %}
{% load util %}

{% if checking_scene_titles.exists %}
  <div class="pd-section pd-section--delivery-video scene-home-pd refactor">
    <div class="pd-section__title sheading sheading--18">チェックバック待ち
        <span class="pd-section__title-sub">ほかの作業に集中しよう</span></div>

    <div class="pd-section__content">
      <div class="pd-section__video mscrollbar">
        {% for st in checking_scene_titles %}
        {% with st.get_last_version as scene %}
            {% if scene %}
              {% include 'top/_cscene_delivery.html' with scene=scene st=st role=role view_only=view_only is_done=is_done  height="80px" show_button="hide" style="style='margin-left: 4px;'" %}
            {% endif %}
          {% endwith %}
        {% endfor %}
      </div>
    </div>
  </div>
{% endif %}
