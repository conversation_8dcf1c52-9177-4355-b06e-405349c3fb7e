{% load util %}

<!DOCTYPE html>

<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>契約書_{{ instance.contract_code }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Contract pdf */
        @font-face {
            font-family: 'Axis L';
            src: url('{{ axis_l_path }}') format('opentype');
            font-style: normal;
        }

        @font-face {
            font-family: 'Axis R';
            src: url('{{ axis_r_path }}') format('opentype');
            font-style: normal;
        }

        @font-face {
            font-family: 'Axis B';
            src: url('{{ axis_b_path }}') format('opentype');
            font-style: normal;
        }

        .font-R {
            font-family: 'Axis R';
        }
        
        .container {
            max-width: 970px;
            padding-top: 0px;
            padding-left: 100px;
            padding-right: 100px;
            padding-bottom: 100px;
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table thead {
            border-style: hidden !important;
            border: none;
        }

        table thead th:not(:nth-child(2)) {
            padding-left: 0 !important;
        }
        .table-business tbody tr,
        .table-upload-contract tbody tr:not(:first-child) {
            border-top: 0.3mm solid #53565A !important;
        }


        .table-business thead tr,
        .table-upload-contract thead tr{
            border: none !important;
        }

        .table-business thead tr td,
        .table-upload-contract thead tr td {
            border-style : hidden!important;
        }

        .border-bottom {
            border-bottom: 0.3mm solid #53565A !important;
        }

        .table-business tr.no-border,
        .table-upload-contract tr.no-border {
            border-top: none !important;
        }

        .table-content tbody tr:not(:last-child) td {
            border-top: none !important;
            border-bottom: 0.5mm solid #D3D3D3 !important;
        }

        .table-content tr td {
            border-top: none !important;
            border-bottom: none !important;
        }

        table:not(.table-info) tr td:nth-child(1) {
            padding-left: 0 !important;
            vertical-align: top;
        }

        .table-info tr th, .table-info tr td {
            padding: 2px;
        }

        .table-info tbody tr td {
            padding-left: 8px;
        }

        .table-content {
            margin-top: -32px !important;
            margin-bottom: -22px !important;
        }

        table tr td {
            vertical-align: top;
        }

        table tr td:nth-child(1) {
            padding-left: 0 !important;
            vertical-align: top;
        }

        table tr td:nth-child(2) {
            padding-right: 0 !important;
            word-break: break-word;
            white-space: pre-line;
        }

        .table-content tbody tr td:nth-child(2) {
            text-align: right;
        }

        .export-wrap__header {
            margin-bottom: 50px;
        }

        .export-wrap__header-logo img {
            width: 80px;
            height: auto;
        }

        .mt-8 {
            margin-top: 8px;
        }

        .mb-8 {
            margin-bottom: 8px;
        }

        .mb-16 {
            margin-bottom: 16px;
        }

        .export-wrap__content .bodytext--13 {
            margin-bottom: 16px;
        }

        .export-wrap__content-wrap {
            margin-bottom: 40px;
        }

        .export-wrap__content-wrap:nth-child(3) {
            page-break-before: always;
        }

        table th,
        table td {
            color: #000000;
            padding: 4px;
            line-height: 23px;
            word-break: break-word;
            text-align: left;
        }

        table td.signature {
            text-align: right;
        }

        .signature span {
            margin-top: 8px;
            padding: 14px;
            border: 0.75mm solid #F21D44;
            border-radius: 26px;
            color: #F21D44;
            font-family: 'Axis B';
            font-weight: 800;
            font-size: 2.5mm;
        }

        table thead tr th {
            border: none;
            min-width: 100px;
            text-align: left;
        }

        table:not(.table-info, .table-business) tr th,
        table:not(.table-info) tr td {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        table.table-business tr th,
        table.table-business tr td {
            padding-top: 20px;
            padding-bottom: 20px;
        }

        table.table-business tr td.has-ol {
            padding-top: 0px;
            padding-bottom: 0px;
        }

        .letter-spacing-300 {
            letter-spacing: 4.8px;
        }

        .letter-spacing-230 {
            letter-spacing: 3.68px;
        }

        .letter-spacing-200 {
            letter-spacing: 3.2px;
        }

        .bodytext--13 {
            line-height: 20px;
            color: #000000 !important;
            font-weight: 300;
        }

        .heading--18 {
            line-height: 27px;
            color: #000000;
            font-weight: 400;
        }

        .heading--20 {
            line-height: 30px;
            color: #000000;
            font-weight: 400;
            word-break: break-word;
            white-space: pre-line;
        }

        .text-break {
            word-break: break-word;
            white-space: pre-line;
        }

        .export-wrap__footer .bodytext--13 {
            padding-top: 8px;
            text-align: right;
        }

        .text-11q-r {
            font-size: 3.75mm;
            font-family: 'Axis R';
            font-weight: 500;
        }

        .text-13q-r {
            font-size: 4.25mm;
            font-family: 'Axis L';
            font-weight: 600;
        }

        .text-16q-r {
            font-size: 5.25mm;
            font-family: 'Axis R';
            font-weight: 500;
        }

        .text-9q-l {
            font-size: 3.25mm;
            font-family: 'Axis L';
            line-height: 19px;
        }

        .text-9q-l-special {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: 100;
        }

        .text-9q-r {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-r {
            font-size: 2.75mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-l {
            font-size: 2.75mm;
            font-family: 'Axis L';
        }

        ol {
            padding-left: 12px;
        }

        td > ol:first-child {
            margin-top: -16px;
       }

       td > ol:last-child {
            margin-bottom: -16px;
       }
       
        td > ol > li:last-child {
            margin: -6px 0 -7px 0;
        }

        td > ol > li {
            margin: -6px 0 -12px 0;
        }

        td > ol > li > ol > li {
            margin: -6px 0 -12px 0;
        }

        ol > li > ol {
            margin-bottom: -18px;
            margin-top: -11px;
            margin-left: 8px;
        }
    
        .table-business td > ol > li:not(:last-child) {
            margin: -8px 0 -8px 0;
        }

        .table-business td > ol > li > ol > li:not(:last-child) {
            margin: -8px 0 -8px 0;
        }

        .table-business td > ol > li:first-child {
            margin: 2px 0 -8px 0;
        }

        .table-business td > ol > li > ol > li:first-child {
            margin: -16px 0 -8px 0;
        }

        .table-business td > ol > li:last-child {
            margin: -8px 0 0 0;
        }

        .table-business td > ol > li > ol > li:last-child {
            margin: -6px 0 -8px 0;
        }

        .table-business ol > li > ol {
            margin-bottom: 0;
            margin-top: 0;
            margin-left: 8px;
        }

        * {
            line-height: 19px;
            letter-spacing: 0.5px;
            overflow: visible !important;
        }
        
        table {
            page-break-inside: auto !important;
        }

        tr {
            page-break-inside: auto !important;
        }
       
        html, body { margin: 0; padding: 0; border: 0; font-size: 100%; font: inherit; vertical-align: baseline; }
        /* End Contract pdf */
    </style>
</head>

<body>
    <div>
        <main>
            <div class="container">
                <div class="export-wrap">
                    <div class="export-wrap__content ">
                        <div class="export-wrap__content-wrap">
                            <div class="heading--20 text-16q-r mb-16 letter-spacing-200">業務委託契約書（{% if semi_delegate %}準委任型{% else %}請負型{% endif %}）</div>
                            <div class="text-9q-l text-break">本書契約当事者欄記載のオーナーである<span class="font-R">{{ owner_name }}</span>と本書契約当事者欄記載のプロデューサーである<span class="font-R">{{ producer_name }}</span>は、株式会社ソレモ（以下「運営会社」という。）が運営するウェブサイト及びアプリケーションソフトウェアである「SOREMO」（以下総称して「本サービス」という。）を通じて<span class="font-R">{{ owner_name }}</span>が<span class="font-R">{{ producer_name }}</span>に対して業務を委託することについて以下の通り合意したので、契約（以下「本契約」という。）を締結する。なお、本契約上の用語の定義は特段の定めのない限り「SOREMO利用規約」（以下「サービス利用規約」という。）に従う。
                            </div>
                            <table class="table table-business" border="0" style="margin-top: 24px;">
                                <colgroup>
                                    <col span="1" style="width: 30%;">
                                    <col span="1" style="width: 70%;">
                                 </colgroup>
                                <tbody>
                                    <tr>
                                        <td class="text-11q-r">第1条	（目的）</td>
                                        <td class="text-9q-l">本契約の目的は、本サービスを通じて{{ producer_name }}が{{ owner_name }}から業務を受託し、{{ producer_name }}が制作等の業務を実施すること及び業務の過程で発生した成果物の権利帰属等について定めることにある。
                                        </td>
                                    </tr>
                                    {% if semi_delegate %}
                                    <tr>
                                        <td class="text-11q-r">第2条	（委託業務の内容等）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}は、{{ producer_name }}に対し、本契約書別表「件名」記載の案件に関して、本契約書別表「内容」記載の業務（以下「本業務」という。）を委託し、{{ producer_name }}はこれを受託する。</li>
                                            <li>{{ producer_name }}は、その裁量により本業務の一部又は全部を適切なアーティストに委託して、本業務を本契約書別表「期間」記載の期間（以下「開発期間」という。）において実施し、{{ owner_name }}は、本業務の実施及び本契約書別表「内容」に納品すべきもの（物に固定されない情報も含む。以下「納品物」という。）の記載がある場合は納品物の納品並びに第７条（権利帰属等）に定める権利譲渡、利用許諾及び権利不行使に対して本契約書別表「対価」記載の対価（以下「対価」という。）を支払う。納品物がある場合の納品形式、納品方法その他の詳細は本契約書別表に定める。</li>
                                            <li style="margin-bottom: 0 !important;">{{ producer_name }}は、{{ owner_name }}提供情報を十分に確認した上で、専門家としての知見等に基づき本業務を実施する。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td class="text-11q-r">第2条	（委託業務の内容等）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}は、{{ producer_name }}に対し、本契約書別表「件名」記載の案件に関して、本契約書別表「内容」記載の業務（以下「本業務」という。）を委託し、{{ producer_name }}はこれを受託する。</li>
                                            <li>{{ producer_name }}は、その裁量により本業務の一部又は全部を適切なアーティストに委託して、本契約書別表「納品物」記載の納品物（物に固定されない情報も含む。以下「納品物」という。）を完成し{{ owner_name }}に対して納品するものとし、{{ owner_name }}は、当該納品物の完成及び納品並びに第７条（権利帰属等）に定める権利譲渡、利用許諾及び権利不行使に対して本契約書別表「対価」記載の対価（以下「対価」という。）を支払う。納品形式、納品方法その他の詳細は本契約書別表に定める。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}提供情報を十分に確認した上で、専門家としての知見等に基づき本業務を実施する。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td class="text-11q-r">第3条	（業務遂行及び資料提供）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}は、その専門的知見に基づいた高度の水準において善良な管理者の注意をもって本業務を行う。</li>
                                            <li>{{ owner_name }}は、{{ producer_name }}の求めに応じて、{{ producer_name }}に対し、本業務を実施するために{{ producer_name }}が必要と判断する動画データその他の資料等の情報（以下「プロジェクト情報」という。）を提供する。</li>
                                            <li>{{ producer_name }}は、プロジェクト情報を、善良なる管理者の注意義務をもって管理するものとする。</li>
                                        </ol></td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第4条	（再委託）</td>
                                        <td class="text-9q-l">{{ producer_name }}は、自己の責任と費用負担において、{{ producer_name }}が適切と判断するアーティストに対し本業務の全部又は一部を再委託することができる。また、{{ producer_name }}の判断のもとで、当該再委託をした第三者から別の第三者に対する再々委託及びそれ以降の委託もできるものとする。</td>
                                    </tr>
                                    <tr class="border-bottom">
                                        <td class="text-11q-r">第5条	（報告義務）</td>
                                        <td class="text-9q-l">{{ producer_name }}は、{{ owner_name }}から本業務の進捗等について報告するよう要請があった場合は、速やかに報告する義務を負う。</td>
                                    </tr>

                                    {% if semi_delegate %}
                                    <tr class="no-border">
                                        <td class="text-11q-r">第6条	（業務の完了、納品及び担保責任）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}は、開発期間満了日に、すみやかに運営会社所定の形式による業務完了通知を{{ owner_name }}に対して行う。</li>
                                            <li>{{ owner_name }}は、前項の業務完了通知を受領後5営業日以内に、この内容を確認し、当該確認したことを運営会社所定の形式により{{ producer_name }}に対して通知する。</li>
                                            <li>前項に基づいて{{ owner_name }}が{{ producer_name }}に通知した時又は{{ producer_name }}が{{ owner_name }}に業務完了通知を行い{{ owner_name }}の異議なく5営業日が経過した時に本業務が完了したものとする。</li>
                                            <li>納品物がある場合は、{{ producer_name }}は、納品物を、開発期間満了日までに、本契約書別表に定める納品方法により{{ owner_name }}に対して納品する義務を負うものとし、この場合、以下の各項に従うものとする。</li>
                                            <li>{{ owner_name }}は、{{ producer_name }}から納品物の納品を受けた場合は、遅滞なく納品物について、検査を行い、合格した場合には検収した旨{{ producer_name }}に対し本サービス上で通知する。当該通知が{{ producer_name }}に到達した時点で検収完了とする。</li>
                                            <li>前項に定める検査において、納品物の不具合（データは本契約書別表「内容」に定める形式で提供されるものとし、当該形式以外の形式でのデータの利用に関する不具合又は{{ owner_name }}の使用する機器若しくはソフトウェアに起因する不具合を除く。）又は納品物の内容についての{{ owner_name }}提供情報の重要な部分との齟齬その他本契約書別表に定める内容との不合致（以下総称して「不適合」という。）があった場合は、{{ owner_name }}は、{{ producer_name }}に対し、{{ producer_name }}による納品通知後14営業日以内に、具体的な不適合を示して本サービス上で通知する。</li>
                                            <li>第6項に定める不適合の通知が、{{ producer_name }}による納品通知後14営業日以内に{{ producer_name }}に到達しなかった場合は、当該時点で納品物は{{ owner_name }}の検査に合格し検収完了したものとみなす。</li>
                                            <li>検収完了時点以降、本条に定める場合を除いて、{{ producer_name }}は納品物について何らの責任を負わず、{{ owner_name }}は、追完請求、損害賠償請求、契約解除をすることができないものとする。</li>
                                            <li>前項に定める検収完了時点以降であっても、不適合のうち第5項に定める検査では通常の注意をもってしても発見し得ない重大なデータの不具合については、納品物を{{ owner_name }}が受領した後1ヶ月以内に限り、当該不具合を発見し{{ producer_name }}に対し、5営業日以内に具体的な不具合の内容を示して通知した場合に限り、{{ producer_name }}は、{{ producer_name }}が合理的と判断する方法により追完する。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}提供情報に起因する納品物の不具合等一切の問題について何らの責任を負わない。</li>
                                            <li>{{ producer_name }}は、本契約又はサービス利用規約に特段の定めの無い限り、本条に定める責任以外に納品物について損害賠償責任、追完責任その他何らの責任を負わない。この場合において、{{ owner_name }}は、これらの責任追及又は契約解除を、不適合を理由として行うことはできないものとする。</li>
                                            <li>{{ producer_name }}は、本条に基づき追完義務を負う場合は、開発期間満了後といえども、当該追完にかかる対価又は費用を{{ owner_name }}請求することができない。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr class="no-border">
                                        <td class="text-11q-r">第6条	（納品及び担保責任）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}は、納品物を、本契約書別表に定める予定納期までに、同別表に定める納品方法により{{ owner_name }}に対して納品する義務を負う。但し、当該予定納期は、あくまで本契約締結時点の予定であり、{{ owner_name }}は、最終的な納期は前後する可能性があることを予め了承する。</li>
                                            <li>{{ owner_name }}は、{{ producer_name }}から納品物の納品を受けた場合は、遅滞なく納品物について、検査を行い、合格した場合には検収した旨{{ producer_name }}に対し本サービス上で通知する。当該通知が{{ producer_name }}に到達した時点で検収完了とする。</li>
                                            <li>前項に定める検査において、納品物の不具合（データは本契約書別表「納品物」に定める形式で提供されるものとし、当該形式以外の形式でのデータの利用に関する不具合又は{{ owner_name }}の使用する機器若しくはソフトウェアに起因する不具合を除く。）又は納品物の内容についての{{ owner_name }}が提供したプロジェクト情報その他の情報の重要な部分との齟齬その他本契約書別表に定める内容との不合致（納品物が通常有すべき品質を有しないことを含む。以下総称して「不適合」という。）があった場合は、{{ owner_name }}は、{{ producer_name }}に対し、{{ producer_name }}による納品通知後14営業日以内に、具体的な不適合を示して本サービス上で通知する。</li>
                                            <li>前項の不適合に関する通知を受領した場合、{{ producer_name }}は、{{ owner_name }}に対して、合理的な期間内に、納品物について修正等{{ producer_name }}が必要と判断する行為（以下「追完」という。）を行う。追完の具体的な方法及び回数は、{{ producer_name }}が合理的に選択することができる。</li>
                                            <li>第3項に定める不適合の通知が、{{ producer_name }}による納品通知後14営業日以内に{{ producer_name }}に到達しなかった場合は、当該時点で納品物は{{ owner_name }}の検査に合格し検収完了したものとみなす。</li>
                                            <li>検収完了時点以降、本条に定める場合を除いて、{{ producer_name }}は納品物について何らの責任を負わず、{{ owner_name }}は、追完請求、損害賠償請求、契約解除をすることができないものとする。</li>
                                            <li>前項に定める検収完了時点以降であっても、不適合のうち第2項に定める検査では通常の注意をもってしても発見し得ない重大なデータの不具合については、納品物を{{ owner_name }}が受領した後1ヶ月以内に限り、当該不具合を発見し{{ producer_name }}に対し、5営業日以内に具体的な不具合の内容を示して通知した場合に限り、{{ producer_name }}は、{{ producer_name }}が合理的と判断する方法により追完する。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}が提供した情報に起因する納品物の不具合等一切の問題について何らの責任を負わない。</li>
                                            <li>{{ producer_name }}は、本契約又はサービス利用規約に特段の定めの無い限り、本条に定める責任以外に納品物について損害賠償責任、追完責任その他何らの責任を負わない。{{ owner_name }}は、本条に定める場合以外に、損害賠償請求その他の請求又は契約解除を、不適合を理由として行うことはできないものとする。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    {% if semi_delegate %}
                                    <tr>
                                        <td class="text-11q-r">第7条	（権利帰属等）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>納品物を含む、本業務の過程又は結果として発生した楽曲、映像、ロゴその他一切の情報（以下「成果物」という。）に関する知的財産権（サービス利用規約に定義するとおり、著作権法第27条及び第28条の権利を含む。）は、{{ owner_name }}又は{{ owner_name }}に利用許諾をした第三者に原始的に帰属する権利を除いて、{{ producer_name }}又は{{ producer_name }}に利用許諾をした第三者に原始的に帰属する。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}に対し、対価全額の支払いを条件に、検収完了時を以って、納品物に関する知的財産権のうち著作権その他著作権法上の一切の権利（著作権法第27条及び第28条に規定する権利、著作隣接権、二次使用料請求権、貸与に係る報酬請求権、放送のIPマルチキャスト技術による同時再送信に係る補償金請求権、私的録音録画補償金請求権を含む。）を譲渡する。{{ producer_name }}は、当該知的財産権がその役職員又は再委託先その他第三者（以下「第三者等」という。）に帰属している場合は、予め当該権利を自己の責任と費用負担で取得する義務を負う。また、{{ producer_name }}は、納品物に関する著作者人格権、実演家人格権、肖像権又はパブリシティ権を、{{ owner_name }}又は{{ owner_name }}から許諾を得て納品物を利用する者（以下「オーナーら」という。）に対して行使せず、第三者等をして当該権利をオーナーらに対して行使させないものとする。</li>
                                            <li>前項の定めにかかわらず、本契約書別表「特記事項」に特段の定めがある場合は、当該定めが優先するものとする。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}が本条を含む本契約の定め又はサービス利用規約に違反した場合、本条に基づく権利譲渡に関する合意又は利用許諾の合意の全部又は一部を直ちに解除することができる。{{ producer_name }}は当該解除によって{{ owner_name }}に発生した損害の賠償義務を負わない。</li>
                                            <li>プロジェクト情報に関する知的財産権は、{{ owner_name }}又は{{ owner_name }}に対して利用許諾した第三者に帰属する。{{ owner_name }}はプロジェクト情報に関して、{{ producer_name }}に対し、本業務の遂行に必要な範囲で利用許諾する（第三者に対する再許諾を含む）。</li>
                                            <li>［オーナーの名称/氏名]は、納品物に含まれるアーティスト名、タイトル、サムネイル、肖像及び著作者名等のクレジット表示について、{{ producer_name }}が表示した内容において本契約書別表記載の「件名」記載の案件に関連する目的で、納品物の使用に伴い使用することができる。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td class="text-11q-r">第7条	（権利帰属等）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>納品物を含む、本業務の過程又は結果として発生した楽曲、映像、ロゴその他一切の情報（以下「成果物」という。）に関する知的財産権（サービス利用規約に定義するとおり、著作権法第27条及び第28条の権利を含む。）は、{{ owner_name }}又は{{ owner_name }}に利用許諾をした第三者に原始的に帰属する権利を除いて、{{ producer_name }}又は{{ producer_name }}に利用許諾をした第三者に原始的に帰属する。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}に対し、対価全額の支払いを条件に、開発期間満了日を以って、納品物（納品物がある場合）を含む成果物に関する知的財産権のうち著作権その他著作権法上の一切の権利（著作権法第27条及び第28条に規定する権利、著作隣接権、二次使用料請求権、貸与に係る報酬請求権、放送のIPマルチキャスト技術による同時再送信に係る補償金請求権、私的録音録画補償金請求権を含む。）を譲渡する。{{ producer_name }}は、当該知的財産権がその役職員又は再委託先その他第三者（以下「第三者等」という。）に帰属している場合は、予め当該権利を自己の責任と費用負担で取得する義務を負う。また、{{ producer_name }}は、納品物に関する著作者人格権、実演家人格権、肖像権又はパブリシティ権を、{{ owner_name }}又は{{ owner_name }}から許諾を得て納品物を利用する者（以下「オーナーら」という。）に対して行使せず、第三者をして当該権利をオーナーらに対して行使させないものとする。</li>
                                            <li>前項の定めにかかわらず、本契約書別表「特記事項」に特段の定めがある場合は、当該定めが優先するものとする。</li>
                                            <li>{{ producer_name }}は、{{ owner_name }}が本条を含む本契約の定め又はサービス利用規約に違反した場合、本条に基づく権利譲渡に関する合意又は利用許諾の合意の全部又は一部を直ちに解除することができる。{{ producer_name }}は当該解除によって{{ owner_name }}に発生した損害の賠償義務を負わない。</li>
                                            <li>プロジェクト情報に関する知的財産権は、{{ owner_name }}又は{{ owner_name }}に対して利用許諾した第三者に帰属する。{{ owner_name }}はプロジェクト情報に関して、{{ producer_name }}に対し、本業務の遂行に必要な範囲で利用許諾する（第三者に対する再許諾を含む）。</li>
                                            <li>［オーナーの名称/氏名]は、納品物に含まれるアーティスト名、タイトル、サムネイル、肖像及び著作者名等のクレジット表示について、{{ producer_name }}が表示した内容において本契約書別表記載の「件名」記載の案件に関連する目的で、納品物の使用に伴い使用することができる。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    {% if semi_delegate %}
                                    <tr>
                                        <td class="text-11q-r">第8条	（保証）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}は、{{ owner_name }}に対し、以下の各号に定める事項を保証する。
                                                <ol type="a" style="margin-bottom: -8mm !important">
                                                    <li>{{ producer_name }}が単独で、又は{{ owner_name }}の承諾を得て第三者に再委託した場合は当該第三者のみとともに、成果物を制作したこと</li>
                                                    <li>成果物に関する知的財産権が、従前から{{ owner_name }}その他第三者に帰属する権利を除いて、{{ producer_name }}に単独で原始的に帰属すること</li>
                                                    <li>前項に定める知的財産権が第三者に帰属する場合又は納品物に第三者の氏名、アーティスト名、タイトル、サムネイル、肖像等を使用する場合は、遅くとも開発期間満了日において、成果物を{{ owner_name }}又は{{ owner_name }}が指定した第三者が何らの制限なく成果物を使用又は利用するために必要となる一切の権利処理を、{{ producer_name }}の責任と費用負担において完了していること</li>
                                                    <li>成果物及び成果物に関する知的財産権について、第三者（著作権等管理事業法に定める著作権等管理事業者を含む。）に対して、現に管理委託をしておらず、将来においても管理委託をしないこと</li>
                                                </ol>
                                            </li>
                                            <li>万一、成果物に関連して、第三者から知的財産権の侵害等に関連してクレーム、異議申立て、訴訟提起その他紛争（以下「紛争等」という。）が発生した場合、{{ producer_name }}は自己の責任と費用負担において一切の対処を行うものとし、{{ owner_name }}において何らかの対応をせざるを得ない場合には{{ owner_name }}に対して協力する義務を負う。</li>
                                            <li  style="margin-bottom: -1mm !important;">紛争等に関連して{{ owner_name }}に損害（直接的損害に限らず、間接的損害、特別の事情による損害を含む。）が発生した場合は、{{ producer_name }}は当該損害について{{ owner_name }}に対し賠償する義務を負う。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td class="text-11q-r">第8条	（保証）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}は、{{ owner_name }}に対し、以下の各号に定める事項を保証する。
                                                <ol type="a" style="margin-bottom: -8mm !important">
                                                    <li>{{ producer_name }}が単独で、又は{{ owner_name }}の承諾を得て第三者に再委託した場合は当該第三者のみとともに、成果物を制作したこと</li>
                                                    <li>成果物に関する知的財産権その他一切の権利が、プロジェクト情報に関する知的財産権等従前から{{ owner_name }}その他第三者に帰属する権利を除いて、{{ producer_name }}に単独で原始的に帰属すること</li>
                                                    <li>前号に定める知的財産権が第三者に帰属する場合又は納品物に第三者の氏名、アーティスト名、タイトル、サムネイル、肖像等を使用する場合は、遅くとも納品物の納品時点で、納品物を{{ owner_name }}又は{{ owner_name }}が指定した第三者が何らの制限なく納品物を使用又は利用するために必要となる一切の権利処理を、{{ producer_name }}の責任と費用負担において完了していること</li>
                                                    <li>納品物及び納品物に関する知的財産権について、第三者（著作権等管理事業法に定める著作権等管理事業者を含む。）に対して、現に管理委託をしておらず、将来においても管理委託をしないこと</li>
                                                </ol>
                                            </li>
                                            <li>万一、納品物に関連して、第三者から知的財産権の侵害等に関連してクレーム、異議申立て、訴訟提起その他紛争（以下「紛争等」という。）が発生した場合、{{ producer_name }}は自己の責任と費用負担において一切の対処を行うものとし、{{ owner_name }}において何らかの対応をせざるを得ない場合には{{ owner_name }}に対して協力する義務を負う。</li>
                                            <li style="margin-bottom: -1mm !important;">紛争等に関連して{{ owner_name }}に損害（直接的損害に限らず、間接的損害、特別の事情による損害を含む。）が発生した場合は、{{ producer_name }}は当該損害について{{ owner_name }}に対し賠償する義務を負う。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    {% if semi_delegate %}
                                    <tr>
                                        <td class="text-11q-r">第9条	（対価）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>対価は第19条（対価支払に関する特記事項）第1項に基づき{{ producer_name }}に代わって運営会社が受領するものとし、{{ owner_name }}は、運営会社に対して、第6条の定めに基づき検収完了となった時点で対価金額を一括でクレジットカード決済又は銀行振込により支払う。クレジットカード決済の場合の決済手続は、検収完了時点で自動的に実施される。銀行振込の場合は、当該支払期限までに、運営会社の指定する銀行口座に振込送金する。振込手数料は{{ owner_name }}の負担とする。</li>
                                            <li>本契約の成立時以後は、{{ owner_name }}は本業務の委託をキャンセルし又は{{ owner_name }}の都合で本契約を解除すること若しくは{{ producer_name }}に対して返金を求めることができない。</li>
                                            <li>{{ producer_name }}は、いかなる理由であるかにかかわらず、対価金額の決済が正常に行われない場合は、本契約を解除できる。{{ producer_name }}は、この場合において{{ owner_name }}その他第三者に発生する損害について何らの責任を負わない。</li>
                                            <li>対価に関する支払いに関しては、本条のほか第19条（対価支払に関する特記事項）の定めに従う。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td class="text-11q-r">第9条	（対価）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}は、開発期間終了時点で対価金額を一括でクレジットカード決済又は銀行振込により支払う。クレジットカード決済の場合の決済手続は、開発期間終了時点で自動的に実施される。銀行振込の場合は、当該支払期限までに、運営会社の指定する銀行口座に振込送金する。振込手数料は{{ owner_name }}の負担とする。</li>
                                            <li>本契約の成立時以後は、{{ owner_name }}は本業務の委託をキャンセルし又は{{ owner_name }}の都合で本契約を解除すること若しくは{{ producer_name }}に対して返金を求めることができない。</li>
                                            <li>{{ producer_name }}は、いかなる理由であるかにかかわらず、対価金額の決済が正常に行われない場合は、本契約を解除できる。{{ producer_name }}は、この場合において{{ owner_name }}その他第三者に発生する損害について何らの責任を負わない。</li>
                                            <li>対価に関する支払いに関しては、本条のほか第19条（対価支払に関する特記事項）の定めに従う。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td class="text-11q-r">第10条	（反社会的勢力の排除）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}及び{{ producer_name }}は、次の各号に掲げる行為をしてはならない。
                                                <ol  style="margin-bottom: -5mm !important" type="a">
                                                    <li>暴力団（暴力団員による不当な行為の防止等に関する法律（平成３年法律第７７号）第２条第２号に規定する暴力団をいう。本条において以下同じ。）、暴力団員（同条第６号に規定する暴力団員をいう。）、暴力団関係団体、暴力団関係者、右翼団体、その他これらに準ずるもの（以下「反社会的勢力」と総称する。）と法令上の義務に基づかずに取引をし、又は取引関係を継続すること</li>
                                                    <li>反社会的勢力に利益を供与すること</li>
                                                    <li>反社会的勢力から利益を収受すること</li>
                                                    <li>反社会的勢力の威力を示すこと</li>
                                                </ol></li>
                                            <li>{{ owner_name }}及び{{ producer_name }}は、その役員又は従業員等が前項に掲げる行為をすることがないように努めなければならない。</li>
                                            <li>{{ owner_name }}及び{{ producer_name }}は、相手方が第１項に違反したときは、何らの催告を要することなく、直ちに本契約の解除をすることができる。</li>
                                        </ol></td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第11条	（解除）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}及び{{ producer_name }}は、相手方に以下の各号の一つに該当する事由が生じた場合、何らの通知・催告を要せず直ちに本契約の全部又は一部を解除することができる。
                                                <ol style="margin-bottom: -5mm" type="a">
                                                    <li>支払停止若しくは手形交換所における取引停止処分、又は破産手続開始、民事再生手続開始、会社更生手続開始、若しくは特別清算開始の申立があったとき</li>
                                                    <li>仮差押、仮処分、差押、強制執行、競売、滞納処分等の申立を受けたとき</li>
                                                    <li>解散の決定がなされた場合、又は解散命令が下された場合</li>
                                                    <li>相手方の行為が、互いの名誉を著しく傷つけ、又は傷つけるおそれのあるとき</li>
                                                    <li>公序良俗に反する行為があったとき</li>
                                                    <li>その信用を著しく失墜する事実が生じたとき</li>
                                                    <li>本契約に対する重大な義務違反があったとき</li>
                                                    <li>その代表者、責任者、実質的に経営権を有する者が、反社会的勢力である、又は、資金提供その他を通じて反社会的勢力の維持、運営、若しくは経営に協力若しくは関与する等反社会的勢力との何らかの交流若しくは関与を行っているとき</li>
                                                </ol></li>
                                            <li>第1項にかかわらず、{{ owner_name }}及び{{ producer_name }}は、相手方が本契約又はサービス利用規約の条項の一つに違反したときにおいて、催告後、14日間経過した時点において、当該違反の是正がなされない場合には、本契約を解除することができる。</li>
                                            <li>本条による解除権の行使は、 相手方に対する損害賠償の請求を妨げない。</li>
                                            <li>本契約の解除にかかる通知及び催告の方法は、本契約書別紙「取引方法」に定める方法によるものとする。</li>
                                        </ol></td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第12条	（権利義務譲渡）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}及び{{ producer_name }}は、事前に相手方から承諾を得なければ、本契約に基づく債権若しくは債務、又は本契約上の地位の全部若しくは一部について担保を設定し、又は譲渡その他の処分をすることはできない。当該承諾の方法は、本契約書別紙「取引方法」に定める方法によるものとする。</li>
                                            <li>前項にかかわらず、{{ producer_name }}は、本サービスにかかる事業を第三者に譲渡した場合、又は会社分割、合併等の組織再編があった場合（以下「事業譲渡等」という。）は、当該事業譲渡等に伴って本契約上の地位を当該第三者に譲渡することができるものとし、この場合について{{ owner_name }}は当該譲渡について予め同意する。</li>
                                        </ol></td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第13条	（適用関係）</td>
                                        <td class="text-9q-l">本書に定めのない事項は、サービス利用規約の各規定に従うものとし、当該各規定は、本書とともに本契約の一部を成すものとする。</td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第14条	（連絡・通知）</td>
                                        <td class="text-9q-l">本契約に関する連絡・通知は、本契約書別紙「取引方法」に定める方法によるものとする。</td>
                                    </tr>
                                    {% if semi_delegate %}
                                    <tr>
                                        <td class="text-11q-r">第15条	（存続条項）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>本契約が終了した場合であっても、第6条（業務の完了、納品及び担保責任）、第7条（権利帰属等）、第8条（保証）、第9条（対価）第2項、第10条（反社会的勢力の排除）、第11条（解除）第3項、第12条（権利義務譲渡）、第13条（適用関係）、第14条（連絡・通知先）、本条、第16条（準拠法）並びに第17条（合意管轄）の定めは有効に存続する。但し、個別に期限の定めがある場合は当該定めに従うものとする。</li>
                                            <li>前項の定めにかかわらず、第7条（権利帰属等）に基づく権利譲渡、利用許諾及び権利不行使の合意は、{{ owner_name }}の責めに帰すべき事由により本契約が解除された場合は、解除時点を以って効力を失う。</li>
                                        </ol></td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td class="text-11q-r">第15条	（存続条項）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>本契約が終了した場合であっても、第6条（納品及び担保責任）、第7条（権利帰属等）、第8条（保証）、第9条（対価）第2項、第10条（反社会的勢力の排除）、第11条（解除）第3項、第12条（権利義務譲渡）、第13条（適用関係）、第14条（連絡・通知先）、本条、第16条（準拠法）並びに第17条（合意管轄）の定めは有効に存続する。但し、個別に期限の定めがある場合は当該定めに従うものとする。</li>
                                            <li>前項の定めにかかわらず、第7条（権利帰属等）に基づく権利譲渡、利用許諾及び権利不行使の合意は、{{ owner_name }}の責めに帰すべき事由により本契約が解除された場合は、解除時点を以って効力を失う。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td class="text-11q-r">第16条	（準拠法）</td>
                                        <td class="text-9q-l">本契約の準拠法は日本法とする。</td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第17条	（合意管轄）</td>
                                        <td class="text-9q-l">本契約に関連して{{ owner_name }}と{{ producer_name }}の間に生じた紛争については、東京地方裁判所を第一審の専属的合意管轄裁判所とする。</td>
                                    </tr>
                                    <tr>
                                        <td class="text-11q-r">第18条	（成立）</td>
                                        <td class="text-9q-l">本契約は、本サービス上において運営会社所定の方法により{{ owner_name }}に対して提示される本契約書について、{{ owner_name }}が本サービス上において運営会社所定の方法により承諾を行った時点で有効に成立する。</td>
                                    </tr>
                                    <tr {% if not instance.allow_public_contract %}style="border-bottom: 0.3mm solid #53565A !important;"{% endif %}>
                                        <td class="text-11q-r">第19条	（対価支払に関する特記事項）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ producer_name }}が運営会社以外の者である場合、{{ owner_name }}は、対価について、{{ producer_name }}に代わって運営会社が受領することに同意する。{{ producer_name }}は、運営会社に対して、対価を受領する権限を付与するものとする。</li>
                                            <li>前項の場合、運営会社が、{{ owner_name }}から支払われる対価を、{{ producer_name }}に代わって受領した時点で、本契約に基づく{{ owner_name }}の対価の支払債務の履行が完了したものとする。</li>
                                            <li>{{ producer_name }}が運営会社以外の者である場合、{{ producer_name }}は、運営会社所定の手続に従いSOREMO上で再委託を行った場合、{{ producer_name }}が再委託先に支払うべき再委託にかかる対価（以下「再委託対価」という。）を運営会社が{{ producer_name }}に代わって再委託先に対して立替払いすることを委託する。当該運営会社による再委託先への再委託対価の支払時点で、再委託対価に関する債権は再委託先から運営会社に移転するものとし、{{ producer_name }}は、運営会社が、第1項に基づき運営会社が受領した対価から、当該再委託対価及び本サービスの利用規約に基づき発生する利用料がある場合は当該利用料を控除した残額を{{ producer_name }}に支払うことについて同意する。当該再委託対価の控除時点で、{{ producer_name }}の再委託先に対する再委託対価に関して運営会社が立替払いによって取得した債権に対する{{ producer_name }}の支払債務の履行が完了したものとする。</li>
                                            <li>本契約の他の定めにかかわらず、{{ owner_name }}は、本契約に基づく{{ owner_name }}と{{ producer_name }}の取引が下請代金支払遅延等防止法の適用対象となる場合においては、本書別表特記事項において定める支払期間（同法第２条の２第１項の定める期間内の期間であって、同法第２条の２第１項の定める期間内に運営会社から{{ producer_name }}に対して第3項に定める支払いを実施することが可能な期間とする）内に、運営会社に対して対価を支払わなければならない。</li>
                                        </ol></td>
                                    </tr>
                                    {% if instance.allow_public_contract %}
                                    <tr style="border-bottom: 0.3mm solid #53565A !important;">
                                        <td class="text-11q-r">第20条	（事例掲載）</td>
                                        <td class="text-9q-l has-ol"><ol style="margin-bottom: 0 !important; margin-top: 0 !important">
                                            <li>{{ owner_name }}は、{{ producer_name }}に対して、本契約書別紙「件名」記載の案件及び本契約に基づく成果物について、本サービスに関する事例又は自己の実績として、任意の方法と内容で本サービス、運営会社のウェブサイト、{{ producer_name }}の運営するウェブサイト若しくはSNS等その他任意の媒体で掲載することについて予め同意する。</li>
                                            <li>{{ owner_name }}は、{{ producer_name }}が、本業務を再委託したアーティスト（再々委託及びそれ以降の委託をしたアーティストも含む）に対して、前項と同様の条件及び内容で、プロジェクト及び成果物について掲載することを許諾することについて予め同意する。</li>
                                        </ol></td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                            <div class="text-9q-l text-break mt-8" style="page-break-before: always;">以上、本契約の成立を証するため、{{ owner_name }}と{{ producer_name }}はサービス利用規約所定の電磁的方法により手続を実施する。</div>
                            <p></p>
                            <div class="text-9q-l">締結日付　{% now "Y 年 n 月 j 日" %}</div>

                        </div>
                    </div>
                    <div class="export-wrap__content-wrap">
                        <div class="heading--20 text-16q-r mb-16">契約当事者</div>
                        <table class="table table-info">
                            <thead>
                                <tr class="text-9q-l">
                                    <th class="text-9q-l-special" width="50%">(オーナー)</th>
                                    <th class="text-9q-l-special" width="50%">(プロデューサー)</th>
                                </tr>
                            </thead>
                            <tbody class="text-9q-l">
                                <tr>
                                    <td class="text-9q-l">{% if owner_info.address %}{{ owner_info.address }}{% endif %}</td>
                                    <td class="text-9q-l">{% if producer_info.address %}{{ producer_info.address }}{% endif %}</td>
                                </tr>
                                <tr>
                                    <td class="text-9q-l">{% if owner_info.company_name %}{{ owner_info.company_name }}{% endif %}</td>
                                    <td class="text-9q-l">{% if producer_info.company_name %}{{ producer_info.company_name }}{% endif %}</td>
                                </tr>
                                <tr>
                                    <td class="text-9q-l">{% if owner_info.job_title %}{{ owner_info.job_title }}{% endif %}</td>
                                    <td class="text-9q-l">{% if producer_info.job_title %}{{ producer_info.job_title }}{% endif %}</td>
                                </tr>
                                <tr>
                                    <td class="text-9q-l">{% if owner_info.fullname %}{{ owner_info.fullname }}{% endif %}</td>
                                    <td class="text-9q-l">{% if producer_info.fullname %}{{ producer_info.fullname }}{% endif %}</td>
                                </tr>
                                <tr>
                                    <td class="text-7q-r signature">{% if owner_name and owner_approved %}<span>{{ owner_name }}</span>{% endif %}</td>
                                    <td class="text-7q-r signature"><span>{{ producer_name }}</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="export-wrap__content-wrap" style="page-break-inside: avoid;">
                        <div class="heading--20 text-16q-r mb-16">別表</div>
                        <table class="table table-upload-contract">
                            <thead>
                                <tr class="text-9q-r">
                                    <th width="30%"></th>
                                    <th width="70%"></th>
                                </tr>
                            </thead>
                            <tbody class="text-9q-l">
                                <tr>
                                    <td class="text-11q-r">案件名</td>
                                    <td class="text-9q-l">{{ subject }}</td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">内容</td>
                                    <td class="text-9q-l">
                                        <table class="table table-content no-border-inside">
                                            <thead>
                                                <tr class="text-9q-r">
                                                    <th width="8%"></th>
                                                    <th colspan="2" width="10%"></th>
                                                    <th width="82%"></th>
                                                </tr>
                                            </thead>
                                            <tbody class="text-9q-l">
                                                {% for item in work_content %}
                                                <tr>
                                                    <td width="8%">{{ item.work_type_dsp }}</td>
                                                    <td width="5%"><span>{{ item.quantity }}</span></td>
                                                    <td width="5%"><span class="text-7q-l">{{ item.unit_dsp }}</span></td>
                                                    <td width="82%" class="text-7q-l">{{ item.note }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">対価</td>
                                    <td class="text-9q-l">{{ total_money|display_currency }} 円（税込）</td>
                                </tr>
                                {% if not instance.semi_delegate %}
                                <tr>
                                    <td class="text-11q-r">納品物</td>
                                    <td class="text-9q-l">{{ instance.delivery_format|default_if_none:"" }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="text-11q-r">期間</td>
                                    <td class="text-9q-l">{{ start_schedule }} - {{ end_schedule }}</td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">予定納期</td>
                                    <td class="text-9q-l">{{ deadline }}</td>
                                </tr>
                                {% if note %}
                                <tr>
                                    <td class="text-11q-r">特記事項</td>
                                    <td class="text-9q-l">{{ note }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td class="text-11q-r">取引方法</td>
                                    <td class="text-9q-l">{{ instance.get_pick_up_method_display }}</td>
                                </tr>
                                <tr style="border-bottom: 0.3mm solid #53565A !important;">
                                    <td class="text-11q-r">引渡場所</td>
                                    <td class="text-9q-l">{{ instance.get_delivery_place_display }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>

</html>
