{% load util %}

<!DOCTYPE html>

<html lang="en">

<head>
    <meta content="text/html; charset=utf-8" charset="UTF-8">
    <title>契約書_{{ instance.contract_code }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Contract pdf */
        @font-face {
            font-family: 'Axis L';
            src: url('{{ axis_l_path }}') format('opentype');
            font-style: normal;
        }

        @font-face {
            font-family: 'Axis R';
            src: url('{{ axis_r_path }}') format('opentype');
            font-style: normal;
        }

        @font-face {
            font-family: 'Axis B';
            src: url('{{ axis_b_path }}') format('opentype');
            font-style: normal;
        }

        .font-R {
            font-family: 'Axis R';
        }
        
        .container {
            max-width: 970px;
            padding-top: 0px;
            padding-left: 100px;
            padding-right: 100px;
            padding-bottom: 100px;
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table thead {
            border-style: hidden !important;
            border: none;
        }

        table thead th:not(:nth-child(2)) {
            padding-left: 0 !important;
        }
        .table-business tbody tr,
        .table-upload-contract tbody tr:not(:first-child) {
            border-top: 0.3mm solid #53565A !important;
        }


        .table-business thead tr,
        .table-upload-contract thead tr{
            border: none !important;
        }

        .table-business thead tr td,
        .table-upload-contract thead tr td {
            border-style : hidden!important;
        }

        .border-bottom {
            border-bottom: 0.3mm solid #53565A !important;
        }

        .table-business tr.no-border,
        .table-upload-contract tr.no-border {
            border-top: none !important;
        }

        .table-content tbody tr:not(:last-child) td {
            border-top: none !important;
            border-bottom: 0.5mm solid #D3D3D3 !important;
        }

        .table-content tr td {
            border-top: none !important;
            border-bottom: none !important;
        }

        table:not(.table-info) tr td:nth-child(1) {
            padding-left: 0 !important;
            vertical-align: top;
        }

        .table-info tr th, .table-info tr td {
            padding: 2px;
        }

        .table-info tbody tr td {
            padding-left: 8px;
        }

        .table-content {
            margin-top: -32px !important;
            margin-bottom: -22px !important;
        }

        table tr td {
            vertical-align: top;
        }

        table tr td:nth-child(1) {
            padding-left: 0 !important;
            vertical-align: top;
        }

        table tr td:nth-child(2) {
            padding-right: 0 !important;
            word-break: break-word;
            white-space: pre-line;
        }

        .table-content tbody tr td:nth-child(2) {
            text-align: right;
        }

        .export-wrap__header {
            margin-bottom: 50px;
        }

        .export-wrap__header-logo img {
            width: 80px;
            height: auto;
        }

        .mt-8 {
            margin-top: 8px;
        }

        .mb-8 {
            margin-bottom: 8px;
        }

        .mb-16 {
            margin-bottom: 16px;
        }

        .export-wrap__content .bodytext--13 {
            margin-bottom: 16px;
        }

        .export-wrap__content-wrap {
            margin-bottom: 40px;
        }

        .export-wrap__content-wrap:nth-child(3) {
            page-break-before: always;
        }

        table th,
        table td {
            color: #000000;
            padding: 4px;
            line-height: 23px;
            word-break: break-word;
            text-align: left;
        }

        table td.signature {
            text-align: right;
        }

        .signature span {
            margin-top: 8px;
            padding: 14px;
            border: 0.75mm solid #F21D44;
            border-radius: 26px;
            color: #F21D44;
            font-family: 'Axis B';
            font-weight: 800;
            font-size: 2.5mm;
            margin-right: 25%;
        }

        table thead tr th {
            border: none;
            min-width: 100px;
            text-align: left;
        }

        table:not(.table-info, .table-business) tr th,
        table:not(.table-info) tr td {
            padding-top: 8px;
            padding-bottom: 8px;
        }

        table.table-business tr th,
        table.table-business tr td {
            padding-top: 20px;
            padding-bottom: 20px;
        }

        table.table-business tr td.has-ol {
            padding-top: 0px;
            padding-bottom: 0px;
        }

        .letter-spacing-300 {
            letter-spacing: 4.8px;
        }

        .letter-spacing-230 {
            letter-spacing: 3.68px;
        }

        .letter-spacing-200 {
            letter-spacing: 3.2px;
        }

        .bodytext--13 {
            line-height: 20px;
            color: #000000 !important;
            font-weight: 300;
        }

        .heading--18 {
            line-height: 27px;
            color: #000000;
            font-weight: 400;
        }

        .heading--20 {
            line-height: 30px;
            color: #000000;
            font-weight: 400;
            word-break: break-word;
            white-space: pre-line;
        }

        .text-break {
            word-break: break-word;
            white-space: pre-line;
        }

        .export-wrap__footer .bodytext--13 {
            padding-top: 8px;
            text-align: right;
        }

        .text-11q-r {
            font-size: 3.75mm;
            font-family: 'Axis R';
            font-weight: 500;
        }

        .text-13q-r {
            font-size: 4.25mm;
            font-family: 'Axis L';
            font-weight: 600;
        }

        .text-16q-r {
            font-size: 5.25mm;
            font-family: 'Axis R';
            font-weight: 500;
        }

        .text-9q-l {
            font-size: 3.25mm;
            font-family: 'Axis L';
            line-height: 19px;
        }

        .text-9q-l-special {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: 100;
        }

        .text-9q-r {
            font-size: 3.25mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-r {
            font-size: 2.75mm;
            font-family: 'Axis L';
            font-weight: bold;
        }

        .text-7q-l {
            font-size: 2.75mm;
            font-family: 'Axis L';
        }

        table ol {
            padding-left: 12px;
        }

        td > ol:first-child {
            margin-top: -16px;
       }

       td > ol:last-child {
            margin-bottom: -16px;
       }
       
        td > ol > li:last-child {
            margin: -6px 0 -7px 0;
        }

        td > ol > li {
            margin: -6px 0 -12px 0;
        }

        td > ol > li > ol > li {
            margin: -6px 0 -12px 0;
        }

        table ol > li > ol {
            margin-bottom: -18px;
            margin-top: -11px;
            margin-left: 8px;
        }
    
        .table-business td > ol > li:not(:last-child) {
            margin: -8px 0 -8px 0;
        }

        .table-business td > ol > li > ol > li:not(:last-child) {
            margin: -8px 0 -8px 0;
        }

        .table-business td > ol > li:first-child {
            margin: 2px 0 -8px 0;
        }

        .table-business td > ol > li > ol > li:first-child {
            margin: -16px 0 -8px 0;
        }

        .table-business td > ol > li:last-child {
            margin: -8px 0 0 0;
        }

        .table-business td > ol > li > ol > li:last-child {
            margin: -6px 0 -8px 0;
        }

        .table-business ol > li > ol {
            margin-bottom: 0;
            margin-top: 0;
            margin-left: 8px;
        }

        * {
            line-height: 19px;
            letter-spacing: 0.5px;
            overflow: visible !important;
        }
        
        table {
            page-break-inside: auto !important;
        }

        tr {
            page-break-inside: auto !important;
        }

        .table-container {
            width: 100%;
            flex-direction: row;
            display: -webkit-box;
            text-align: justify !important;
            text-rendering: geometricPrecision;
        }

        .table-container li,
        .table-container span {
            text-align: justify !important;
            text-rendering: geometricPrecision;
        }

        .table-container .table-column-right > ol {
            margin-left: 8px;
        }

        .table-container .table-column-right ol li:not(:last-child) {
            padding-bottom: 3px;
        }

        .table-container .table-column-right > ol > li > ol {
            padding-left: 20px;
            list-style-type: lower-alpha;
        }
        
        html, body { margin: 0; padding: 0; border: 0; font-size: 100%; font: inherit; vertical-align: baseline; }
        /* End Contract pdf */
    </style>
</head>

<body>
    <div>
        <main>
            <div class="container">
                <div class="export-wrap">
                    <div class="export-wrap__content ">
                        <div class="export-wrap__content-wrap">
                            <div class="heading--20 text-16q-r mb-16 letter-spacing-200">業務委託契約書</div>
                            <div class="text-9q-l text-break mb-16">本書契約当事者欄記載の委託者（以下「<span class="font-R">{{ producer_name }}</span>」という。）と本書契約当事者欄記載の受託者（以下「<span class="font-R">{{ artist_name }}</span>」という。）は、株式会社ソレモ（以下「運営会社」という。）が運営するウェブサイト及びアプリケーションソフトウェアである「SOREMO」（以下総称して「SOREMO」という。）を通じて<span class="font-R">{{ producer_name }}</span>が<span class="font-R">{{ artist_name }}</span>に対して業務を委託することについて以下の通り合意したので、契約（以下「本契約」という。）を締結する。</div>
                            
                            <!-- table -->
                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第1条（目的）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約の目的は、SOREMOを通じて{{ producer_name }}が顧客である{{ owner_name }}から受託した業務について、その一部である本契約書別表に定める業務を{{ artist_name }}に対し再委託し、{{ artist_name }}の業務に基づく成果物の権利帰属等について定めることにある。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第2条（委託業務の内容等）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ producer_name }}は、{{ artist_name }}に対し、本契約書別表に記載する{{ owner_name }}のプロジェクトに関して、本契約書別表に定める業務（以下「本業務」という。）を委託し、{{ artist_name }}はこれを受託する。</li>
                                        <li>本業務の具体的内容は本契約書別表に定めるとおりとし、{{ artist_name }}は当該具体的内容に基づき本業務を完成する。</li>
                                        <li>{{ artist_name }}が{{ producer_name }}に対し本業務の成果として納品すべき物（物に固定されない情報も含む。以下「納品物」という。）は、本契約書別表に定めるとおりとする。</li>
                                        <li>{{ artist_name }}は、{{ artist_name }}自身が単独で制作して単独で知的財産権（第7条第1項第2号に定義する。）を保有し、何らの制限なく利用許諾することができる制作物等のみを、又は{{ producer_name }}の承諾を得て第三者に再委託した場合は{{ artist_name }}若しくは当該第三者のみが知的財産権を保有し何らの制限なく利用許諾することができる制作物等のみを、本業務の過程において及び納品物に用いることができる。</li>
                                        <li>{{ artist_name }}は、{{ producer_name }}の事前の許諾なく、第三者の知的財産権の対象となる制作物等（前項に定める第三者の知的財産権の対象となる制作物等を含む）又は第三者による権利管理の対象となる制作物等を、特記事項に別途定めがある場合を除いて、本業務において使用してはならない。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第3条（業務の遂行及び資料提供）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、その専門的知見に基づいた高度の水準において善良な管理者の注意をもって本業務を行う。</li>
                                        <li>{{ producer_name }}は、{{ artist_name }}に対し、本業務を実施するために{{ producer_name }}が必要と判断する動画データその他の資料等の情報（以下「プロジェクト情報」という。）を提供する。</li>
                                        <li>{{ artist_name }}は、プロジェクト情報を、善良なる管理者の注意義務をもって管理するものとする。</li>
                                        <li>{{ artist_name }}は、本業務の遂行に必要な範囲に限り、{{ artist_name }}自身においてプロジェクト情報を複製、翻案その他利用することができる。</li>
                                        <li>{{ artist_name }}は、本業務が終了したとき、または{{ producer_name }}が要求したときに、プロジェクト情報を返還又は破棄するものとする。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第4条（再委託の禁止）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、本業務を第三者に再委託してはならない。</li>
                                        <li>前項の定めにかかわらず、本契約書別表において再委託できる内容について定めがある場合は、{{ artist_name }}は、{{ producer_name }}が認める内容及び範囲において、{{ artist_name }}の責任と費用負担において本業務の一部を第三者に再委託することができる。この場合、{{ artist_name }}は、当該第三者（以下「再委託先」という）との間の契約において、本契約に基づく{{ artist_name }}の義務と同等の義務を当該第三者に負わせるものとする。</li>
                                        <li>前項の場合、{{ artist_name }}は、本書別表の再委託できる内容の記載により{{ producer_name }}の承諾があることを理由として本契約上の自己の義務の免除又は軽減を主張することができないものとする。</li>
                                        <li>第2項の場合、{{ producer_name }}は、再委託先の行為を全て{{ artist_name }}の行為とみなし、{{ artist_name }}に対し、本契約上の責任を問うことができる。</li>
                                        <li>本契約書別表において再委託できる内容の定めがある場合は、再委託先から第三者に対する再々委託及びそれ以降の委託についても本条と同等の条件において行う委託については可能とするものとする。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第5条（報告義務）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">{{ artist_name }}は、{{ producer_name }}から本業務の進捗等について報告するよう要請があった場合は、速やかに報告する義務を負う。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第6条（納品及び担保責任）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、納品物を、本契約書別表に定める納期までに、同別表に定める納品方法により{{ producer_name }}に対して納品する義務を負う。</li>
                                        <li>{{ producer_name }}は、納品物の納品を受けた場合は、遅滞なく納品物について検査を行い、その検査結果を、SOREMO上の運営会社所定の方法により{{ artist_name }}に通知するものとする。</li>
                                        <li>前項に定める検査において、納品物の不具合（データは本契約書別表「納品物」に定める形式で提供されるものとし、当該型式外の形式でのデータの利用に関する不具合又は{{ producer_name }}の使用する機器若しくはソフトウェアに起因する不具合を除く。）又は納品物の内容について{{ producer_name }}が提供したプロジェクト情報その他の情報の重要な部分との齟齬その他本契約書別表に定める内容との不合致（納品物が通常有すべき品質を有しないことを含む。以下総称して「不適合」という。）があった場合、{{ producer_name }}は、{{ artist_name }}に対し具体的な不適合を示してSOREMO上で通知する。</li>
                                        <li>前項の場合、{{ producer_name }}は、{{ artist_name }}に対し、当該不適合の修正等の履行の追完（以下本条において「追完」という。）をその内容、方法及び期限を指定して請求することができ、{{ artist_name }}は当該追完を行う義務を負う。{{ artist_name }}は、当該追完にかかる費用の多寡にかかわらず、{{ producer_name }}が特別に認めた場合を除いて、当該追完義務を免れることはできない。この場合において、{{ artist_name }}は、{{ producer_name }}の指定する期限内に無償で納品物を修正して{{ producer_name }}に納入し、{{ producer_name }}は必要となる範囲で、第1項所定の検査を再度行うものとする。</li>
                                        <li>第2項の検査又は第4項に定める検査に合格した時点で、検収完了とする。</li>
                                        <li>検収完了となった後であっても、納品物に第2項に定める検査では通常の注意をもってしても発見し得ない重大なデータの不具合が発見された場合には、{{ producer_name }}は、{{ artist_name }}に対し、追完をその内容、方法及び期限を指定して請求することができ、この場合において第3項及び第4項の規定を準用する。</li>
                                        <li>{{ producer_name }}は、不適合の場合において、{{ artist_name }}の責めに帰すべき事由により損害を被った場合、{{ artist_name }}に対して損害賠償請求をすることができる。</li>
                                        <li>{{ producer_name }}の追完の請求にもかかわらず、{{ producer_name }}が指定した期間内に、当該追完がなされない場合又は追完の見込みがない場合は、{{ producer_name }}は、{{ artist_name }}の責に帰すべき事由の有無又は当該不適合の軽重にかかわらず、本契約の全部又は一部を解除することができる。</li>
                                        <li>{{ artist_name }}が本条に定める責任その他の担保責任を負うのは、{{ producer_name }}が不適合を知った時から１ヶ月以内に{{ producer_name }}がその旨を{{ artist_name }}にSOREMO上で通知した場合に限る。</li>
                                        <li>{{ artist_name }}は、本契約又はサービス利用規約に特段の定めの無い限り、本条に定める責任以外に納品物について損害賠償責任、追完責任その他何らの責任を負わない。{{ producer_name }}は、本条に定める場合以外に、損害賠償請求その他の請求又は契約解除を、不適合を理由として行うことはできないものとする</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第7条（保証）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、{{ producer_name }}に対し、以下の各号に定める事項を保証する。
                                            <ol>
                                                <li>{{ artist_name }}が単独で、又は{{ producer_name }}の承諾を得て第三者に再委託した場合は当該第三者のみとともに、納品物を制作したこと</li>
                                                <li>納品物に関する著作権その他著作権法上の一切の権利（著作権法第27条及び第28条に規定する権利、著作隣接権、二次使用料請求権、貸与に係る報酬請求権、放送のIPマルチキャスト技術による同時再送信に係る補償金請求権、私的録音録画補償金請求権を含む。）、商標権、意匠権、特許権、実用新案権（これらの権利を取得し又は登録出願する権利を含む。以下総称して「知的財産権」という。）その他一切の権利が、プロジェクト情報に関する知的財産権等従前から{{ producer_name }}、{{ owner_name }}その他第三者に帰属する権利を除いて、{{ artist_name }}に単独で原始的に帰属すること</li>
                                                <li>前号に定める知的財産権が第三者に帰属する場合又は納品物に第三者の氏名、アーティスト名、タイトル、サムネイル、肖像等を使用する場合は、遅くとも納品物の納品時点で、納品物を{{ producer_name }}、{{ owner_name }}又はこれらの者が指定した第三者が何らの制限なく納品物を使用又は利用するために必要となる一切の権利処理を、{{ artist_name }}の責任と費用負担において完了していること</li>
                                                <li>納品物及び納品物に関する知的財産権について、第三者（著作権等管理事業法に定める著作権等管理事業者を含む。）に対して、現に管理委託をしておらず、将来においても管理委託をしないこと</li>
                                            </ol>
                                        </li>
                                        <li>万一、納品物に関連して、第三者（{{ owner_name }}を含む。）から知的財産権の侵害等に関連してクレーム、異議申立て、訴訟提起その他紛争（以下「紛争等」という。）が発生した場合、{{ artist_name }}は自己の責任と費用負担において一切の対処を行うものとし、{{ producer_name }}において何らかの対応をせざるを得ない場合には{{ producer_name }}に対して協力する義務を負う。</li>
                                        <li>紛争等に関連して{{ producer_name }}に損害（直接的損害に限らず、間接的損害、特別の事情による損害を含む。）が発生した場合は、{{ artist_name }}は当該損害について{{ producer_name }}に対し賠償する義務を負う。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第8条（権利帰属等）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、{{ producer_name }}に対し、納品物に関する知的財産権を、その発生と同時に譲渡する。又は、{{ artist_name }}は、当該知的財産権がその役職員又は再委託先その他第三者（以下「第三者等」という。）に帰属する場合は、遅くとも納品物の納品時までに、納品物を{{ producer_name }}、{{ owner_name }}又はこれらの者（以下「オーナーら」という。）が指定した第三者が何らの制限なく納品物を使用又は利用するために必要となる範囲において、当該第三者等から当該知的財産権を自己の責任と費用負担であらかじめ取得する義務を負う。また、{{ artist_name }}は、納品物に関する著作者人格権、実演家人格権、肖像権又はパブリシティ権を、オーナーらに対して行使せず、第三者等をして当該権利をオーナーらに対して行使させないものとする。</li>
                                        <li>プロジェクト情報に関する知的財産権は、{{ producer_name }}、{{ owner_name }}又は{{ producer_name }}に対して利用許諾した第三者に帰属する。{{ producer_name }}はプロジェクト情報に関して、{{ artist_name }}に対し、本業務の遂行に必要な範囲で利用許諾する（第三者に対する再許諾を含む。）。</li>
                                        <li>本条の定めにかかわらず、本書別表特記事項に別段の定めがある場合は当該定めが優先する。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第9条（肖像等の使用）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">{{ artist_name }}は、{{ producer_name }}に対し、{{ artist_name }}がSOREMO上で登録した{{ artist_name }}のアーティスト名、タイトル、サムネイル、肖像等の情報（以下「肖像等」という。）及び納品物に関する著作者名等のクレジット表示（以下「クレジット表示」という。）を、本契約書別表記載の「件名」記載の案件に関連する目的で、あらゆる方法及び対応において、使用すること及び{{ producer_name }} が{{ owner_name }}にその使用（{{ owner_name }}がその指定する者に使用させることも含む）を許諾することを、無償で、期間の制限なく許諾する。但し、肖像等又はクレジット表示について、{{ artist_name }}に特段の希望ある場合は、{{ producer_name }}は当該希望を尊重し、{{ owner_name }}と協議の上、当該希望に沿うよう努める。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第10条（著作者人格権等）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、{{ producer_name }}、{{ owner_name }}及びこれらの者の許諾する第三者に対して、納品物、肖像等及びクレジット表示に関する著作者人格権、実演家人格権、肖像権、プライバシー権及びパブリシティ権を行使しない。</li>
                                        <li>{{ producer_name }}は、納品物に関するクレジット表示の有無、その内容・方法について、{{ producer_name }}の裁量において決定することができる。</li>
                                        <li>前２項にかかわらず、{{ producer_name }}は、クレジット表示に関しては、{{ artist_name }}のブランディングにとってクレジット表示が重要であることに鑑み、{{ artist_name }}の価値を高めるために、{{ artist_name }}本人の意思を尊重するよう努める。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第11条（対価及び費用）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ producer_name }}は、{{ artist_name }}に対し、本業務の実施及び第8条（権利帰属等）ないし第10条（著作者人格権等）に定める権利譲渡、許諾及び権利不行使の対価として、本契約書別表に定める対価を支払う義務を負う。</li>
                                        <li>本業務の実施に関して発生する一切の費用は、特段の合意のない限り、{{ artist_name }}が負担する。</li>
                                        <li>{{ artist_name }}は、{{ producer_name }}に対し本契約に明示的に定める対価以外のいかなる金銭的請求も行わない。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第12条（支払い）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ producer_name }}は、{{ artist_name }}に対し、対価を以下の各号に従い、運営会社に各支払業務を委託して、支払う。振込手数料が発生する場合は、{{ producer_name }}の負担とする。
                                            <ol>
                                                <li>検収完了後、SOREMO上で運営会社所定の手続に従い{{ artist_name }} が振込申請を行った場合、運営会社は、{{ artist_name }}が登録した銀行口座に対し支払明細書記載の振込期日までに振込送金する方法で支払う。但し、本契約に基づく委託者と受託者の取引が下請代金支払遅延等防止法の適用対象となる場合においては、本書別表特記事項において定める支払期間（同法第２条の２第１項の定める期間内の期間とする）内に、受託者は振込申請を行うものとする。</li>
                                            </ol>
                                        </li>
                                        <li>{{ artist_name }}は、SOREMO上で運営会社所定の入金口座情報を登録する義務を負う。登録された支払情報に誤り等がある場合、{{ artist_name }}はサービス利用規約及び本契約に基づく支払いを受けることができない場合があり、この場合に{{ artist_name }}に発生した損害等について{{ producer_name }}は一切の責任を負わない。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第13条（秘密保持）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}は、SOREMO又は本契約に関連して知り得た{{ producer_name }}又は{{ owner_name }}の営業上、技術上又は人事上の一切の情報（以下「秘密情報」という。）を、自らが保有し同程度の機密性を有する情報を保護するのと同程度の注意義務（但し、いかなる場合も善良な管理者の注意義務を下回らない。）をもって、取り扱わなければならず、秘密情報の漏えい防止のため必要かつ適切な措置を講じなければならない。</li>
                                        <li>{{ artist_name }}は、本契約に関する処理のために客観的かつ合理的に必要な範囲を超えて、秘密情報を使用、複製又は翻訳等してはならない。</li>
                                        <li>{{ artist_name }}は、{{ producer_name }}事前の同意をSOREMO上で得ずに、秘密情報を本契約に関する処理のために必要な最小限度の自己の役員又は従業員以外の者に開示してはならない。</li>
                                        <li>{{ artist_name }}は、司法機関又は行政機関等から秘密情報の開示を求められた場合は、速やかに、開示の求めがあった事実を{{ producer_name }}に通知しなければならず、開示の範囲について{{ producer_name }}と事前協議を行い、法令により開示する義務を負う範囲に限り、秘密情報を開示することができる。この場合、{{ artist_name }}は、開示する秘密情報について開示先で秘密として取り扱われるよう最善を尽くし、{{ producer_name }}が開示の求めに対し法的に救済を求めるときは、合理的範囲内で相手方に協力しなければならない。</li>
                                        <li>{{ artist_name }}は、{{ producer_name }}からその旨の要求があった場合又は本契約が終了した場合には、{{ producer_name }}が指定する期間内に自己が保有する{{ producer_name }} 又は{{ owner_name }}の秘密情報の使用を停止しなければならない。この場合、{{ artist_name }}は、{{ producer_name }}の選択及び指示に従い、遅滞なく、秘密情報及びその複製物のすべてを返還若しくは廃棄し、又は秘密情報を消去しなければならず、{{ producer_name }}から要求のあった場合には、速やかに、かかる廃棄及び消去を確認する書面（電子データを含む）をSOREMO上で提出しなければならない。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第14条（個人情報）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>前条の規定は、{{ producer_name }}が{{ artist_name }}に個人情報（個人情報の保護に関する法律第2条第1項の定義に従う。）を開示する場合の当該個人情報の取扱いについて準用する。</li>
                                        <li>{{ artist_name }}は、個人情報の取扱いにつき、法令、行政機関が定める個人情報保護に関するガイドライン等を遵守しなければならない。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第15条（反社会的勢力の排除）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}及び{{ producer_name }}は、次の各号に掲げる行為をしてはならない。
                                            <ol>
                                                <li>暴力団（暴力団員による不当な行為の防止等に関する法律（平成３年法律第７７号）第２条第２号に規定する暴力団をいう。本条において以下同じ。）、暴力団員（同条第６号に規定する暴力団員をいう。）、暴力団関係団体、暴力団関係者、右翼団体、その他これらに準ずるもの（以下「反社会的勢力」と総称する。）と法令上の義務に基づかずに取引をし、又は取引関係を継続すること</li>
                                                <li>反社会的勢力に利益を供与すること</li>
                                                <li>反社会的勢力から利益を収受すること</li>
                                                <li>反社会的勢力の威力を示すこと</li>
                                                <li>{{ artist_name }}及び{{ producer_name }}は、その役員又は従業員等が前項に掲げる行為をすることがないように努めなければならない。</li>
                                                <li>{{ artist_name }}及び{{ producer_name }}は、相手方が第１項に違反したときは、何らの催告を要することなく、直ちに本契約の解除をすることができる。</li>
                                            </ol>
                                        </li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第16条（解除）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}及び{{ producer_name }}は、相手方に以下の各号の一つに該当する事由が生じた場合、何らの通知・催告を要せず直ちに本契約の全部又は一部を解除することができる。但し、{{ artist_name }}は、{{ producer_name }}が第11条第1項に定める報酬を支払った後は、第8条ないし第10条に関する合意を解除することはできない。
                                            <ol>
                                                <li>支払停止若しくは手形交換所における取引停止処分、又は破産手続開始、民事再生手続開始、会社更生手続開始、若しくは特別清算開始の申立があったとき</li>
                                                <li>仮差押、仮処分、差押、強制執行、競売、滞納処分等の申立を受けたとき</li>
                                                <li>解散の決定がなされた場合、又は解散命令が下された場合</li>
                                                <li>相手方の行為が、互いの名誉を著しく傷つけ、又は傷つけるおそれのあるとき</li>
                                                <li>公序良俗に反する行為があったとき</li>
                                                <li>その信用を著しく失墜する事実が生じたとき</li>
                                                <li>本契約に対する重大な義務違反があったとき</li>
                                                <li>その代表者、責任者、実質的に経営権を有する者が、反社会的勢力である、又は、資金提供その他を通じて反社会的勢力の維持、運営、若しくは経営に協力若しくは関与する等反社会的勢力との何らかの交流若しくは関与を行っているとき</li>
                                            </ol>
                                        </li>
                                        <li>第1項にかかわらず、{{ artist_name }}及び{{ producer_name }}は、相手方が本契約又はサービス利用規約の条項の一つに違反したときにおいて、SOREMO上の運営会社所定の連絡手段による催告後、14日間経過した時点において、当該違反の是正がなされない場合には、本契約を解除することができる。</li>
                                        <li>本条による解除権の行使は、 相手方に対する損害賠償の請求を妨げない。</li>
                                        <li>本契約の解除にかかる通知及び催告の方法は、本契約書別紙「取引方法」に定める方法によるものとする。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第17条（権利義務譲渡）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ artist_name }}及び{{ producer_name }}は、事前に相手方から書面による承諾を得なければ、本契約に基づく債権若しくは債務、又は本契約上の地位の全部若しくは一部について担保を設定し、又は譲渡その他の処分をすることはできない。当該承諾の方法は、本契約書別紙「取引方法」に定める方法によるものとする。</li>
                                        <li>前項にかかわらず、{{ producer_name }}は、SOREMOにかかる事業を第三者に譲渡した場合、又は会社分割、合併等の組織再編があった場合（以下「事業譲渡等」という。）は、当該事業譲渡等に伴って本契約上の地位を当該第三者に譲渡することができるものとし、この場合について{{ artist_name }}は当該譲渡について予め同意する。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第18条（適用関係）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本書に定めのない事項は、サービス利用規約第1章（総則）及び第3章（{{ artist_name }}による利用条件等）の各規定に従うものとし、当該各規定は、本書とともに本契約の一部を成すものとする。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第19条（連絡・通知）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約に関する連絡・通知は、別段の定めのない限り、本契約書別紙「取引方法」に定める方法によるものとする。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第20条（準拠法）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約の準拠法は日本法とする。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第21条（合意管轄）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約に関連して{{ artist_name }}と{{ producer_name }}の間に生じた紛争については、東京地方裁判所を第一審の専属的合意管轄裁判所とする。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第22条（成立）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約は、SOREMO上において運営会社所定の方法により{{ artist_name }}に対して提示される本契約書について、{{ artist_name }}がSOREMO上において運営会社所定の方法により承諾を行った時点で有効に成立する。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第23条（存続条項）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <div style="padding: 20px 0;"><span class="">本契約が終了した場合であっても、第6条（納品及び担保責任）、第7条（保証）、第8条（権利帰属等）、第9条（肖像等の使用）、第10条（著作者人格権等）、第13条（秘密保持）、第14条（個人情報）、第15条（反社会的勢力の排除）、第16条（解除）第3項、第17条（権利義務譲渡）、第18条（適用関係）、第19条（連絡・通知先）、第20条（準拠法）、第21条（合意管轄）及び本条の定めは有効に存続する。但し、個別に期限の定めがある場合は当該定めに従うものとする。</span></div>
                                </div>
                            </div>

                            <div class="table-container" style="display: flex; border-top: 1px solid #53565a; border-bottom: 1px solid #53565a;">
                                <div class="table-column-left" style="width: 30%; padding: 20px 0;">
                                    <span class="text-11q-r">第24条（対価支払に関する特記事項）</span>
                                </div>
                                <div class="table-column-right text-9q-l" style="width: 70%;">
                                    <ol style="padding: 20px 0 20px 13px;">
                                        <li>{{ producer_name }} が運営会社以外の者である場合、{{ producer_name }}は、運営会社に対して、{{ artist_name }}に支払うべき対価を立て替え払いすることを委託する。{{ artist_name }}は、運営会社が{{ producer_name }}に代わって対価を弁済することについて予め承諾する。</li>
                                        <li>前項に基づき運営会社が対価を{{ artist_name }}に対して支払った時点で、当該対価に関する債権は{{ artist_name }}から運営会社に移転する。</li>
                                        <li>{{ artist_name }}が、運営会社所定の手続に従いSOREMO上で再委託を行った場合、{{ artist_name }}は、{{ artist_name }}が再委託先に支払うべき再委託にかかる対価（以下「再委託対価」という。）を運営会社が{{ artist_name }}に代わって立替払いすることを委託する。当該運営会社による再委託先への再委託対価の支払時点で、再委託対価に関する債権は再委託先から運営会社に移転するものとし、{{ artist_name }}は、{{ artist_name }}が本契約に基づき受け取るべき対価（第12条及び本条第1項に基づき運営会社が{{ producer_name }}に代わって支払う対価）について、運営会社が、当該再委託対価を控除した上でその残額を{{ artist_name }}に支払うことについて同意する。当該再委託対価の控除時点で、{{ artist_name }}の再委託先に対する再委託対価に関して運営会社が立替払いによって取得した債権に対する{{ artist_name }}の支払債務の履行が完了したものとする。</li>
                                    </ol>
                                </div>
                            </div>

                            <div class="text-9q-l text-break mt-8" style="page-break-before: always;">以上、本契約の成立を証するため、{{ artist_name }}と{{ producer_name }}はサービス利用規約所定の電磁的方法により手続を実施する。</div>
                            <p></p>
                            <div class="text-9q-l">締結日付　{{ offer.contract_time|date:"Y 年 n 月 j 日" }}</div>
                        </div>
                    </div>
                    <div class="export-wrap__content-wrap">
                        <div class="heading--20 text-16q-r mb-16">契約当事者</div>
                        <table class="table table-info">
                            <thead>
                                <tr class="text-9q-l">
                                    <th class="text-9q-l-special" width="50%">[委託者の名称/氏名]</th>
                                    <th class="text-9q-l-special" width="50%">[受託者の名称/氏名]</th>
                                </tr>
                            </thead>
                            <tbody class="text-9q-l">
                                <tr class="text-9q-l">
                                    <td class="text-9q-l" width="50%">{{ producer.enterprise }}</td>
                                    <td class="text-9q-l" width="50%">{{ artist.enterprise }}</td>
                                </tr>

                                <tr class="text-9q-l">
                                    <td class="text-9q-l" width="50%">{{ producer.position }}</td>
                                    <td class="text-9q-l" width="50%">{{ artist.position }}</td>
                                </tr>

                                <tr class="text-9q-l">
                                    <td class="text-9q-l" width="50%">{{ producer.get_display_name }}</td>
                                    <td class="text-9q-l" width="50%">{{ artist.get_display_name }}</td>
                                </tr>

                                <tr>
                                    <td class="text-7q-r signature"><span>{{ producer_name }}</span></td>
                                    <td class="text-7q-r signature">{% if artist_name and approved %}<span>{{ artist_name }}</span>{% endif %}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="export-wrap__content-wrap" style="page-break-inside: avoid;">
                        <div class="heading--20 text-16q-r mb-16">別表</div>
                        <table class="table table-upload-contract">
                            <colgroup>
                                <col span="1" style="width: 30%;">
                                <col span="1" style="width: 70%;">
                             </colgroup>
                            <tbody class="text-9q-l">
                                <tr style="border-top: 1px solid #53565a;">
                                    <td class="text-11q-r">プロジェクト情報</td>
                                    <td class="text-9q-l">
                                        <table class="table table-content no-border-inside">
                                            <thead>
                                                <tr class="text-9q-r">
                                                    <th width="15%"></th>
                                                    <th width="85%"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td  class="text-9q-r" width="25%">目的物：</td>
                                                    <td  class="text-9q-l" width="75%">{{ project.get_name_by_contract }}</td>
                                                </tr>
                                                <tr>
                                                    <td  class="text-9q-r" width="25%">エンドクライアント：</td>
                                                    <td  class="text-9q-l" width="75%">{{ owner_name }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="text-11q-r">ロール</td>
                                    <td class="text-9q-l">{{ offer.get_contract_display }}</td>
                                </tr>

                                <tr>
                                    <td class="text-11q-r">再委託の可否</td>
                                    <td class="text-9q-l">{% if offer.allow_subcontracting %}可{% else %}不可{% endif%}</td>
                                </tr>

                                <tr>
                                    <td class="text-11q-r">対価</td>
                                    <td class="text-9q-l">{{ offer.reward|display_currency }} 円（税込）</td>
                                </tr>
                                
                                <tr>
                                    <td class="text-11q-r">納品物</td>
                                    <td class="text-9q-l">
                                        <div style="display:flex; flex-direction: column; margin-top: -40px; margin-bottom: -20px;">
                                            <div class="table-container" style="display: flex; padding: 10px 0 !important; margin-top: -24px; margin-bottom: -42px;">
                                                <div class="table-column-left" style="width: 15%; padding: 0 !important; display: flex;">
                                                    <span class="text-9q-r">シーン：</span>
                                                </div>
                                                <div class="table-column-right text-9q-l" style="width: 85%; padding: 0 !important; display: flex;  text-align: right">
                                                    <div style="">{% if offer.scenes %}<span class="">{{ offer.scenes }}</span>{% else %}<span style='color: transparent'>test</span>{% endif %}</div>
                                                </div>
                                            </div>
                                            <div class="table-container" style="display: flex; border-top: 1px solid #d3d3d3; padding: 10px 0 !important; margin-bottom: -40px;">
                                                <div class="table-column-left" style="width: 15%; padding: 0 !important; display: flex; margin-top: -22px;">
                                                    <span class="text-9q-r">説明：</span>
                                                </div>
                                                <div class="table-column-right text-9q-l" style="width: 85%; padding: 0 !important; display: flex; margin-top: -22px;">
                                                    <div style="">{% if offer.message %}<span class="">{{ offer.message }}</span>{% else %}<span style='color: transparent'>test</span>{% endif %}</div>
                                                </div>
                                            </div>
                                            <div class="table-container" style="display: flex; border-top: 1px solid #D3D3D3; padding: 10px 0 !important; margin-bottom: -27px;">
                                                <div class="table-column-left" style="width: 15%; padding: 0 !important; display: flex; margin-top: -22px;">
                                                    <span class="text-9q-r">形式：</span>
                                                </div>
                                                <div class="table-column-right text-9q-l" style="width: 85%; padding: 0 !important; display: flex; margin-top: -22px;  text-align: right">
                                                    <span class="">{% if offer.data_format %}{{ offer.data_format }}{% endif %}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">期間</td>
                                    <td class="text-9q-l">{{ start_time }} - {{ end_time }}</td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">納期</td>
                                    <td class="text-9q-l">{{ deadline }}</td>
                                </tr>

                                <tr>
                                    <td class="text-11q-r">取引方法</td>
                                    <td class="text-9q-l">{{ offer.pick_up_method }}</td>
                                </tr>
                                <tr>
                                    <td class="text-11q-r">納品物の引渡場所</td>
                                    <td class="text-9q-l">{{ offer.delivery_place }}</td>
                                </tr>

                                <tr style="border-bottom: 0.3mm solid #53565A !important;">
                                    <td class="text-11q-r">特記事項</td>
                                    {% if offer.note %}
                                    <td class="text-9q-l">{{ offer.note }}</td>
                                    {% else %}
                                    <td class="text-9q-l"></td>
                                    {% endif %}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>

</html>
