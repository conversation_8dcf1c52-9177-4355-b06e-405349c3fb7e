
/* .pbanner>div:last-child {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
} */

.pbanner__top {
    display: flex;
    position: absolute;
    right: 0;
    bottom: -46px;
    /*z-index: 3;*/
}

.pbanner__top .bdropdown {
    margin-left: 24px;
    margin-top: 24px;
}

@media (max-width: 992px) {
    .pbanner__top .bdropdown {
        margin-top: 8px;
        margin-right: 15px;
    }
}

.pbanner__top .bdropdown .icon {
    color: #fff;
    font-size: 24px;
}

.pbanner__top .bproject-setting .icon {
    color: #A7A8A9;
    font-size: 24px;
}

.pbanner__top .bdropdown .bdropdown-menu {
    margin-left: 0;
    left: 0 !important;
    /* stylelint-disable-line */
    right: auto !important;
    /* stylelint-disable-line */
}

.pbanner__members {
    margin-left: auto;
    overflow: hidden;
}

.pbanner__user-list {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-end;
    /* margin-right: 24px; */
    margin-left: auto;
    /* margin-top: 24px; */
}

/* @media (max-width: 992px) {
    .pbanner__user-list {
        margin-right: 16px;
        margin-top: 12px;
    }
}

@media (max-width: 992px) {
    .pbanner__user-list:last-child {
        margin-top: 4px;
    }
} */



.pbanner__user {
    display: inline-flex;
}

.pbanner__user:not(:first-child) {
    margin-left: -8px;
}

.pbanner__user .avatar {
    border: 2px solid #fff;
}

.pbanner__more-link {
    margin-left: -20px;
    font-size: 27px;
    color: #fff;
    line-height: 1;
}

.pbanner__more-link:hover {
    color: #fff;
}

.pbanner__bottom {
    position: absolute;
    right: 0;
    bottom: -64px;
    display: flex;
    align-items: flex-end;
    /* padding: 0 24px; */
    width: 71%;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

.icon-container {
    display: flex;
    justify-content: center;
    width: 100%;
    height: auto;
    position: absolute;
    bottom: -56px;
    padding-bottom: 8px;
    background: transparent;
    min-height: 40px;
}

.pbanner__icon-expand-container, .pbanner__icon-collapse-container {
    width: 24px;
    height: 24px;
    border-radius: 100%;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #a7a8a9;
    z-index: 3;
    cursor: pointer;
}

.pbanner__icon-expand-container {
    display: none;
}

.show-button-expand {
    display: flex !important;
}

.account_upload-file #uploadFile1 {
    padding: 24px;
    cursor: pointer;
    background: #fff;
    border: 1px dashed #D3D3D3;
    border-radius: 6px;
    text-align: center;
    min-height: 53px;
    margin-top: 8px;
}

.pbanner__icon-expand-container:hover, .pbanner__icon-collapse-container:hover {
    background: #009ACE;
    color: white;
}

.icon-toggle{
   font-size: 20px;
}

.pbanner__detail-expand {
    position: absolute;
    top: calc(100% + 12px);
    border-radius: 6px 6px 6px 6px;
    padding: 8px 0px 8px 0px;
    width: 100%;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid #F0F0F0;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    z-index: 100;
    opacity: 0;
    transform: translateY(-50%);
    transform: scaleY(0);
    animation: slideout .25s forwards;
    cursor: default;

    font-weight: normal;
}

.hide-button {
    opacity: 0;
    pointer-events: none;
}

@keyframes slidein {
    from {
    opacity: 0;
    transform: translateY(-50%) scaleY(0);
    }

    to {
    opacity: 1;
    transform: translateY(0%) scaleY(1);
    }
  }
@keyframes slideout {
    from {
      transform: translateY(0%) scaleY(1) ;
      opacity: 1;
    }

    to {
    opacity: 0;
    transform: translateY(-50%) scaleY(0);
    }
  }

@keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(180deg);
    }
  }

  @keyframes rotate-1 {
    from {
      transform: rotate(180deg);
    }

    to {
      transform: rotate(0deg);
    }
  }

.pbanner__detail-expand-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 16px 0px 16px;

    @media (width <= 640) {
        flex-direction: column;
        justify-content: flex-start;
        }
}

.detail-expand-lef {
    display: flex;
}

.detail-code-name, .detail-time-range  {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    border-radius: 4px;
}

.detail-code-name {
    background: #A7A8A9;
    color: #FFFFFF;
    padding: 2px 8px;
    cursor: pointer;
}

.detail-time-range {
    color: #a7a8a9;
    /* margin: 0px 24px; */
    cursor: pointer;
}

.detail-expand-right .sproject-meta {
    margin: 0px !important;
}

.pbanner__detail-expand-action {
    width: 100%;
    display: flex;
    justify-content: center;
    height: auto;
    padding-top: 12px;
}

.pbanner__detail-description, .pbanner__detail-expand-bottom {
    width: 100%;
    height: auto;
    padding: 0px 16px 0px 16px;
    position: relative;
}

.description-content{
    width: 100%;
    height: auto;
    cursor: pointer;
    position: relative;
}

.description-content__content {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-feature-settings: 'clig' off, 'liga' off;

    text-align: justify;
    leading-trim: both;
    text-edge: cap;

    font-size: 13px;
    line-height: 200%;
    color: #000000;

    width: min(100%, 640px);
    height: auto;
    white-space: pre-line;
    overflow-x: auto;
}

.dropzone {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border: 1px dashed #D3D3D3 !important;
    box-sizing: border-box;
    border-radius: 6px;
}

.description-edit-pc, .description-edit-sp, .code-name-edit-pc, .code-name-edit-sp, .credit-edit, .time-range-edit, .owner-company-name-edit {
    background: #A7A8A9;
    width: 24px;
    height: 24px;
    background-color: rgba(167, 168, 169, 0.2);
    border-radius: 50%;
    color: #a7a8a9;
    /* display: none; */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.description-edit-pc, .code-name-edit-pc, .credit-edit, .time-range-edit, .owner-company-name-edit {
    display: none;
}

.credit-edit {
    position: absolute;
    top: 100%;
    left: 0;
}

.owner-company-name-edit{
    position: absolute;
    bottom: 8px;
    left: 32px;
}

.description-edit-pc:hover, .description-edit-sp:hover, .code-name-edit-pc:hover, .code-name-edit-sp:hover, .credit-edit:hover, .time-range-edit:hover, .owner-company-name-edit:hover {
    background: #009ace;
    color: white;
}

.show-btn-edit {
    display: inline-flex !important;
}

.detail-time-range-container{
    position: relative;
}

.code-name-edit-pc, .description-edit-pc {
    position: absolute;
    top: 100%;
    left: 0;
}

.time-range-edit{
    position: absolute;
    top: 100%;
    left: 24px;
}

.owner-company-name-input-before{
    position: absolute;
    top: 33%;
    left: 10px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    color: #000000;
}

.daterangepicker{
    z-index: 9999;
}

.owner-company-name-input-before::after{
    content: '|';
    padding-left: 8px;
    color: #A7A8A9;
    height: 18px;
    font-size: 15px;
}

.code-name-edit-sp, .description-edit-sp {
    display: none;
}

.detail-expand-left {
    position: relative;
    display: flex;

    width: 100%;
    height: 100%;
}

.none-description {
    color: #A7A8A9 !important;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
}

.pbanner__detail-owner-name {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    color: #a7a8a9;
    cursor: pointer;
    padding-top: 24px;
}

.pbanner__detail-show-credit {
    position: absolute;
    width: auto;
    height: auto;
    right: 16px;
    bottom: 32px;
}

.show-credit-btn {
    /* font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 16px;
    line-height: 150%; */
    text-align: center;
}

.credit-button-action {
    display: flex;
    flex-direction: column;
    position: relative;
}

.show-btn-edit {
    display: inline-flex !important;
}

.description-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #F0F0F0;
    border-radius: 4px !important;
    color: #000000;
    font-size: 13px;
}

.description-input:focus {
    border: 1px solid var(--soremo-placeholder);
    outline: none !important;
}

@media (max-width: 992px) {
    .pbanner__icon-expand-container {
        display: flex;
    }

    /* .pbanner__detail-expand-top {
        display: block;
    } */

    /* .detail-time-range {
        margin: 0px 10px;
    }

    .detail-expand-right {
        margin-top: 12px;
    } */

    /* .pbanner__detail-expand-top, .pbanner__detail-description, .pbanner__detail-expand-bottom {
        padding: 0px 16px;
    } */

    /* .pbanner__detail-show-credit{
        position: unset;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        padding: 10px;
    } */

    .code-name-edit-sp, .description-edit-sp, .time-range-edit, .owner-company-name-edit {
        display: inline-flex;
    }

    .code-name-edit-pc {
        display: none;
    }

    .time-range-edit, .owner-company-name-edit{
        left: 10px;
    }

    .detail-expand-right .sproject-meta {
        justify-content: flex-start;
    }

    .detail-expand-right .sproject-meta .sproject-banner-rating {
        margin-left: auto !important;
    }

    .description-edit, .credit-edit {
        display: inline-flex;
        position: unset;
    }

    /* .detail-expand-left {
       width: 100%;
   } */
}

/* @media (max-width: 992px) {
    .pbanner__bottom {
        padding: 0 15px;
        bottom: 16px;
    }
} */

.pbanner__user-btn {
    font-size: 24px;
    color: #a7a8a9;
    height: 24px;
    line-height: 1;
    margin:0 2px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 50%;

    transition: 0.2s;
}

.pbanner__user-btn:hover {
    background-color: #009ace;
    color: #FFFFFF;
    scale: 1.025;
}

.button-show-staff .icon--sicon-union:hover {
    color: #009ace;
}

.pbanner__number {
    color: #fff;
    margin-left: auto;
    line-height: 1;
}

.pbanner__progress {
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 100%;
    z-index: 2;
}

.pbanner__progress .p-tooltip {
    background-color: #000;
    border-radius: 3px;
    color: #fff;
    padding: 8px 16px;
    height: 33px;
    font-size: 14px;
    position: absolute;
    right: 0;
    top: calc(100% + 18px);
    transform: translateX(50%);
    opacity: 0;
    visibility: hidden;
    transition: all .3s;
    display: flex;
    align-items: center;
    line-height: 1;
}

@media (max-width: 992px) {
    .pbanner__progress .p-tooltip {
        display: none;
    }
}

.pbanner__progress .p-tooltip:not(.p-tooltip_two):after {
    width: 0;
    height: 0;
    position: absolute;
    top: -9px;
    left: 50%;
    content: '';
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-bottom: 9px solid #000;
    transform: translate(-50%, 0);
}

.pbanner__progress .p-tooltip:hover {
    z-index: 3;
}

.pbanner__progress .p-tooltip_two:before {
    width: 0;
    height: 0;
    position: absolute;
    top: calc(100%);
    left: 50%;
    content: '';
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 9px solid #000;
    transform: translate(-50%, 0);
}

.pbanner__progress .p-tooltip.p-tooltip--total:after {
    border-bottom-color: #f0f0f0;
}



.pbanner__progress .p-tooltip_two {
    top: calc(100% - 55px);

}

.pbanner__progress .p-tooltip--blue {
    background-color: #009ace;
}

.pbanner__progress .p-tooltip--blue:after {
    border-bottom-color: #009ace !important;
}

.pbanner__progress .p-tooltip--total {
    right: 55px;
    transform: translateX(0);
    background-color: #f0f0f0;
    color: #53565a;
}



.pbanner__progress .progress {
    height: 4px;
    background-color: #d3d3d3;
    border-radius: 3px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    overflow: visible;
    transition: all .3s;
    margin-bottom: 0;
}

.pbanner__progress .progress-bar {
    background-color: #009ace;
    border-radius: 3px;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 2;
}

.pbanner__progress .progress-bar.progress-bar-black {
    background-color: #53565a;
    z-index: 1;
}

.pbanner__image {
    height: calc(1140px / 5);

    background-size: auto 100%;

    background-repeat: no-repeat;
    background-position: left top;
    border-radius: 6px;
    position: relative;
}



/* .pbanner__image:before {
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, #000 100%);
} */

/*.pbanner__image:hover .pbanner__progress .p-tooltip {*/
/*    opacity: 1;*/
/*    visibility: visible;*/
/*    transition: all .3s;*/
/*}*/

/*.pbanner__image:hover .pbanner__progress .progress {*/
/*    height: 10px;*/
/*    transition: all .3s;*/
/*}*/

.pbanner-info {
    display: flex;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-top: none;
}

@media (max-width: 992px) {
    .pbanner-info {
        justify-content: center;
    }
}

.pbanner-tabs {
    display: flex;
}

/* .pbanner-tab {
    padding: 22px 30px;
    color: #A7A8A9;
    font-size: 13px;
    display: flex;
    align-items: center;
    max-width: 200px;
    word-break: break-word;
} */

.pbanner-info {
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-top: none;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    margin-bottom: 24px;
}

@media (max-width: 992px) {
    .pbanner-info {
        justify-content: center;
    }
    .video-filter .icon--sicon-heart-o:hover:before { color: #009ace !important; }
}

@media (max-width: 992px) {
    .pbanner-tab .title-tab {
        padding: 16px 40px;
        /*font-size: 0;*/
        display: none;
    }

    /* .pbanner-tab {
        padding: 12px 10vw;
    } */
}

@media (max-width: 992px) {
    .pbanner-tab:last-child {
        border-right: none;
    }

    .pbanner-tabs {
        justify-content: center;
        width: 100%;
    }
}

.pbanner-tab .icon {
    /* stylelint-disable-line */
    margin-right: 8px;
    font-size: 24px;
}

@media (max-width: 992px) {
    .pbanner-tab .icon {
        margin-right: 0;
    }
}


.pd-search {
    margin: 12px;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
}

@media (max-width: 992px) {
    .pd-search {
        margin: 12px;
    }

    .pd-search-keyword {
        width: 50vw;
    }
}

.pbanner-tab:hover {
    color: #009ace;
    cursor: pointer;
}

.pbanner-tab.active {
    color: #009ace;
}


.pbanner-action {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.pbanner-btn {
    /*color: #53565a;*/
    display: inline-flex;
    align-items: center;
    margin-right: 48px;
}

.pbanner-btn:last-child {
    margin-right: 24px;
}

.pbanner-btn:hover {
    color: #009ace;
}

.pbanner-btn .icon {
    /* stylelint-disable-line */
    font-size: 24px;
    margin-right: 10px;
    /*color: #53565a;*/
}

.pbanner__progress .p-tooltip .icon {
    font-size: 18px;
    margin-right: 6px;
    color: #FFFFFF;
}

.pbanner-btn .bdropdown-toggle {
    display: flex;
    align-items: center;
    color: #53565a;
}

.pbanner-btn .bdropdown-toggle:hover {
    color: #009ace;
}

.pbanner-btn .bdropdown-toggle:hover .icon {
    /* stylelint-disable-line */
    color: #009ace;
}

.pbanner.no-notification {
    filter: grayscale(100%) brightness(0.6);
}

.number-notification {
    margin-bottom: 15px;
    margin-left: -15px;
    background-color: #009ace;
    text-align: center;
    border-radius: 50px;
    line-height: 4px;
    width: auto;
    min-width: 15px;
    height: 15px;
    padding: 5px 2px;
    font-size: .7em;
    color: #fff;
    justify-content: center;
    align-content: center;
    border: 1px solid #FFFFFF;
}

@media (max-width: 992px) {
    .number-notification {
        margin-bottom: 17px;
        margin-left: -9px;
    }
}

.number-notification[value='0'] {
    display: none;
}

.number-notification.deepblue {
    background-color: #0076a5 !important;
}

.number-notification.blue {
    background-color: #009ace !important;
}

.sprojects-item {
    margin-bottom: 12px;
}

.sproject-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin: 2px 0 16px;
    cursor: default !important;
    gap: 8px;
}

.sproject-wishlist {
    line-height: 1;
    font-size: 18px;
    margin-right: 0px;
    color: #a7a8a9;
}

.sproject-date-time {
    display: flex;
    align-items: center;
    line-height: 1;
}

.sproject-time {
    font-size: 13px;
    color: #000 !important;
    cursor: default;
}

.sproject-banner-rating {
    margin-left: 0px !important;
    margin-right: unset !important;
}

.sproject-time__done-count {
    margin-left: 0px;
}

.sproject-time__done-count:not(.cannot-check):hover {
    cursor: pointer;
    color: #009ace;
}

.sproject-meta {
    justify-content: flex-start;
}

.stars-detail-expand .stars:before {
    position: absolute;
}

.stars-detail-expand .stars span {
    position: unset;
}

/* @media (max-width: 992px) {
    .sproject-time,
    .sproject-banner-rating span {
        font-size: 11px !important;
    }
} */

.sproject-time .icon {
    color: #a7a8a9;
    font-size: 16px;
    margin-right: 8px;
}


/* Modal edit max scene */
.bootbox .modal-header {
    padding-bottom: 0;
}

.bootbox .modal-title, .bootbox-prompt-message label {
    color: #000;
    font-size: 13px;
    font-weight: 300;
}

input.form-control {
    padding: 12px 16px;
    color: #000;
    border: 1px solid #F0F0F0;
    background-color: #FFFFFF;
    height: auto;
    font-size: 13px;
    border-radius: 4px !important;
    line-height: 20px;
}

input {
    box-shadow: none !important;
    -webkit-appearance: none !important;
    appearance: none !important;
}

input.form-control:focus {
    border: 1px solid #D3D3D3;
    outline: none !important;
}

input.form-control::placeholder {
    color: #D3D3D3;
}
/* End modal edit max scene */

/* Modal project setting */
.project-setting-modal .popup-title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 24px;
    line-height: 150%;
    color: #000000;
}

.project-setting-modal .popup-footer, .search-block-list-modal .popup-footer {
    text-align: right;
}

.popup-body__part-content {
    padding: 0 !important;
    margin-top: 64px;
    margin-bottom: 24px;
}

.search-block-list-modal .popup-body__part-content {
    margin-top: 0px;
    margin-bottom: 32px;
}

.popup-body__part-content h3 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 18px;
    line-height: 150%;
    color: #000000;
    margin: 0 !important;
}

.popup-body__part-content span {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    color: #000000;
    margin: 0 !important;
}

.label-form {
    width: 100%;
    position: relative;
}

.dropzone {
    min-height: 100px !important;
}

/*.modal.popup-container .modal-dialog.popup-dialog {
    top: unset !important;
    transform: unset !important;
    -webkit-transform: unset !important;
}*/

.project-setting__block-list {
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.user-info__avatar {
    width: 40px;
    height: 40px;
    position: relative;
    margin-right: 4px;
    min-width: 40px;
}

.user-info__avatar img {
    width: 40px;
    height: 40px;
    border-radius: 100%;
}

.user-info__avatar-bandage {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 14px;
    height: 14px;
    background: #009ACE;
    border-radius: 100%;
    color: white;
    border: 1px solid #FFFFFF;
    font-size: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 150%;
}

.user-info__artist-infor {
    display: block;
}

.user-info__artist-infor .artist-infor__name__content {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 13px;
    line-height: 150%;
    color: #000000;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
}

.user-info__artist-infor .artist-infor__name {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.user-info__artist-infor .user-info__delete {
    display: none;
    justify-content: center;
    align-items: center;
    background: transparent;
    color: #A7A8A9;
    padding-left: 10px;
    font-size: 19px;
}

.user-info__artist-infor .user-info__delete:hover {
    color: #0076A5;
}

.show_action_delete {
    display: inline-flex !important;
    margin-right: 5px;
}

.artist-infor__detail {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-style: normal;
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    color: #000000;
}

.artist-infor__detail .artist-infor__organization-name {
    margin-left: 16px;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.artist-infor__detail .artist-infor__title {
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.project-setting__block-list-artist .artist-infor__organization-name {
    overflow: hidden;
    text-overflow: ellipsis;
}

.block-list__user-info-left {
    display: flex;
}

.block-list__user-info-right {
    width: 20%;
    text-align: right;
    min-width: 20%;
}

.account__trade-item::before {
    height: 1px;
}

.account__trade-item::after {
    height: 8px;
    width: 8px;
    border: 1px solid #a7a8a9;
}

.account__trade-item.active:after {
    width: 9px;
    height: 9px;
    border: 1px solid #009ace;
}

.account__trade-item:first-child:after, .account__trade-item:last-child:after {
    left: 50%;
}

.account__tradeoff {
    margin: 11px 0px 0px 0px;
    padding: 0;
}

.block-list__identity-confirmation {
    display: flex;
    justify-content: flex-end;
}

.block-list__identity-confirmation .identity-confirmation__identification, .block-list__identity-confirmation .identity-confirmation__NDA{
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 8px;
    line-height: 150%;
    color: #000000;
    padding: 2px 6px;
    border-radius: 2px;
    border: 1px solid #F0F0F0;
    margin-left: 7px;
}

.account__trade-item.active:first-child:after, .account__trade-item.active:last-child:after {
    left: 50%;
}

.project-setting__block-list__butoon-add {
    border: 1px solid #F0F0F0;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    padding-top: 10px;
    align-items: center;
    margin-bottom: 20px;
    flex-direction: column;
}

.project-setting__block-list__butoon-add .icon {
    font-size: 20px;
    color: #A7A8A9;
}

.project-setting__block-list__butoon-add p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    color: #000000;
}

.project-setting__block-list__butoon-add:hover {
    background-color: #A7A8A9 !important;
    transition: 0.2s;
    color: white;
    cursor: pointer;
}

.project-setting__block-list__butoon-add:hover p, .project-setting__block-list__butoon-add:hover .icon {
    transition: 0.2s;
    color: white;
}

.label-form .account__field-label__icon-search {
    position: absolute;
    bottom: 30%;
    right: auto;
    padding-left: 16px;
    color: #C4C4C4;
}

.block-list__search {
    padding-left: 42px !important;
    max-width: 260px;
}

#modal-search-block-list .modal-header {
    padding: 14px 24px 6px 24px;
}

#modal-search-block-list .smodal-close--prev {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    color: #53565a;
    text-align: center;
}

#modal-search-block-list .smodal-close--prev:hover {
    color: #0076A5;
}

#modal-search-block-list .smodal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    /* color: #a7a8a9; */
    z-index: 9;
    font-size: 20px;
}

.block-list__results{
    width: 100%;
}

.block-list__results-search {
    overflow-x: scroll;
    flex-wrap: nowrap;
    white-space: nowrap;
    display: flex;
    padding-bottom: 24px;
}

.block-list__results-search-detail {
    flex: 0 0 350px;
    margin-right: 24px;
    max-width: 350px;
    padding: 24px;
    border: 1px solid #F0F0F0;
}

.block-list__results-search-detail .project-setting__block-list {
    border: none;
    padding: 0;
}

.block-list__results-search-detail:last-child {
    margin-right: 0px;
}

.artist-block-list {
    border: 1px solid #F0F0F0;
    box-sizing: border-box;
    border-radius: 4px;
    min-height: 300px;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    flex-wrap: nowrap;
    white-space: nowrap;
}

.artist-block-list__blocked-company {
    margin-bottom: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.artist-block-list__blocked-company .blocked-company__company-name {
   width: 30%;
}

.artist-block-list__blocked-company .blocked-company__reason-text {
   width: 70%;
   padding-left: 10px;
}

.artist-block-list__blocked-company .blocked-company__reason-text, .artist-block-list__blocked-company .blocked-company__company-name {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    color: #000000;
    height: auto;
}

.artist-block-list__blocked-company:last-child {
    margin-bottom: 0px;
}

.block-list__register-action{
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
}

.disabled-btn-add {
    pointer-events: none !important;
    background-color: var(--soremo-border) !important;
    color: var(--soremo-light-gray) !important;
    background-image: none !important;
}

#modal-search-block-list .artist-infor__name,
#modal-search-block-list .blocked-company__company-name,
#modal-search-block-list .blocked-company__reason-text
{
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 190px;
    display: block;
}
/* End modal project setting */
/* Modal add director */
#modal-add-director .block-list__results-search-detail, #modal-add-admin .block-list__results-search-detail {
    flex: 0 0 203px;
    max-width: 203px;
    padding: 12px;
    border-radius: 12px;
}

#modal-add-director .modal-header,  #modal-add-admin .modal-header {
    padding: 14px 24px 6px 24px;
}

#modal-add-director .smodal-close, #modal-add-admin .smodal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    /* color: #a7a8a9; */
    z-index: 9;
    font-size: 20px;
}

#modal-add-director .smodal-close--prev, #modal-add-admin .smodal-close--prev {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    color: #53565a;
    text-align: center;
}

#modal-add-director .smodal-close--prev:hover, #modal-add-admin .smodal-close--prev:hover {
    color: #0076A5;
}

#modal-add-director .block-list__user-info-right, #modal-add-admin .block-list__user-info-right {
    width: 80%;
    text-align: left;
}

#modal-add-director .block-list__identity-confirmation, #modal-add-admin .block-list__identity-confirmation {
    justify-content: flex-start;
}

#modal-add-director .block-list__identity-confirmation .identity-confirmation__identification, .block-list__identity-confirmation .identity-confirmation__NDA {
    margin: 20px 7px 12px 0px !important;
}

#modal-add-director .user-info__artist-infor .artist-infor__name, #modal-add-admin .user-info__artist-infor .artist-infor__name {
    overflow: hidden;
    max-width: 130px;
    text-overflow: ellipsis;
    display: block;
}

#modal-add-admin .user-info__artist-infor {
    display: flex;
    align-items: center;
}

#modal-add-director .add-artist-into-list, #modal-add-admin .add-artist-into-list {
    width: 100%;
    font-family: var(--font-family-R) !important;
    font-size: 13px !important;
    font-weight: var(--font-weight-400);
    line-height: var(--line-height-20);
    background-image: none;
    border-radius: 4px;
    border: none !important;
    padding: 12px 0px !important;
    cursor: pointer;
    color: #fff;
    background-color: var(--blue-color);
}

#modal-add-director .add-artist-into-list:hover, #modal-add-admin .add-artist-into-list:hover {
    color: #fff !important;
    background-color: var(--soremo-deep-blue) !important;
}

#modal-add-director .block-list__register-action, #modal-add-admin .block-list__register-action {
   display: block;
}

#modal-add-director .popup-body__part-content:not(:first-child), #modal-add-admin .popup-body__part-content:not(:first-child) {
   margin-top: 0px;
}

#modal-add-director .account__tradeoff, #modal-add-admin .account__tradeoff {
    margin: 16px 0 0 3px;
    width: 80%;
}
/* End modal add director */

.col-content-budget {
    color: #0f9ca9 !important;
}

.modal-unlock-artist {
    z-index: 9999;
}

#modal-confirm-delete:before,
#modal-staff-credit:before,
#modal-staff-credit-artist:before,
#modal-staff-credit-section:before {
    content:"";
    display:inline-block !important;
    width:0;
    height:100% !important;
    vertical-align:middle !important;
}

/*@media (max-width: 576px) {
    #modal-staff-credit:before,
    #modal-staff-credit-artist:before,
    #modal-staff-credit-section:before,
    #modal-confirm-delete:before {
        height: 0 !important;
    }
}*/

#modal-staff-credit,
#modal-staff-credit-artist,
#modal-staff-credit-section,
#modal-confirm-delete {
    vertical-align: middle !important;
}

#modal-staff-credit .modal-dialog.popup-dialog,
#modal-staff-credit-section .modal-dialog.popup-dialog,
#modal-staff-credit-artist .modal-dialog.popup-dialog,
#modal-confirm-delete .modal-dialog.popup-dialog{
    vertical-align: middle;
    /*top: 0;*/
}

#modal-staff-credit-artist .modal-content.popup-content,
#modal-staff-credit .modal-content.popup-content {
    max-height: calc(100dvh - 48px);
}

#modal-staff-credit-section .modal-content.popup-content {
    /*min-height: 200px;*/
}
#modal-staff-credit-section .load-more-loading {
    /*min-height: 200px;*/
    height: auto;
}

#modal-staff-credit-artist .load-more-loading,
#modal-staff-credit .load-more-loading {
    /*min-height: 400px;*/
    height: auto;
}

.modal-staff-credit-header {
    display: flex;
    flex-direction: row;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 16px;
}

.modal-staff-credit-header-lan {
    margin-right: 32px;
    cursor: pointer;
}

.modal-staff-credit-header-lan:hover {
    color: #009ace;
}

.modal-staff-credit-header-lan.active {
    color: #009ace;
    position: relative;
}

.modal-staff-credit-header-lan.active:after {
    content: " ";
    background: #009ace;
    width: 100%;
    left: 0;
    bottom: -4px;
    height: 4px;
    border-radius: 4px;
    position: absolute;
}

.modal-staff-credit-body {
    text-align: center;
    /*padding-bottom: 16px;*/
    /*margin-bottom: 8px;*/
    /*min-height: 240px;*/
}

.modal-staff-credit-section-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    /*padding-top: 24px;*/
    background: #fcfcfc;
    /* border: 1px solid #f0f0f0; */
    border-radius: 4px;
    padding: 4px 0;
    margin-bottom: 4px;
    min-height: 32px;

    position: relative;
}

.editable .modal-staff-credit-section-container:hover {
    background: #f0f0f0;
}

.modal-staff-credit-section-artist {
    /*position: relative;*/
    display: flex;
    flex-direction: row;
    justify-content: center;
    /*margin-bottom: 16px;*/
}

.modal-staff-credit-section-artist > div {
    flex: 1;
}

.modal-staff-credit-section-artist--title {
    text-align: right;
    margin-right: 8px;
    color: #a7a8a9 !important;
}

.modal-staff-credit-section-artist--name {
    text-align: left;
    margin-left: 8px;
}

.modal-staff-credit-section-artist--title,
.modal-staff-credit-section-artist--name,
.modal-staff-credit-section-header--title,
.modal-staff-credit-section-header--desc {
    white-space: pre-line;
    line-break: anywhere;
}


.editable .modal-staff-credit-section-artist--title,
.editable .modal-staff-credit-section-artist--name,
.editable .modal-staff-credit-section-header--title,
.editable .modal-staff-credit-section-header--desc {
    cursor: pointer;
}
.modal-staff-credit-section-header--title {
    /* min-height: 8px;
    padding: 8px 0 8px; */
    /* border-top: 1px solid #f0f0f0; */
}

.modal-staff-credit-section-header--title,
.modal-staff-credit-section-header--desc {
    /*min-height: 20px;*/
}

/*.modal-staff-credit-section-header {
    position: relative;
} */

.modal-staff-credit-section--action-btn {
    position: absolute;
    top: 4px;
    right: 0;
    display: none;
}

.editable .modal-staff-credit-section-container:hover .modal-staff-credit-section-header > .modal-staff-credit-section--action-btn,
.editable .modal-staff-credit-section-artist:hover .modal-staff-credit-section--action-btn {
    display: block;
    /*background: #ffffff;*/
}


.modal-staff-credit-section--action-btn i {
    font-size: 24px;
    padding: 4px;
    cursor: pointer;
    color: #a7a8a9;
}

.modal-staff-credit-section--action-btn i:hover {
    color: #009ace;
}

/*.modal-staff-credit-section-header--title {
    margin-bottom: 8px;
}*/

.modal-staff-credit-artist-add,
.modal-staff-credit-section-add {
    /* display: none; */
    justify-content: center;
    cursor: pointer;
    /* flex-direction: column;
    width: 200px; */
    /* margin: 12px auto; */
    /* padding: 12px 0 8px; */
    /* border: 1px dashed #f0f0f0;
    border-radius: 12px; */
}

.modal-staff-credit-artist-add i,
.modal-staff-credit-section-add i {
    font-size: 16px;
    background: #a7a8a9;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    margin: auto;
    padding: 4px;
    line-height: 16px;
}

.modal-staff-credit-artist-add span,
.modal-staff-credit-section-add span {
    margin-top: 4px;
}

.modal-staff-credit-artist-add:hover,
.modal-staff-credit-section-add:hover {
    background: #009ace;
    color: white;
    transition: 0.2s;
}


.modal-staff-credit-artist-add:hover i,
.modal-staff-credit-section-add:hover i {
    background: white;
    color: #a7a8a9;
}

.modal-staff-credit-section-add {
    width: 100%;
}

.editable .modal-staff-credit-artist-add,
.editable .modal-staff-credit-section-add {
    display: flex;
}

.modal-staff-credit-artist-header {
    overflow-x: scroll;
    display: flex;
}

.modal-staff-credit-artist-header::-webkit-scrollbar {
	height: 6px;
	background-color: #F0F0F0;
    border-radius: 4px;
    margin: 4px;
    cursor: pointer;
}

.modal-staff-credit-artist-header::-webkit-scrollbar-thumb {
    background-color: #a7a8a9;
    border-radius: 4px;
}

.modal-staff-credit-artist-header-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    width: auto;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin: 0 16px 16px 0;
    cursor: pointer;
}

.modal-staff-credit-artist-header-item.active,
.modal-staff-credit-artist-header-item:hover {
    border: 1px solid #009ace;
    transition: 0.2s;
}

.modal-staff-credit-artist-header-item--title {
    color: #a7a8a9 !important;
}

.modal-staff-credit-artist-header-item--avatar {
    height: 64px;
    width: 64px;
    background: #f0f0f0;
    border-radius: 50%;
    margin: 8px 0 14px;
    background-size: 100%;
    background-position: center;
    background-repeat: no-repeat;
}

.modal-staff-credit-artist-header-item--name,
.modal-staff-credit-artist-header-item--title {
    text-overflow: ellipsis;
    overflow: hidden;
    width: 110px;
    text-align: center;
    white-space: nowrap;
    margin-bottom: 6px;
}

.modal-staff-credit-artist-toggle.custom-switch {
    margin: 32px 0 24px;
    display: flex;
    align-items: center;
}

.modal-staff-credit-artist-toggle.custom-switch .form-check-group {
    margin: 0;
}

.modal-staff-credit-artist-toggle.custom-switch .form-check-group input:checked + .switch-slider:before {
    transform: translateX(10px);
}

.modal-staff-credit-artist-toggle.custom-switch .form-check-group .switch-slider {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    line-height: 17px;
    width: 29px;
    height: 19px;
}

.modal-staff-credit-artist-toggle.custom-switch .form-check-group .switch-slider:before {
    position: absolute;
    content: '';
    height: 17px;
    width: 17px;
    background-color: #A7A8A9;
    border-radius: 50%;
    transition: .3s;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 10%), 0 1px 2px 0 rgb(0 0 0 / 6%);
    top: 1px;
    left: 1px;
}

.modal-staff-credit-artist-toggle.custom-switch .switch-label {
    color: #000000;
}

.modal-staff-credit-artist-body {
    display: flex;
    flex-direction: column;
    padding-bottom: 24px;
    margin-bottom: 8px;
}

.modal-staff-credit-body:not(.editable) .link-to-profile {
    cursor: pointer;
}

.modal-staff-credit-body:not(.editable) .link-to-profile:hover {
    color: #009ace;
    transition: 0.2s;
}

.source:dragging {
    background: red;
}

.project--no-artist .modal-staff-credit-artist-add,
.modal-staff-credit-section-container:not(.section--container) {
    display: none;
}

.project-text {
    position: absolute;
    top: calc(50% - 18px);
    left: 24px;
    color: #000;
    width: 200px;
    line-break: anywhere;
    text-overflow: ellipsis;
    overflow: hidden;
    max-height: 72px;
}
