/* Payment */
:root {
    --black1-color: #000000;
    --black2-color: #53565A;
    --grey1-color: #A7A8A9;
    --grey2-color: #D3D3D3;
    --grey3-color: #F0F0F0;
    --white-color: #FFFFFF;
    --blue-color: #009ACE;
    --blue-color-hover: #0076A5;
    --background-color: #FCFCFC;
    --error-color: #2CC84D;
}

.payment {
    margin-top: 65px;
    background-color: var(--white-color);
}

.payment__heading h3 {
    margin: 32px 0 4px;
}

.payment__heading p {
    margin: 0;
    word-break: break-word;
    white-space: pre-line;
}

.payment__content-wrap {
    margin: 32px 0;
    padding: 24px 12px;
    border: 1px solid var(--soremo-border);
    border-radius: 6px;
}

.payment--statement {
    overflow-y: auto;
    max-height: 290px;
}

.payment--all {
    overflow-y: auto;
    max-height: 600px;
}

.payment__content-heading .heading--18 {
    margin-bottom: 8px;
}

.payment__content-body {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    margin: 4px 0 32px;
}

.payment__content-body-left {
    display: flex;
    align-items: baseline;
}

.payment__content-body-left .bodytext--13 {
    margin-left: 8px;
}

.payment__content-body-right .btn {
    padding: 12px 43px !important;
}

.payment__content-footer .bodytext--13 {
    margin-left: 4px;
}

.payment__content-list {

}

.payment__content-item {
    padding: 16px 0;
}

.payment__content-item .heading--16 {
    padding: 16px 0;
}

.payment-switch {
    margin-bottom: 16px;
}

.table-report td:last-child {
    text-align: right;
}

.table-report td:last-child button {
    padding: 8px 37px !important;
}

.payment .table thead tr th {
    padding: 8px 8px 16px;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    color: var(--grey1-color);
    min-width: 120px;
    border-bottom: 1px solid var(--soremo-border);
}

.payment .table tr td {
    padding: 4px;
    border: none;
    vertical-align: middle;
}

.payment .table tr td .bodytext--13 {
    word-break: break-word;
    white-space: pre-line;
}

.payment .table tr td .caption--11 {
    display: inline-block;
    padding: 4px 16px;
    margin: 4px;
    color: var(--black1-color);
    border: 1px solid var(--black2-color);
    border-radius: 4px;
}

.table.table-history  tr td:nth-child(2) {
    text-align: right;
}

.payment .table tfoot tr td {
    border-top: 1px solid var(--soremo-border);
}

.table tfoot tr td:last-child {
    text-align: right;
}

.table tfoot tr td:last-child .icon {
    color: var(--soremo-placeholder);
}

.payment-scene {
    display: flex;
    align-items: center;
}

.payment-scene__user {
    display: flex;
    align-items: center;
}

.payment-scene__user .icon {
    color: var(--grey1-color);    
    font-size: 12px;
    margin: 0 2px;
}

.payment-scene__name {
    margin-left: 9px;
}

.payment-scene__name .bodytext--13 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
}

table {
    width: 100%;
}

@media (max-width: 992px) { 
    /* .payment__content-wrap {
        padding: 24px 12px;
    } */

    .payment__content-body-right .btn {
        padding: 12px 36px !important;
    }

    .payment-scene__name .bodytext--13 {
        max-width: 64px;
        white-space: nowrap !important;
    }
}

@media (max-width: 739px) {
    .payment__content-body-left .heading--40 {
        font-size: 24px;
        line-height: 36px;
    }

    .payment__content-body-left {
        width: 100%;
        justify-content: right;
    }

    .payment__content-body-right {
        display: flex;
        justify-content: right;
        margin: 4px;
        width: 100%;
    }
}
/* End Payment */

/* Modal preview pdf */
.modal--pdf {
    top: 0 !important;
}
/* End Modal preview pdf */
