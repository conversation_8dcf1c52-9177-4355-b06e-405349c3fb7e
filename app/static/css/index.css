@charset "UTF-8";
/* CSS Document */

/* 共通部分
------------------------------ */
html {
  font-size: 100%;
  scroll-padding-top: 60px; /* 固定ヘッダの高さ分 */
}

body {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'A+mfCv-AXISラウンド 50 R StdN', 'A+mfCv-AXISラウンド 50 M StdN';
  line-height: 1.875;
  color: #000000;
  overflow-y: auto;
}

img {
  max-width: 100%;
}

h2 {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 2.5rem;
  font-weight: normal;
  letter-spacing: 2px;
}

h3 {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 1.875rem; /* 30px */
  font-weight: normal;
}

h4 {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 1.5rem;
  font-weight: normal;
}

h5 {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 1.25rem;
  font-weight: normal;

}

p {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 1rem;
  font-weight: normal;
}

a {
  text-decoration: none;
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 0.8125rem;
  font-weight: normal;
  letter-spacing: 0px;
}

li {
  list-style-type: none;
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 0.8125rem;
  font-weight: normal;
}

b {
  font-family: 'A+mfCv-AXISラウンド 50 M StdN';
  font-weight: normal;
}

hr {
  width: 100%;
  border: thin solid #f0f0f0;
  margin:60px auto;
}

/*　color　*/

/* Custom Property */
:root {
  --color-soremo-blue: #009ACE;
  --color-soremo-deepblue: #0076A5;
  --color-soremo-gray: #A7A8A9;
  --color-soremo-deepgray: #53565A;
  --color-soremo-green: #2CC84D;

  --color-soremo-background: #FFFFFF;
}


.blue {
    color:#009ACE;
}

.deepblue {
    color:#0076A5;
}

.green {
    color:#2CC84D;
}

.lightgray {
    color:#A7A8A9;
}

.gray {
    color:#53565A;
}




/* HEADER
-------------------------------- */
header {
  background-color: #feffff;
  border-bottom: thin solid #f0f0f0;
  position: fixed;
  top: 0;
  left: 0;
  width:100%;
  z-index: 10;

}

.wrapper {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 5px;
}


.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0px;
}

.logo {
  width: 35px;
  margin: 10px 0px 0px 0px;
}

.header-nav {
  display: flex;
  font-size: 0.8125rem;
  list-style: none;
}
.header-nav li {
  margin: 0px 0px 0px 60px;
}
.header-nav a {
  color: #000000;
}
.header-nav a:hover {
  color: #009ace;
}

.line {
  width: 100%;
  border-top: thin solid #f0f0f0;
}

/*
-------default banner　-------*/

.banner {
    background: #ffffff !important;
    width: 100%;
    height: 360px;
    margin: 60px 0 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: none;
}



/*
------- default blog -------*/


.blog {
    width:clamp(325px,100%,1000px);
    padding:0px 5px 0px;
    text-align: left;
    background: #ffffff;

}

.blog h3 {
    margin:90px auto 5px;
    border-top: thin solid #f0f0f0;
    text-align: justify;
}

.blog h4 {
    padding-top: 15px;
    text-align: justify;
}

.blog h5 {
    padding-top: 15px;
    text-align: justify;
}

.blog p {
    text-align: justify;
    margin-top:10px;
}

.blog img {
    width:clamp(325px,68.2%,375px);
}

.blog ul li {
  list-style-type:disc;
  list-style-position: inside;
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-weight: normal;
}

.blog ol li {
  list-style-type:decimal;
  list-style-position: inside;
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-weight: normal;
}



.blog a:link,.blog a:visited,.blog a:hover,.blog a:active {
  color: #333333;
}

.tag-blue {
    color:#ffffff;
    background-color: #009ace;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 0.8125rem;
    padding: 5px 10px;
    margin:0px 0px;
}

.tag-gray {
    color:#ffffff;
    background-color: #A7A8A9;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 0.8125rem;
    padding: 5px 5px;
    margin:0px 0px;
}

.tag-owner {
    color:#0076A5;
    border:medium #0076A5 1px;
    background-color: #ffffff;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 0.8125rem;
    padding: 1px 5px;
    margin:0px 15px 0px 5px;
}

.tag-director {
    color:#53565A;
    border:medium #53565A 1px;
    background-color: #ffffff;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 0.8125rem;
    padding: 2px 3px;
    margin:0px 15px 0px 5px;
    min-width: 90px;
}



/* banner */

.top-banner {
    background: #fcfcfc;
    width: 100%;
    height:360px;
    margin-bottom: 15px;
}

.top-banner h2 {
    text-align: center;
    padding:150px 0 0px;
}

.top-banner p {
    text-align: center;
}


/* ----- nav -----*/

.main-nav {
  display: flex;
  flex-flow: row nowrap;
  font-size: 0.8125rem;
  list-style: none;
}
.main-nav li {
  margin: 0px 30px 0px 0px;
}

.main-nav a {
  color: #009ace;
  padding: 10px 15px;
  border-radius: 30px;
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 1rem;

}

.main-nav a:hover {
    background-color: #009cde;
    color: #ffffff;

}


/* MAIN - Your Sound Conciege SOREMO
-------------------------------- */
.main {
  max-width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 382px auto 0px;
  position: relative;
  z-index: 1;
}

.srm3 {
  background-color: #ffffff !important;
  background: #ffffff !important;
}

/* Main Title */
.main-title {
  padding: calc(50vh - 130px) 10px 0;
}

.main-title h2 {
  font-family: 'A+mfCv-AXISラウンド 50 コンプレス R';
  text-align: center;
  padding-bottom: 40px;
  font-size: 96px;
  line-height: 96px;
  text-transform: uppercase;
  background: url(../images/bg_heading_text.png) no-repeat; 
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  -webkit-background-size: cover;
  -moz-background-size: cover;
  -o-background-size: cover;
  background-size: cover;
}

.image-hero-text-container {
  display: flex;
  justify-content: center;
}

.image-hero-text {
  width: 69vw;
  height: auto;
  padding-bottom: 40px;
}

.main-title h4 {
  text-align: left;
  padding-left: 30px;
}

.main-title p {
  margin: 5px 0px 0px;
  text-align: left;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 150%;
  color: #000000 !important;
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
}

.main-title a:hover{
    color:#0076A5;
}

/* HERO IMAGE (VIDEO)
------------------------------ */
.video-container {
  position: relative;
  overflow-y: hidden;
  margin-top: 60px;
}
video {
  width: 100%;
}

.heroimage {
  width: 100%;
}


/* SIGNUP
------------------------------ */
.signup {
  text-align: center;
  margin: 90px auto 120px;
}

.signup h4 {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  padding-bottom: 24px;
  font-size: 24px;
  line-height: 150%;
  margin: 0;
  color: #000000;
  letter-spacing: 0.17px;
}

.signup p {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-style: normal;
  font-weight: 300;
  font-size: 11px;
  line-height: 150%;
  padding: 24px 8px 0;
  letter-spacing: 0.14px;
}

.button {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  color: #FFFFFF;
  font-size: 24px;
  line-height: 150%;
  letter-spacing: 0.17px;
  text-align: center;
  background: #009ace;
  border-radius: 4px;
  opacity: 1;
  padding: 12px 24px;
  transition: background 0.2s;
  min-width: 300px;
}

.button:hover {
  background: #0076A5;
  color: #FFFFFF;
}




/*　Article ３つのコンセプト
------------------------------ */
.concept {
  /* border-top: thin solid #f0f0f0; */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 480px;
  margin: 120px auto 120px;
}

.concept-text {
  width: 38.2%;
  padding: 0 5px;
}
.concept-text h3 {
  font-size: 24px;
  line-height: 150%;
  margin: 90px auto 30px;
  color: #000;
  /* text-align: right; */
  letter-spacing: -0.327273px;
}
.concept-text p {
  color: #000000;
  margin: 30px auto 120px;
  /* text-align: right; */
  font-weight: 300;
  font-size: 13px;
  line-height: 150%;
}

.concept-text-left {
  text-align: left;
}

.concept-text-right {
  text-align: right;
}

.concept-image {
  width: 61.8%;
  margin: 75px auto 30px;
}
.concept-image-reverse {
  width: 61.8%;
  margin: 105px auto 30px;
}

article h3 {
    margin: 30px 0;
}




/*　Aside 機能一覧
------------------------------ */
aside {
  background-color: #fcfcfc;
  padding: 90px 0 90px;
}

aside h3.wrapper {
  color: var(--color-soremo-blue);
  padding: 10px 20px 16px;
}

.function {
  display: flex;
  justify-content:space-between;
  flex-wrap: wrap;
}
.function-item {
  width: 50%;
  padding:0px 15px 60px;
}

.function-item h5 {
  color: #000;
  margin: 0;
  padding: 24px 0 16px;
  font-size: 18px;
  line-height: 150%;
  letter-spacing: -0.245455px;
}

.function-item p {
    padding: 0px 0px 0px 0px;
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    color: #000000;
    letter-spacing: 0.168824px;
}

/* List logo */
.list-logo {
  background-color: #FFFFFF;
  border-bottom: 1px solid #F0F0F0;
}
.logo-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 32px;
}

.logo-item {
}

.owl-theme .owl-nav.disabled+.owl-dots {
  display: none;
}

/*　Footer フッター
------------------------------ */

.page-footer {
  display: flex;
  justify-content:flex-start;
  padding: 80px 0px 0px;
flex-wrap: wrap;
}

.footer-nav {
    flex-direction: column;
    min-width: 200px;
}

.footer-nav a:hover {
    color: #0076a5;
}

.footer-nav li {
    font-size: 0.8125rem;
    list-style: none;
    color: #000000;
    padding: 0 10px 15px;
}

.footer-nav a {
    font-size: 0.8125rem;
    list-style: none;
    color: #000000;
}


.footer-logo {
    background: #53565A;
    text-align: center;
    margin: 40px auto 0px;
    padding: 0px 20px 1px !important;
}

.footer-logo img {
    margin: 120px auto 30px;
}

.footer-logo p {
    color: #FFFFFF;
    font-weight: 300;
    font-size: 11px;
    line-height: 150%;
    padding-bottom: 60px;
    margin-top: 0;
}

/* Ipad Version
------------------------------
------------------------------ */
@media (max-width:992px) {
  .main-title h2 {
    font-size: 50px;
    line-height: 50px;
  }
  .main-title p {
    font-size: 18px;
    text-align: center;
  }
}

/* SP Version
------------------------------
------------------------------ */

@media (max-width:560px) {


h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1rem;
}

p {
  font-size: 0.8125rem;
}

li {
  font-size: 0.8125rem;
}


.header-nav li {
    margin: 0px 0px 0px 20px;
    font-size: 0.6875rem;
}

.main-nav li {
    margin: 0px 0px 0px 20px;
    font-size: 0.6875rem;
}



/* MAIN - Your Sound Conciege SOREMO
-------------------------------- */
  .main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 120px auto 0px;
    position: relative;
    z-index: 1;
  }

  /* Main Title */
  .main-title h2 {
    font-size: 17px;
    line-height: 17px;
    text-transform: uppercase;
    padding-bottom: 10px;
  }
  .main-title p {
    margin: 5px 0px 0px;
    font-size: 9px;
  }

  .video-container {
    position: relative;
    overflow-y: hidden;
    margin-top: 90px;
  }

  /* Contents */
  .concept {
    border-top: thin solid #f0f0f0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* height: 720px; */
  }
  .concept-text {
    position: absolute;
    width: 100%;
    text-align: center;
  }
  .concept-text p {
    margin-bottom: 320px;
    text-align: left;
    padding: 0 0.5rem;
  }
  .concept-image {
    width: 100%;
    position: absolute;
    top: 200px;
  }
  .concept-image-reverse {
    width: 100%;
    position: absolute;
    top: 250px;
  }
  /* function */
  .function {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
  }
  .function-item {
    width: 100%;
    margin: 0 auto;
  }

  /* Aside */
  aside {
    padding: 0;
  }

  /* List logo */
  .logo-list {
    padding: 0;
  }

  .logo-item {
    padding: 25px 0;
  }
}
