@charset "UTF-8";

body {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'A+mfCv-AXISラウンド 50 R StdN', 'A+mfCv-AXISラウンド 50 M StdN';
    line-height: 1.875;
    color: #333333;
}

.gallery__list {
   max-width: 500px;
   margin: 50px auto 0 auto;
}

.gallery__row {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery__item {
  position: relative;
  padding: 5px;
  flex: 0 0 90px;
  height: 90px;
  margin: 5px;
  background-size: cover;
  background-position: center center;
}

.gallery__item:hover {
  cursor: pointer;
}

.list-new-works__media.gallery__item {
  cursor: default;
}

.gallery__item:after {
  content: '';
}


@media screen and (max-width: 1440px) {
  .gallery__list {
     max-width: 500px; }
  .gallery__item {
      padding: 3px; }
}

@media screen and (max-width: 767px) {
  .gallery__list {
     max-width: 300px;
     padding: 0; }

    .gallery__item {
        flex: 0 0 60px;
        height: 60px;
    }
}

.list-composer {
  margin-top: 60px;
  padding-top: 10px;
  border-top: 1px solid #d6d6d6; }
  .list-artist__title,
  .list-composer__title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 18px;
    color: #000;
    margin-bottom: 5px; }
  .list-artist__sub-title,
  .list-composer__sub-title {
    font-size: 11px;
    color: #000;
    margin-bottom: 25px; }
  .list-artist .user-item,
  .list-composer .user-item {
    margin: 0 10px; }
    .list-artist .user-item:hover,
    .list-composer .user-item:hover {
      cursor: pointer; }
      .list-artist .user-item:hover .user-item__action,
      .list-composer .user-item:hover .user-item__action {
        opacity: 1;
        visibility: visible;
        transition: all .3s; }

  .list-artist .user-item,
  .list-composer .user-item {
    flex: 0 0 140px;
  }

  .user-item__avatar-img {
    width: 112px;
    height: 112px;
    background: #ffffff;
    border-radius: 50%;
  }
    @media screen and (max-width: 767px) {
      .list-artist .user-item,
      .list-composer .user-item {
        flex: 0 0 90px;
        margin: 0;
      }
      .user-item__avatar-img {
        width: 72px;
        height: 72px;
        border-radius: 50%;
      }
    }
    .list-artist .user-item:first-child,
    .list-composer .user-item:first-child {
      margin-left: 0; }
    .list-artist .user-item:last-child,
    .list-composer .user-item:last-child {
      margin-right: 0; }
  .list-artist .custom-scrollbar-horizontal,
  .list-composer .custom-scrollbar-horizontal {
    margin-left: 6%;
    padding-bottom: 10px; }
    .list-artist .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
    .list-composer .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
      margin: 0; }
  .list-artist__list,
  .list-composer__list {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: scroll;}

.list-composer {
  margin-bottom: 45px; }
  .list-composer__add {
    margin-right: 10px; }
    .list-composer__add .srm-icon-wrap {
      background-color: #53565a;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center; }
    .list-composer__add .srm-add {
      background-color: #fff; }
    .list-composer__add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 140px;
      height: 140px;
      border-radius: 50%;
      background-color: #c4c4c4;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
      @media screen and (max-width: 1440px) {
        .list-composer__add-btn {
          width: 110px;
          height: 110px; } }
      @media screen and (max-width: 767px) {
        .list-composer__add-btn {
          height: 90px;
          width: 90px; } }
      .list-composer__add-btn:hover {
        cursor: pointer; }
    .list-composer__add-desc {
      font-size: 13px;
      color: #A7A8A9;
      margin-top: 10px;
      text-align: center; }
      @media screen and (max-width: 1440px) {
        .list-composer__add-desc {
          font-size: 13px; } }

.user-item__action {
  /* stylelint-disable-line */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  opacity: 0;
  visibility: hidden; }

.user-item__link {
  margin-right: 10px; }
  @media screen and (max-width: 767px) {
    .user-item__link {
      margin-right: 5px; } }

.user-item__delete {
  margin-left: 10px; }
  @media screen and (max-width: 767px) {
    .user-item__delete {
      margin-left: 5px; } }

.user-item__link, .user-item__delete {
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center; }
  @media screen and (max-width: 767px) {
    .user-item__link, .user-item__delete {
      width: 30px;
      height: 30px; } }
  @media screen and (max-width: 767px) {
    .user-item__link .srm-icon, .user-item__delete .srm-icon {
      transform: scale(0.7); } }
  .user-item__link:hover .srm-icon, .user-item__delete:hover .srm-icon {
    background-color: #009dc4; }

.user-item__label {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background-color: #009dc4;
  color: #fff;
  border-radius: 23px;
  font-size: 12px;
  padding: 0 8px;
  text-transform: uppercase; }
  @media screen and (max-width: 767px) {
    .user-item__label {
      font-size: 10px; } }

.user-item__info {
  text-align: center;
  margin: 8px 0; }

.user-item__name {
  font-size: 13px;
  color: #000;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word; }
  @media screen and (max-width: 1440px) {
    .user-item__name {
      font-size: 13px; } }
  .user-item__name:hover {
    color: #009dc4;
    transition: all .3s; }

.user-item__work {
  font-size: 11px;
  color: #000;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word; }
  @media screen and (max-width: 1440px) {
    .user-item__work {
      font-size: 11px; } }


.user-item__avatar {
  width: 80%;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 50%;
  margin: auto;
}

#composer-modal .modal-body,
#composer-new-modal .modal-body {
  max-height: 100vh;
  padding: 20px; }

.composer-modal .custom-scrollbar-horizontal {
  padding-bottom: 10px; }
  .composer-modal .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
    margin: 0; }

.composer-modal .user-item {
  /* stylelint-disable-line */
  flex: 0 0 128px;
}

.composer-modal .user-item .user-item__avatar-img {
  width: 102.5px;
  height: 102.5px;
}

.composer-modal .user-item:hover {
  cursor: pointer;
}

@media screen and (max-width: 1440px) {
  .composer-modal .user-item {
    flex: 0 0 110px;
  }

  .composer-modal .user-item .user-item__avatar-img {
    width: 88px;
    height: 88px;
  }
}

@media screen and (max-width: 767px) {
  .composer-modal .user-item {
    flex: 0 0 90px;
    margin: 0;
  }

  .composer-modal .user-item .user-item__avatar-img {
    width: 72px;
    height: 72px;
  }
}

.composer-modal .user-item:first-child {
  margin-left: 0;
}

.composer-modal .user-item:last-child {
  margin-right: 0;
}

.composer-modal .user-item .user-item__avatar-img {
  border: 4px solid #ffffff;
}

.composer-modal .user-item.selected .user-item__avatar-img {
  /* stylelint-disable-line */
  border: 4px solid #009dc4;
}

.composer-modal__title {
  font-size: 20px;
  color: #333; }

.composer-modal__form {
  margin-top: 40px;
  padding-left: 30px;
  padding-right: 30px; }

.composer-modal__input {
  border: 1px solid #d6d6d6;
  height: 44px;
  line-height: 42px;
  padding: 10px 15px;
  width: 100%; }
  .composer-modal__input:-ms-input-placeholder {
    color: #a7a8a9; }
  .composer-modal__input::placeholder {
    color: #a7a8a9; }
  .composer-modal__input:focus {
    outline: 1px solid #009dc4; }

.composer-modal__action {
  text-align: right;
  margin-top: 30px; }

.composer-modal .btn-add-composer {
  display: initial;
  max-width: 400px;
  margin: 0 auto;
  font-size: 18px;
  font-weight: 400; }

.composer-modal .btn-cancel-composer {
  display: initial;
  margin-top: 20px;
  margin-bottom: 15px;
  font-weight: 400; }

.composer-modal__list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  margin-top: 30px; }
  .composer-modal__list.searching .user-item {
    display: none; }
    .composer-modal__list.searching .user-item.search-found {
      display: block; }

.composer-modal__not-found, .composer-modal__existed {
  display: none;
  padding: 25px 10px;
  text-align: center; }
  .composer-modal__not-found.active, .composer-modal__existed.active{
    display: block; }

.gallery-new {
  margin: 50px 0;
  text-align: center; }
  .gallery-new .srm-icon-wrap {
    background-color: #53565a;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center; }
  .gallery-new .srm-add {
    background-color: #fff; }
  .gallery-new__btn {
    display: inline-block; }
    .gallery-new__btn:hover {
      cursor: pointer; }
  .gallery-new__desc {
    margin-top: 10px; }

.composer-new {
  padding: 10px 20px; }
  .composer-new__title {
    font-size: 20px;
    color: #333; }
  .composer-new__form {
    margin-top: 40px; }
  .composer-new__action {
    margin-top: 30px; }
  .composer-new .form-group label {
    color: #333; }
  .composer-new .form-control {
    height: 36px;
    line-height: 36px; }
  .composer-new .btn-new-composer {
    display: initial;
    max-width: 400px;
    margin: 0 auto;
    font-size: 18px;
    font-weight: 400; }
  .composer-new .btn-cancel-composer {
    display: initial;
    margin-top: 20px;
    margin-bottom: 15px;
    font-weight: 400; }

.srm-icon {
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  background-color: #707070; }

.srm-delete {
  -webkit-mask-image: url("../images/icon-delete_1.svg");
  mask-image: url("../images/icon-delete.svg");
  width: 24px;
  height: 24px; }

.srm-launch {
  -webkit-mask-image: url("../images/icon-launch.svg");
  mask-image: url("../images/icon-launch.svg");
  width: 24px;
  height: 24px; }

.srm-add {
  -webkit-mask-image: url("../images/icon-adds.svg");
  mask-image: url("../images/icon-adds.svg");
  width: 24px;
  height: 24px; }

.list-search__reload {
  width: 24px;
  height: 24px;
  background-color: #53565a;
  background-repeat: no-repeat;
  -webkit-mask-image: url("../images/icon-reload.svg");
  mask-image: url("../images/icon-reload.svg");
  -webkit-mask-size: cover;
  mask-size: cover;
  margin-left: auto;
  margin-right: calc(30% - 75px);
}

.list-search__reload:hover {
  background-color: #009dc4;
  cursor: pointer;
}

.list-artist {
  margin-top: 10px;
}

.list-artist__title {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.list-artist__item {
  flex: 0 0 128px;
  margin: 0 10px;
}

.list-artist__item:hover {
  cursor: pointer;
}

@media screen and (max-width: 1440px) {
  .list-artist__item {
    flex: 0 0 110px;
  }
}

@media screen and (max-width: 767px) {
  .list-artist__item {
    flex: 0 0 90px;
  }
}

.list-artist__item:first-child {
  margin-left: 0;
}

.list-artist__item:last-child {
  margin-right: 0;
}

.list-artist .scrollbar-horizontal {
  margin-left: 6%;
  padding-bottom: 10px;
}

.list-artist .scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  margin: 0;
}

.list-artist__list {
  display: flex;
  flex-wrap: nowrap;
}

.list-artist__avatar {
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 50%;
}

.list-artist__avatar-img {
  width: 100%;
  border-radius: 50%;
}

.list-artist__label {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #009dc4;
  color: #fff;
  border-radius: 23px;
  font-size: 10px;
  padding: 0 5px;
  text-transform: uppercase;
}

@media screen and (max-width: 767px) {
  .list-artist__label {
    font-size: 8px;
  }
}

.list-artist__info {
  text-align: center;
  margin: 8px 0;
}

.list-artist__name {
  font-size: 16px;
  line-height: 20px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word;
  margin-bottom: 0;
}

@media screen and (max-width: 1440px) {
  .list-artist__name {
    font-size: 14px;
  }
}

.list-artist__work {
  font-size: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word;
  color: #a7a8a9;
}

@media screen and (max-width: 1440px) {
  .list-artist__work {
    font-size: 10px;
  }
}

.list-search__item-playpause {
    height: 100%;
    width: 100%;
    max-height: 144px;
    background-image: url('../images/icon_play.svg');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 64px;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    border-radius: 0;
    cursor: pointer;
    mix-blend-mode: screen;
}


.list-circle__component .list-search__item-playpause {
  height: 24px;
  width: 24px;
  top: 0;
  left: 0;
}

.gallery__item[data-file-type='audio'] .list-search__item-playpause:hover {
  opacity: 1;
}

.gallery__item:hover {
  box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.05);
}

.list-new-works__sub-item .gallery__item[data-file-type='audio'] .list-search__item-playpause:hover {
  opacity: 0;
}

.playing-navi.gallery__item[data-file-type='audio'] .list-search__item-playpause {
    background-image: url('../images/icon_pause.svg');
    opacity: 1;
}

.toast-info {
    background: #ffffff;
    text-align: end;
    color: #333333 !important;
    min-width: 200px;
    width: auto !important;
}


.creator-list-chapter-title-edit {
    display: none;
}

.creator-list_header {
    position: relative;
    cursor: grab;
}

.creator-list_header:focus {
    cursor: move;
}

.creator-list-chapter-title-edit button {
    width: 50%;
    background: white;
}

.creator-list-chapter-title-edit button:hover {
    width: 50%;
    background: #d9d9d9;
}

.creator-list-chapter-title-edit button img {
    width: 15px;
    margin: auto;
    filter: invert(87%) sepia(0%) saturate(5%) hue-rotate(352deg) brightness(104%) contrast(87%);
}

.creator-list-chapter-title-edit button:hover img {
    filter: invert(94%) sepia(61%) saturate(0%) hue-rotate(189deg) brightness(106%) contrast(106%);
}

.creator-list_header:hover .creator-list-chapter-title-edit  {
    display: flex;
    left: 10px;
    top: 30px;
}

.creator-list_header:hover .creator-list-chapter-title-edit .edit-creator-list {
    border-radius: 10px 0 0 10px;
}

.creator-list_header:hover .creator-list-chapter-title-edit .edit-creator-list img {
    position: absolute;
    top: 9%;
    left: 16%;
}

.creator-list_header:hover .creator-list-chapter-title-edit .delete-creator-list {
    border-radius: 0 10px 10px 0;
}

.creator-list_header:hover .creator-list-chapter-title-edit .delete-creator-list img {
    position: absolute;
    top: 9%;
    right: 14%;
}

.creator-list-chapter-title-edit {
    width: 60px;
    height: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 10px;
    position: absolute;
    /*right: 10px;*/
    /*bottom: -10px;*/
    background: white;
}

/* New gallery */
.gallery__new-works-container {
  width: 100%;
  height: auto;
  margin: 64px 0px 90px;
}

.gallery__title-new-works__container {
  display: flex;
  cursor: grab;
}

.gallery__title-new-works__action {
  display: none;
  align-content: center;
  align-items: center;
}

.gallery__title-new-works__container:hover .gallery__title-new-works__action {
  display: flex;
}

.gallery__new-works__sub-component {
  margin-bottom: 32px;
}

.delete-work-title {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-style: normal;
  font-weight: 300;
  font-size: 13px;
  line-height: 150%;
  color: #000000;
  letter-spacing: 0.168824px;
  margin-bottom: 32px;
}

.list-search-work {
  margin-top: 32px;
  display: flex;
  max-width: 100%;
  overflow-x: auto;
}

.gallery__title-new-works__action span {
  width: 22px;
  height: 22px;
  background-color: rgba(167, 168, 169, 0.2);
  border-radius: 50%;
  color: #a7a8a9;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 6px;
}

.gallery__title-new-works__action span:hover {
  background-color: #009ace;
  color: #FFFFFF;
}

.gallery__title-new-works {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 18px;
  line-height: 150%;
  letter-spacing: -0.245455px;
  font-weight: 400;
  color: #000000;
  margin-bottom: 4px;
  word-break: break-word;
}

.gallery__sub-title-new-works {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 11px;
  line-height: 150%;
  letter-spacing: 0.144706px;
  font-weight: 400;
  color: #000000;
  margin-bottom: 16px;
  word-break: break-word;
}

.gallery__list-new-works {
  max-width: 100%;
  overflow-x: auto;
  display: flex;
  /* padding-bottom: 5px; */
  padding-bottom: 30px;
  overflow-y: hidden;
  width: fit-content;
}

.list-new-works__item-container {
  margin-right: 16px;
  width: auto;
  position: relative;
  flex: 0 0 144px;
}

.list-new-works__media {
  width: 144px;
  height: 144px;
  background: #C4C4C4;
  padding: 0px !important;
  margin: 0px !important;
  background-size: cover;
  border-radius: 6px;
}

.list-new-works__media[data-file-type="movie"] {
  width: 256px;
}

.list-new-works__content {
  cursor: default;
  max-width: 144px;
  background: #FFFFFF;
  cursor: pointer;
}

.list-new-works__media[data-file-type="movie"]+.list-new-works__content {
  max-width: 256px;
}

.list-new-works__title {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-weight: 400;
  font-size: 13px;
  line-height: 150%;
  letter-spacing: 0.168824px;
  color: #000000;
  margin-top: 2px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-new-works__sub-title, .list-new-works__hint {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 11px;
  line-height: 150%;
  font-weight: 400;
  letter-spacing: 0.144706px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 2px;
}

.list-new-works__sub-title {
  color: #000000;
}
.list-new-works__hint {
  color: #979797;
}

.label-budget {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 13px;
  line-height: 150%;
  letter-spacing: 0.168824px;
  color: #000000 !important;
  font-weight: 400;
}

.contact__jp-astarisk-op {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 8px;
  line-height: 150%;
  text-align: center;
  letter-spacing: 0.144706px;
  color: #A7A8A9;
  margin-left: 4px;
}

.contact__jp-astarisk {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 8px;
  line-height: 150%;
  text-align: center;
  letter-spacing: 0.144706px;
  color: #009ACE;
  margin-left: 4px;
}

#modal-contact-artist input[name='budget'] {
  width: 50%;
}

#modal-contact-artist textarea[name='description'], #modal-contact-artist-profile textarea[name='description'] {
  padding: 12px 12px 12px 16px;
  border: 1px solid #F0F0F0;
  box-sizing: border-box;
  border-radius: 4px;
  height: auto;
  color: #000000;
  max-width: 480px;
  max-height: 104px;
}

#modal-contact-artist textarea[name='description']:focus,
#modal-contact-artist-profile textarea[name='description']:focus{
  border: 1px solid #D3D3D3 !important;
  outline: none !important;
}

#modal-contact-artist-profile textarea {
  max-width: 100% !important;
}

#modal-contact-artist-profile #uploadFile,
#modal-contact-artist-profile .mattach-previews {
  max-width: calc(100% - 25px) !important;
}

.budget-input-container {
  width: 100%;
  display: flex;
  align-items: center;
}

.budget-unit {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-weight: 400;
  font-size: 13px;
  line-height: 150%;
  letter-spacing: 0.168824px;
  margin-left: 4px;
  color: #000000;
}

#modal-contact-artist .form-group label,
#modal-contact-artist-profile .form-group label{
  width: 100%;
  margin-bottom: 32px;
}

#modal-contact-artist .popup-content,
#modal-contact-artist-profile .popup-content{
  padding: 32px;
}

#modal-contact-artist .list-new-works__content {
  cursor: default;
}

#modal-contact-artist .popup-footer, #modal-contact-artist-profile .popup-footer {
  border-top: 1px solid #F0F0F0;
  padding-top: 16px;
}

.list-new-works__sub-item {
  margin-bottom: 32px;
}

.gallery__title-topics {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 24px;
  line-height: 150%;
  letter-spacing: -0.327273px;
  margin-bottom: 4px;
  color: #000000;
}

.gallery__sub-title-topics {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-size: 11px;
  line-height: 150%;
  letter-spacing: 0.144706px;
  margin-bottom: 38px;
  color: #000000;
}

.gallery__list-topics {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  padding-bottom: 35px;
  margin-left: 0;
}

.gallery__list-topics:not(.splide) .splide__list {
   overflow-x: scroll;
   overflow-y: hidden;
}

.list-topics__topics-container {
  background: #FFFFFF;
  box-sizing: border-box;
  border-radius: 12px;
  margin-right: 56px;
  position: relative;
  margin-bottom: 24px;
  flex: 0 0 339px;
  height: 480px;
  width: 339px;
}
.splide__track--nav>.splide__list>.splide__slide,
.splide__track--nav>.splide__list>.splide__slide.is-active {
  border: none !important;
}

.list-topics__topics-container:not(:first-child) {
  margin-left: 8px;
}

.list-topics__topics-container.splide__slide:hover {
  cursor: pointer;
  box-shadow: 1px 2px 12px rgba(100, 100, 100, 0.1);
}

.list-topics__content-top {
  width: 339px;
  height: 480px;
  border-radius: 12px;
  position: relative;
  border: 1px solid #F0F0F0;
  background-color: #f0f0f0;
  background-size: 101%;
  background-repeat: no-repeat;
  background-position: center;
  transition: 0.3s ease-out;
}

.list-topics__content-top:hover {
  background-size: 110%;
  transition: 0.3s ease-out;
}

.topic-setting {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
}

.topic-setting .icon--sicon-settings {
  color: #A7A8A9;
  font-size: 22px;
  cursor: pointer;
}

.topic-setting .icon--sicon-settings:hover {
  color: #0076A5;
}

.list-topics__content-top img {
  border-radius: 12px 12px 0px 0px;
  width: 100%;
}

.list-topics__content-bottom {
  padding: 24px;
  border-radius: 0px 0px 12px 12px;
  border: 1px solid #F0F0F0;
  border-top: none;
  display: none;
}

.list-topics__content-bottom__title {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 18px;
  line-height: 150%;
  letter-spacing: -0.245455px;
  margin-bottom: 16px;
  color: #000000;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-topics__content-bottom__title span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.list-topics__content-bottom__sub-title {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    margin-bottom: 15px;
    color: #000000;
    height: 60px;
    overflow: auto;
    word-break: break-word;
}

.list-topics__content-bottom__list-circle {
  display: flex;
  flex-flow: wrap row;
  height: 57px;
  overflow-y: auto;
  margin-bottom: 4px;
}

.list-circle__component {
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid #ffffff !important;
  border-radius: 50%;
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0px;
}

.list-circle__component.last-selected {
  border: 1px solid #009ace !important;
}

.list-topics__content-bottom__list-circle .list-circle__component.last-selected {
  border: 1px solid transparent !important;
}

.list-topics__content-bottom__list-circle .list-circle__component.playing,
.list-topics__content-bottom__list-circle .list-circle__component.loading {
  border: 1px solid #009ace !important;
}

.list-circle__sub-component {
  width: 26px;
  height: 26px;
  background: transparent;
  border-radius: 50%;
  background-size: cover;
  cursor: pointer;
  border: 1px solid #FFFFFF;
}

.list-topics__content-bottom__button {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.gallery__btn-move, .gallery__btn-delete {
  font-size: 18px;
  color:  #A7A8A9;
}

.gallery__topics-actions {
  position: absolute;
  top: 100%;
  right: 0;
  display: none;
}

.gallery__new-works-actions {
  position: absolute;
  top: 100%;
  right: 0;
  display: none;
}

.gallery__topics-actions .gallery__btn-move, .gallery__topics-actions .gallery__btn-delete {
  margin-left: 15px;
}

.gallery__new-works-actions .gallery__btn-move, .gallery__new-works-actions .gallery__btn-delete {
  margin-left: 15px;
}

.gallery__topics-actions .gallery__btn-move:hover, .gallery__topics-actions .gallery__btn-delete:hover {
  cursor: pointer;
  color: #0076A5;
}

.gallery__new-works-actions .gallery__btn-move:hover, .gallery__new-works-actions .gallery__btn-delete:hover {
  cursor: pointer;
  color: #0076A5;
}

.list-topics__topics-container:not(.cannot-check):hover .gallery__topics-actions {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.list-new-works__item-container:not(.cannot-check):hover .gallery__new-works-actions {
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.list-new-works__button-add-work {
  width: 144px;
  border: 1px solid #F0F0F0;
  box-sizing: border-box;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 144px;
  margin-right: 16px;
  min-height: 202px;
}

.list-new-works__button-add-work__content {
  width: 100%;
  text-align: center;
  color: #A7A8A9;
}

.list-new-works__button-add-work:hover, .list-new-works__button-add-work-theme:hover {
  color: #FFFFFF;
  background: #A7A8A9;
  cursor: pointer;
}

.list-new-works__button-add-work:hover .list-new-works__button-add-work__content,
.list-new-works__button-add-work-theme:hover .list-new-works__button-add-work-theme__content {
  color: #FFFFFF;
  background: #A7A8A9;
  cursor: pointer;
}

.list-new-works__button-add-work__content i, .list-new-works__button-add-work-theme__content i {
  font-size: 20px;
  margin-bottom: 4px;
}

.list-new-works__button-add-work__content p, .list-new-works__button-add-work-theme__content p {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-weight: 400;
  font-size: 13px;
  line-height: 150%;
  letter-spacing: 0.168824px;
}

.gallery__topics-container {
  padding-top: 32px;
}

.list-new-works__button-add-work-theme {
  border: 1px solid #F0F0F0;
  box-sizing: border-box;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-content: center;
  height: 144px;
  width: 100%;
  background: #FFFFFF;
  margin-top: 16px;
}

.list-new-works__button-add-work-theme__content {
  text-align: center;
  background: #FFFFFF;
  color: #A7A8A9;
  margin: auto;
}

.list-topics__button-add-topic {
  border: 1px solid #F0F0F0;
  box-sizing: border-box;
  border-radius: 12px;
  width: 339px;
  min-width: 339px;
  height: 480px;
  display: flex;
  justify-content: center;
  align-content: center;
  margin-right: 16px;
  background: #FFFFFF;
  color: #fff;
  transition: 0.1s ease;
  box-shadow: 2px 4px 8px rgb(0 0 0 / 5%);
}

.list-topics__button-add-topic__content {
  width: 100%;
  text-align: center;
  margin: auto;
}

.list-topics__button-add-topic__content i {
  font-size: 20px;
  margin-bottom: 4px;
}

.list-topics__button-add-topic__content p {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN';
  font-weight: 400;
  font-size: 8px;
  line-height: 150%;
  letter-spacing: 0.168824px;
  color: #000;
}

.list-topics__button-add-topic:hover {
  cursor: pointer;
  background: #A7A8A9;
  color: #FFFFFF;
}

.list-topics__button-add-topic:hover {
  background: #009ace;
  color: #FFFFFF;
}

.list-topics__button-add-topic:hover .list-topics__button-add-topic__content p {
  color: #fff;
}

.label_field_new-work {
  width: 100%;
  margin-bottom: 32px !important;
}

.new-work_label {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
  font-size: 13px;
  line-height: 150%;
  letter-spacing: 0.168824px;
  color: #000000;
  margin-bottom: 4px;
}

#modal-add-new-work-theme .form-control {
  width: 100%;
}


.list-new-works__media {
  position: relative;
}

.checkbox-container{
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: 100%;
}

#modal-add-sub-work .checkmark {
  position: absolute;
  top: 5px !important;
  left: auto;
  right: 5px;
  background-color: #C4C4C4;
  border: 2px solid #FFFFFF;
}

#modal-add-sub-work .checkbox-container:hover input ~ .checkmark {
  background-color: #ccc;
}

#modal-add-sub-work .checkbox-container input:checked ~ .checkmark {
  background-color: #FFFFFF;
}

#modal-add-sub-work .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

#modal-add-sub-work .checkbox-container input:checked ~ .checkmark:after {
  display: block;
}

#modal-add-sub-work .checkbox-container .checkmark:after {
  left: 4px;
  top: 0px;
  width: 5px;
  height: 10px;
  border: solid #C4C4C4;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

#modal-add-sub-work .list-new-works__media {
  cursor: pointer;
}

.gallery__content-container {
  padding-top: 32px;
}

#modal-add-sub-work .popup-close, #modal-search-sale-content-selection .popup-close {
  top: 7px;
  right: 7px
}

.js-crop-and-upload-project {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN';
}
/* End new gallery */

.disabledbutton {
    pointer-events: none;
}

#toast-container.toast-top-right .toast-title,
#toast-container.toast-top-right .toast-message {
  max-width: 280px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

@media screen and (max-width: 767px) {
  .list-topics__button-add-topic {
    width: 226px;
    min-width: 226px;
    height: 320px;
  }

  .list-topics__topics-container {
    width: 226px;
    height: 320px;
    flex: 0 0 226px;
  }

  .list-topics__content-top {
    width: 226px;
    height: 320px;
  }
}
