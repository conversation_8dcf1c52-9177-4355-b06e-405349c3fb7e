.statement {
    background-color: #fff;
    /* width: 100%; */
    display: flex;
    justify-content: center;
    position: relative;
}

.statement:is(.new, .editable) {
    cursor: pointer;
}

.statement-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: auto;
    max-width: 100%;
    min-height: 210px;
    justify-content: center;
}

.statement-main {
    display: flex;
    flex-direction: row;
    width: 100%;
    max-width: 100%;
    max-height: 100%;
    justify-content: center;
    align-items: center;
}

.statement-avatar {
    width: 112px;
    height: 112px;
    margin-right: 20px;
}

.statement.no-content .statement-main,
.statement.no-content .statement-info {
    display: none;
}

.statement.no-content .statement-container {
    height: 0 !important;
    min-height: 0;
}

.statement.no-content:is(.new, .editable) .statement-container {
    height: 210px !important;
    padding: 50px 0 40px;
}

.statement-main.no-avatar .statement-avatar {
    display: none;
}

.statement-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.statement-quote {
    width: auto;
    max-width: calc(100% - 124px);
    min-width: 160px;
}

.no-avatar .statement-quote {
    max-width: 100%;
}

.statement-main.no-avatar .statement-quote p {
    font-size: clamp(0.5rem, -0.409rem + 4.55vw, 3rem);
}

.statement-quote p {
    line-height: 200%;
    letter-spacing: 2.5px;
    margin: 0;
    font-weight: 300;
    color: #000000;
    white-space: nowrap;
}

.statement-info {
    margin-top: 48px;
    max-width: 100%;
    width: auto;
    align-self: flex-end;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    position: relative;
}

.statement-info-name {
    margin-right: 16px;
    line-height: 200%;
    max-width: calc(8vw + 80px);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.statement-info-title {
    line-height: 200%;
    max-width: calc(8vw + 80px);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}


.statement-info:before {
    content: " ";
    position: absolute;
    left: calc(-40px - 1vw);
    height: 1px;
    background-color: #a7a8a9;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
}

.statement-info.off:before {
    display: none;
}

@media (max-width: 767px) {
    .statement.container {
        padding: 0 !important;;
    }

    .statement-avatar {
        width: 48px;
        height: 48px;
        margin-right: 8px;
    }

    .statement-container {
        padding: 0 8px;
    }

    .statement-quote {
        max-width: calc(100% - 60px);
    }

    .statement-info {
        max-width: calc(100% - 40px);
    }

    .statement-info-name {
        max-width: none;
        min-width: 40px;
    }
    
    .statement-info-title {
        max-width: none;
        min-width: 40px;
    }
}

#modal_profile_statement .custom-switch:not(:last-child) {
    margin-bottom: 16px;
}
