body .sheader.d-block-header {
    display: block;
}

body main.owner-top {
    margin-top: 64px;
}

.container-new {
    padding: 0;
}

.pdf-component {
    border: none;
}

.scene-detail-share .mmessage.mmessage--received.clicked .messenger-content.messenger-content-file {
    display: block
}

@media (max-width: 768px) {
    /*for SP*/
    .pd-section__content .block-content-scene {
        padding-top: 0;
    }

    .project-item.active.on-mobile {
        min-height: auto;
    }
}

@media (min-width: 768px) {
    /*for PC*/
    .line-header-share {
        min-height: 1px;
        background-color: #F0F0F0;
        width: 100%;
    }

    .block-content-scene .pd-comment {
        margin-top: 0;
    }

    .cscene__version.slick-slide.slick-current.slick-active .ccscene__thumb.scene-type-document.active {
        top: 50%;
    }

    body main.owner-top {
        margin-top: 64px;
    }
}

.container.container-new, .project-list, .new-video-menu, .project-item.active, .project-item__content, .project-tab.project-tab-progress, .pd-section.pd-section--detail.pd-scene-title-detail {
    height: 100% !important;
}

.new-video-menu {
    height: 100% !important;
}

.mmessage--sent.clicked .mmessage-info .message-info-container .mmessage-status .mmessage-user .notification.notification--outline-gray.notification--round  {
  margin-left: 2px
}