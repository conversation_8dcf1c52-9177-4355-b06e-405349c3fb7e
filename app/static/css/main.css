/* 01EG89H6SS2141VNGDDEHBMV4Q */
@charset "UTF-8";
/* @import url("https://fonts.googleapis.com/earlyaccess/notosansjapanese.css"); */
/*@import "modal.css";*/
/*!
 * Font Awesome Free 5.4.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
.fa,
.fas,
.far,
.fal,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1; }

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em; }

.fa-xs {
  font-size: .75em; }

.fa-sm {
  font-size: .875em; }

.fa-1x {
  font-size: 1em; }

.fa-2x {
  font-size: 2em; }

.fa-3x {
  font-size: 3em; }

.fa-4x {
  font-size: 4em; }

.fa-5x {
  font-size: 5em; }

.fa-6x {
  font-size: 6em; }

.fa-7x {
  font-size: 7em; }

.fa-8x {
  font-size: 8em; }

.fa-9x {
  font-size: 9em; }

.fa-10x {
  font-size: 10em; }

.fa-fw {
  text-align: center;
  width: 1.25em; }

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0; }
  .fa-ul > li {
    position: relative; }

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit; }

.fa-border {
  border: solid 0.08em #eee;
  border-radius: .1em;
  padding: .2em .25em .15em; }

.fa-pull-left {
  float: left; }

.fa-pull-right {
  float: right; }

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: .3em; }

.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: .3em; }

.fa-spin {
  animation: fa-spin 2s infinite linear; }

.fa-pulse {
  animation: fa-spin 1s infinite steps(8); }

@keyframes fa-spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

.fa-rotate-90 {
  transform: rotate(90deg); }

.fa-rotate-180 {
  transform: rotate(180deg); }

.fa-rotate-270 {
  transform: rotate(270deg); }

.fa-flip-horizontal {
  transform: scale(-1, 1); }

.fa-flip-vertical {
  transform: scale(1, -1); }

.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1); }

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  filter: none; }

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2em; }

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%; }

.fa-stack-1x {
  line-height: inherit; }

.fa-stack-2x {
  font-size: 2em; }

.fa-inverse {
  color: #fff; }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-500px:before {
  content: '\f26e'; }

.fa-accessible-icon:before {
  content: '\f368'; }

.fa-accusoft:before {
  content: '\f369'; }

.fa-acquisitions-incorporated:before {
  content: '\f6af'; }

.fa-ad:before {
  content: '\f641'; }

.fa-address-book:before {
  content: '\f2b9'; }

.fa-address-card:before {
  content: '\f2bb'; }

.fa-adjust:before {
  content: '\f042'; }

.fa-adn:before {
  content: '\f170'; }

.fa-adversal:before {
  content: '\f36a'; }

.fa-affiliatetheme:before {
  content: '\f36b'; }

.fa-air-freshener:before {
  content: '\f5d0'; }

.fa-algolia:before {
  content: '\f36c'; }

.fa-align-center:before {
  content: '\f037'; }

.fa-align-justify:before {
  content: '\f039'; }

.fa-align-left:before {
  content: '\f036'; }

.fa-align-right:before {
  content: '\f038'; }

.fa-alipay:before {
  content: '\f642'; }

.fa-allergies:before {
  content: '\f461'; }

.fa-amazon:before {
  content: '\f270'; }

.fa-amazon-pay:before {
  content: '\f42c'; }

.fa-ambulance:before {
  content: '\f0f9'; }

.fa-american-sign-language-interpreting:before {
  content: '\f2a3'; }

.fa-amilia:before {
  content: '\f36d'; }

.fa-anchor:before {
  content: '\f13d'; }

.fa-android:before {
  content: '\f17b'; }

.fa-angellist:before {
  content: '\f209'; }

.fa-angle-double-down:before {
  content: '\f103'; }

.fa-angle-double-left:before {
  content: '\f100'; }

.fa-angle-double-right:before {
  content: '\f101'; }

.fa-angle-double-up:before {
  content: '\f102'; }

.fa-angle-down:before {
  content: '\f107'; }

.fa-angle-left:before {
  content: '\f104'; }

.fa-angle-right:before {
  content: '\f105'; }

.fa-angle-up:before {
  content: '\f106'; }

.fa-angry:before {
  content: '\f556'; }

.fa-angrycreative:before {
  content: '\f36e'; }

.fa-angular:before {
  content: '\f420'; }

.fa-ankh:before {
  content: '\f644'; }

.fa-app-store:before {
  content: '\f36f'; }

.fa-app-store-ios:before {
  content: '\f370'; }

.fa-apper:before {
  content: '\f371'; }

.fa-apple:before {
  content: '\f179'; }

.fa-apple-alt:before {
  content: '\f5d1'; }

.fa-apple-pay:before {
  content: '\f415'; }

.fa-archive:before {
  content: '\f187'; }

.fa-archway:before {
  content: '\f557'; }

.fa-arrow-alt-circle-down:before {
  content: '\f358'; }

.fa-arrow-alt-circle-left:before {
  content: '\f359'; }

.fa-arrow-alt-circle-right:before {
  content: '\f35a'; }

.fa-arrow-alt-circle-up:before {
  content: '\f35b'; }

.fa-arrow-circle-down:before {
  content: '\f0ab'; }

.fa-arrow-circle-left:before {
  content: '\f0a8'; }

.fa-arrow-circle-right:before {
  content: '\f0a9'; }

.fa-arrow-circle-up:before {
  content: '\f0aa'; }

.fa-arrow-down:before {
  content: '\f063'; }

.fa-arrow-left:before {
  content: '\f060'; }

.fa-arrow-right:before {
  content: '\f061'; }

.fa-arrow-up:before {
  content: '\f062'; }

.fa-arrows-alt:before {
  content: '\f0b2'; }

.fa-arrows-alt-h:before {
  content: '\f337'; }

.fa-arrows-alt-v:before {
  content: '\f338'; }

.fa-assistive-listening-systems:before {
  content: '\f2a2'; }

.fa-asterisk:before {
  content: '\f069'; }

.fa-asymmetrik:before {
  content: '\f372'; }

.fa-at:before {
  content: '\f1fa'; }

.fa-atlas:before {
  content: '\f558'; }

.fa-atom:before {
  content: '\f5d2'; }

.fa-audible:before {
  content: '\f373'; }

.fa-audio-description:before {
  content: '\f29e'; }

.fa-autoprefixer:before {
  content: '\f41c'; }

.fa-avianex:before {
  content: '\f374'; }

.fa-aviato:before {
  content: '\f421'; }

.fa-award:before {
  content: '\f559'; }

.fa-aws:before {
  content: '\f375'; }

.fa-backspace:before {
  content: '\f55a'; }

.fa-backward:before {
  content: '\f04a'; }

.fa-balance-scale:before {
  content: '\f24e'; }

.fa-ban:before {
  content: '\f05e'; }

.fa-band-aid:before {
  content: '\f462'; }

.fa-bandcamp:before {
  content: '\f2d5'; }

.fa-barcode:before {
  content: '\f02a'; }

.fa-bars:before {
  content: '\f0c9'; }

.fa-baseball-ball:before {
  content: '\f433'; }

.fa-basketball-ball:before {
  content: '\f434'; }

.fa-bath:before {
  content: '\f2cd'; }

.fa-battery-empty:before {
  content: '\f244'; }

.fa-battery-full:before {
  content: '\f240'; }

.fa-battery-half:before {
  content: '\f242'; }

.fa-battery-quarter:before {
  content: '\f243'; }

.fa-battery-three-quarters:before {
  content: '\f241'; }

.fa-bed:before {
  content: '\f236'; }

.fa-beer:before {
  content: '\f0fc'; }

.fa-behance:before {
  content: '\f1b4'; }

.fa-behance-square:before {
  content: '\f1b5'; }

.fa-bell:before {
  content: '\f0f3'; }

.fa-bell-slash:before {
  content: '\f1f6'; }

.fa-bezier-curve:before {
  content: '\f55b'; }

.fa-bible:before {
  content: '\f647'; }

.fa-bicycle:before {
  content: '\f206'; }

.fa-bimobject:before {
  content: '\f378'; }

.fa-binoculars:before {
  content: '\f1e5'; }

.fa-birthday-cake:before {
  content: '\f1fd'; }

.fa-bitbucket:before {
  content: '\f171'; }

.fa-bitcoin:before {
  content: '\f379'; }

.fa-bity:before {
  content: '\f37a'; }

.fa-black-tie:before {
  content: '\f27e'; }

.fa-blackberry:before {
  content: '\f37b'; }

.fa-blender:before {
  content: '\f517'; }

.fa-blender-phone:before {
  content: '\f6b6'; }

.fa-blind:before {
  content: '\f29d'; }

.fa-blogger:before {
  content: '\f37c'; }

.fa-blogger-b:before {
  content: '\f37d'; }

.fa-bluetooth:before {
  content: '\f293'; }

.fa-bluetooth-b:before {
  content: '\f294'; }

.fa-bold:before {
  content: '\f032'; }

.fa-bolt:before {
  content: '\f0e7'; }

.fa-bomb:before {
  content: '\f1e2'; }

.fa-bone:before {
  content: '\f5d7'; }

.fa-bong:before {
  content: '\f55c'; }

.fa-book:before {
  content: '\f02d'; }

.fa-book-dead:before {
  content: '\f6b7'; }

.fa-book-open:before {
  content: '\f518'; }

.fa-book-reader:before {
  content: '\f5da'; }

.fa-bookmark:before {
  content: '\f02e'; }

.fa-bowling-ball:before {
  content: '\f436'; }

.fa-box:before {
  content: '\f466'; }

.fa-box-open:before {
  content: '\f49e'; }

.fa-boxes:before {
  content: '\f468'; }

.fa-braille:before {
  content: '\f2a1'; }

.fa-brain:before {
  content: '\f5dc'; }

.fa-briefcase:before {
  content: '\f0b1'; }

.fa-briefcase-medical:before {
  content: '\f469'; }

.fa-broadcast-tower:before {
  content: '\f519'; }

.fa-broom:before {
  content: '\f51a'; }

.fa-brush:before {
  content: '\f55d'; }

.fa-btc:before {
  content: '\f15a'; }

.fa-bug:before {
  content: '\f188'; }

.fa-building:before {
  content: '\f1ad'; }

.fa-bullhorn:before {
  content: '\f0a1'; }

.fa-bullseye:before {
  content: '\f140'; }

.fa-burn:before {
  content: '\f46a'; }

.fa-buromobelexperte:before {
  content: '\f37f'; }

.fa-bus:before {
  content: '\f207'; }

.fa-bus-alt:before {
  content: '\f55e'; }

.fa-business-time:before {
  content: '\f64a'; }

.fa-buysellads:before {
  content: '\f20d'; }

.fa-calculator:before {
  content: '\f1ec'; }

.fa-calendar:before {
  content: '\f133'; }

.fa-calendar-alt:before {
  content: '\f073'; }

.fa-calendar-check:before {
  content: '\f274'; }

.fa-calendar-minus:before {
  content: '\f272'; }

.fa-calendar-plus:before {
  content: '\f271'; }

.fa-calendar-times:before {
  content: '\f273'; }

.fa-camera:before {
  content: '\f030'; }

.fa-camera-retro:before {
  content: '\f083'; }

.fa-campground:before {
  content: '\f6bb'; }

.fa-cannabis:before {
  content: '\f55f'; }

.fa-capsules:before {
  content: '\f46b'; }

.fa-car:before {
  content: '\f1b9'; }

.fa-car-alt:before {
  content: '\f5de'; }

.fa-car-battery:before {
  content: '\f5df'; }

.fa-car-crash:before {
  content: '\f5e1'; }

.fa-car-side:before {
  content: '\f5e4'; }

.fa-caret-down:before {
  content: '\f0d7'; }

.fa-caret-left:before {
  content: '\f0d9'; }

.fa-caret-right:before {
  content: '\f0da'; }

.fa-caret-square-down:before {
  content: '\f150'; }

.fa-caret-square-left:before {
  content: '\f191'; }

.fa-caret-square-right:before {
  content: '\f152'; }

.fa-caret-square-up:before {
  content: '\f151'; }

.fa-caret-up:before {
  content: '\f0d8'; }

.fa-cart-arrow-down:before {
  content: '\f218'; }

.fa-cart-plus:before {
  content: '\f217'; }

.fa-cat:before {
  content: '\f6be'; }

.fa-cc-amazon-pay:before {
  content: '\f42d'; }

.fa-cc-amex:before {
  content: '\f1f3'; }

.fa-cc-apple-pay:before {
  content: '\f416'; }

.fa-cc-diners-club:before {
  content: '\f24c'; }

.fa-cc-discover:before {
  content: '\f1f2'; }

.fa-cc-jcb:before {
  content: '\f24b'; }

.fa-cc-mastercard:before {
  content: '\f1f1'; }

.fa-cc-paypal:before {
  content: '\f1f4'; }

.fa-cc-stripe:before {
  content: '\f1f5'; }

.fa-cc-visa:before {
  content: '\f1f0'; }

.fa-centercode:before {
  content: '\f380'; }

.fa-certificate:before {
  content: '\f0a3'; }

.fa-chair:before {
  content: '\f6c0'; }

.fa-chalkboard:before {
  content: '\f51b'; }

.fa-chalkboard-teacher:before {
  content: '\f51c'; }

.fa-charging-station:before {
  content: '\f5e7'; }

.fa-chart-area:before {
  content: '\f1fe'; }

.fa-chart-bar:before {
  content: '\f080'; }

.fa-chart-line:before {
  content: '\f201'; }

.fa-chart-pie:before {
  content: '\f200'; }

.fa-check:before {
  content: '\f00c'; }

.fa-check-circle:before {
  content: '\f058'; }

.fa-check-double:before {
  content: '\f560'; }

.fa-check-square:before {
  content: '\f14a'; }

.fa-chess:before {
  content: '\f439'; }

.fa-chess-bishop:before {
  content: '\f43a'; }

.fa-chess-board:before {
  content: '\f43c'; }

.fa-chess-king:before {
  content: '\f43f'; }

.fa-chess-knight:before {
  content: '\f441'; }

.fa-chess-pawn:before {
  content: '\f443'; }

.fa-chess-queen:before {
  content: '\f445'; }

.fa-chess-rook:before {
  content: '\f447'; }

.fa-chevron-circle-down:before {
  content: '\f13a'; }

.fa-chevron-circle-left:before {
  content: '\f137'; }

.fa-chevron-circle-right:before {
  content: '\f138'; }

.fa-chevron-circle-up:before {
  content: '\f139'; }

.fa-chevron-down:before {
  content: '\f078'; }

.fa-chevron-left:before {
  content: '\f053'; }

.fa-chevron-right:before {
  content: '\f054'; }

.fa-chevron-up:before {
  content: '\f077'; }

.fa-child:before {
  content: '\f1ae'; }

.fa-chrome:before {
  content: '\f268'; }

.fa-church:before {
  content: '\f51d'; }

.fa-circle:before {
  content: '\f111'; }

.fa-circle-notch:before {
  content: '\f1ce'; }

.fa-city:before {
  content: '\f64f'; }

.fa-clipboard:before {
  content: '\f328'; }

.fa-clipboard-check:before {
  content: '\f46c'; }

.fa-clipboard-list:before {
  content: '\f46d'; }

.fa-clock:before {
  content: '\f017'; }

.fa-clone:before {
  content: '\f24d'; }

.fa-closed-captioning:before {
  content: '\f20a'; }

.fa-cloud:before {
  content: '\f0c2'; }

.fa-cloud-download-alt:before {
  content: '\f381'; }

.fa-cloud-moon:before {
  content: '\f6c3'; }

.fa-cloud-sun:before {
  content: '\f6c4'; }

.fa-cloud-upload-alt:before {
  content: '\f382'; }

.fa-cloudscale:before {
  content: '\f383'; }

.fa-cloudsmith:before {
  content: '\f384'; }

.fa-cloudversify:before {
  content: '\f385'; }

.fa-cocktail:before {
  content: '\f561'; }

.fa-code:before {
  content: '\f121'; }

.fa-code-branch:before {
  content: '\f126'; }

.fa-codepen:before {
  content: '\f1cb'; }

.fa-codiepie:before {
  content: '\f284'; }

.fa-coffee:before {
  content: '\f0f4'; }

.fa-cog:before {
  content: '\f013'; }

.fa-cogs:before {
  content: '\f085'; }

.fa-coins:before {
  content: '\f51e'; }

.fa-columns:before {
  content: '\f0db'; }

.fa-comment:before {
  content: '\f075'; }

.fa-comment-alt:before {
  content: '\f27a'; }

.fa-comment-dollar:before {
  content: '\f651'; }

.fa-comment-dots:before {
  content: '\f4ad'; }

.fa-comment-slash:before {
  content: '\f4b3'; }

.fa-comments:before {
  content: '\f086'; }

.fa-comments-dollar:before {
  content: '\f653'; }

.fa-compact-disc:before {
  content: '\f51f'; }

.fa-compass:before {
  content: '\f14e'; }

.fa-compress:before {
  content: '\f066'; }

.fa-concierge-bell:before {
  content: '\f562'; }

.fa-connectdevelop:before {
  content: '\f20e'; }

.fa-contao:before {
  content: '\f26d'; }

.fa-cookie:before {
  content: '\f563'; }

.fa-cookie-bite:before {
  content: '\f564'; }

.fa-copy:before {
  content: '\f0c5'; }

.fa-copyright:before {
  content: '\f1f9'; }

.fa-couch:before {
  content: '\f4b8'; }

.fa-cpanel:before {
  content: '\f388'; }

.fa-creative-commons:before {
  content: '\f25e'; }

.fa-creative-commons-by:before {
  content: '\f4e7'; }

.fa-creative-commons-nc:before {
  content: '\f4e8'; }

.fa-creative-commons-nc-eu:before {
  content: '\f4e9'; }

.fa-creative-commons-nc-jp:before {
  content: '\f4ea'; }

.fa-creative-commons-nd:before {
  content: '\f4eb'; }

.fa-creative-commons-pd:before {
  content: '\f4ec'; }

.fa-creative-commons-pd-alt:before {
  content: '\f4ed'; }

.fa-creative-commons-remix:before {
  content: '\f4ee'; }

.fa-creative-commons-sa:before {
  content: '\f4ef'; }

.fa-creative-commons-sampling:before {
  content: '\f4f0'; }

.fa-creative-commons-sampling-plus:before {
  content: '\f4f1'; }

.fa-creative-commons-share:before {
  content: '\f4f2'; }

.fa-creative-commons-zero:before {
  content: '\f4f3'; }

.fa-credit-card:before {
  content: '\f09d'; }

.fa-critical-role:before {
  content: '\f6c9'; }

.fa-crop:before {
  content: '\f125'; }

.fa-crop-alt:before {
  content: '\f565'; }

.fa-cross:before {
  content: '\f654'; }

.fa-crosshairs:before {
  content: '\f05b'; }

.fa-crow:before {
  content: '\f520'; }

.fa-crown:before {
  content: '\f521'; }

.fa-css3:before {
  content: '\f13c'; }

.fa-css3-alt:before {
  content: '\f38b'; }

.fa-cube:before {
  content: '\f1b2'; }

.fa-cubes:before {
  content: '\f1b3'; }

.fa-cut:before {
  content: '\f0c4'; }

.fa-cuttlefish:before {
  content: '\f38c'; }

.fa-d-and-d:before {
  content: '\f38d'; }

.fa-dashcube:before {
  content: '\f210'; }

.fa-database:before {
  content: '\f1c0'; }

.fa-deaf:before {
  content: '\f2a4'; }

.fa-delicious:before {
  content: '\f1a5'; }

.fa-deploydog:before {
  content: '\f38e'; }

.fa-deskpro:before {
  content: '\f38f'; }

.fa-desktop:before {
  content: '\f108'; }

.fa-dev:before {
  content: '\f6cc'; }

.fa-deviantart:before {
  content: '\f1bd'; }

.fa-dharmachakra:before {
  content: '\f655'; }

.fa-diagnoses:before {
  content: '\f470'; }

.fa-dice:before {
  content: '\f522'; }

.fa-dice-d20:before {
  content: '\f6cf'; }

.fa-dice-d6:before {
  content: '\f6d1'; }

.fa-dice-five:before {
  content: '\f523'; }

.fa-dice-four:before {
  content: '\f524'; }

.fa-dice-one:before {
  content: '\f525'; }

.fa-dice-six:before {
  content: '\f526'; }

.fa-dice-three:before {
  content: '\f527'; }

.fa-dice-two:before {
  content: '\f528'; }

.fa-digg:before {
  content: '\f1a6'; }

.fa-digital-ocean:before {
  content: '\f391'; }

.fa-digital-tachograph:before {
  content: '\f566'; }

.fa-directions:before {
  content: '\f5eb'; }

.fa-discord:before {
  content: '\f392'; }

.fa-discourse:before {
  content: '\f393'; }

.fa-divide:before {
  content: '\f529'; }

.fa-dizzy:before {
  content: '\f567'; }

.fa-dna:before {
  content: '\f471'; }

.fa-dochub:before {
  content: '\f394'; }

.fa-docker:before {
  content: '\f395'; }

.fa-dog:before {
  content: '\f6d3'; }

.fa-dollar-sign:before {
  content: '\f155'; }

.fa-dolly:before {
  content: '\f472'; }

.fa-dolly-flatbed:before {
  content: '\f474'; }

.fa-donate:before {
  content: '\f4b9'; }

.fa-door-closed:before {
  content: '\f52a'; }

.fa-door-open:before {
  content: '\f52b'; }

.fa-dot-circle:before {
  content: '\f192'; }

.fa-dove:before {
  content: '\f4ba'; }

.fa-download:before {
  content: '\f019'; }

.fa-draft2digital:before {
  content: '\f396'; }

.fa-drafting-compass:before {
  content: '\f568'; }

.fa-dragon:before {
  content: '\f6d5'; }

.fa-draw-polygon:before {
  content: '\f5ee'; }

.fa-dribbble:before {
  content: '\f17d'; }

.fa-dribbble-square:before {
  content: '\f397'; }

.fa-dropbox:before {
  content: '\f16b'; }

.fa-drum:before {
  content: '\f569'; }

.fa-drum-steelpan:before {
  content: '\f56a'; }

.fa-drumstick-bite:before {
  content: '\f6d7'; }

.fa-drupal:before {
  content: '\f1a9'; }

.fa-dumbbell:before {
  content: '\f44b'; }

.fa-dungeon:before {
  content: '\f6d9'; }

.fa-dyalog:before {
  content: '\f399'; }

.fa-earlybirds:before {
  content: '\f39a'; }

.fa-ebay:before {
  content: '\f4f4'; }

.fa-edge:before {
  content: '\f282'; }

.fa-edit:before {
  content: '\f044'; }

.fa-eject:before {
  content: '\f052'; }

.fa-elementor:before {
  content: '\f430'; }

.fa-ellipsis-h:before {
  content: '\f141'; }

.fa-ellipsis-v:before {
  content: '\f142'; }

.fa-ello:before {
  content: '\f5f1'; }

.fa-ember:before {
  content: '\f423'; }

.fa-empire:before {
  content: '\f1d1'; }

.fa-envelope:before {
  content: '\f0e0'; }

.fa-envelope-open:before {
  content: '\f2b6'; }

.fa-envelope-open-text:before {
  content: '\f658'; }

.fa-envelope-square:before {
  content: '\f199'; }

.fa-envira:before {
  content: '\f299'; }

.fa-equals:before {
  content: '\f52c'; }

.fa-eraser:before {
  content: '\f12d'; }

.fa-erlang:before {
  content: '\f39d'; }

.fa-ethereum:before {
  content: '\f42e'; }

.fa-etsy:before {
  content: '\f2d7'; }

.fa-euro-sign:before {
  content: '\f153'; }

.fa-exchange-alt:before {
  content: '\f362'; }

.fa-exclamation:before {
  content: '\f12a'; }

.fa-exclamation-circle:before {
  content: '\f06a'; }

.fa-exclamation-triangle:before {
  content: '\f071'; }

.fa-expand:before {
  content: '\f065'; }

.fa-expand-arrows-alt:before {
  content: '\f31e'; }

.fa-expeditedssl:before {
  content: '\f23e'; }

.fa-external-link-alt:before {
  content: '\f35d'; }

.fa-external-link-square-alt:before {
  content: '\f360'; }

.fa-eye:before {
  content: '\f06e'; }

.fa-eye-dropper:before {
  content: '\f1fb'; }

.fa-eye-slash:before {
  content: '\f070'; }

.fa-facebook:before {
  content: '\f09a'; }

.fa-facebook-f:before {
  content: '\f39e'; }

.fa-facebook-messenger:before {
  content: '\f39f'; }

.fa-facebook-square:before {
  content: '\f082'; }

.fa-fantasy-flight-games:before {
  content: '\f6dc'; }

.fa-fast-backward:before {
  content: '\f049'; }

.fa-fast-forward:before {
  content: '\f050'; }

.fa-fax:before {
  content: '\f1ac'; }

.fa-feather:before {
  content: '\f52d'; }

.fa-feather-alt:before {
  content: '\f56b'; }

.fa-female:before {
  content: '\f182'; }

.fa-fighter-jet:before {
  content: '\f0fb'; }

.fa-file:before {
  content: '\f15b'; }

.fa-file-alt:before {
  content: '\f15c'; }

.fa-file-archive:before {
  content: '\f1c6'; }

.fa-file-audio:before {
  content: '\f1c7'; }

.fa-file-code:before {
  content: '\f1c9'; }

.fa-file-contract:before {
  content: '\f56c'; }

.fa-file-csv:before {
  content: '\f6dd'; }

.fa-file-download:before {
  content: '\f56d'; }

.fa-file-excel:before {
  content: '\f1c3'; }

.fa-file-export:before {
  content: '\f56e'; }

.fa-file-image:before {
  content: '\f1c5'; }

.fa-file-import:before {
  content: '\f56f'; }

.fa-file-invoice:before {
  content: '\f570'; }

.fa-file-invoice-dollar:before {
  content: '\f571'; }

.fa-file-medical:before {
  content: '\f477'; }

.fa-file-medical-alt:before {
  content: '\f478'; }

.fa-file-pdf:before {
  content: '\f1c1'; }

.fa-file-powerpoint:before {
  content: '\f1c4'; }

.fa-file-prescription:before {
  content: '\f572'; }

.fa-file-signature:before {
  content: '\f573'; }

.fa-file-upload:before {
  content: '\f574'; }

.fa-file-video:before {
  content: '\f1c8'; }

.fa-file-word:before {
  content: '\f1c2'; }

.fa-fill:before {
  content: '\f575'; }

.fa-fill-drip:before {
  content: '\f576'; }

.fa-film:before {
  content: '\f008'; }

.fa-filter:before {
  content: '\f0b0'; }

.fa-fingerprint:before {
  content: '\f577'; }

.fa-fire:before {
  content: '\f06d'; }

.fa-fire-extinguisher:before {
  content: '\f134'; }

.fa-firefox:before {
  content: '\f269'; }

.fa-first-aid:before {
  content: '\f479'; }

.fa-first-order:before {
  content: '\f2b0'; }

.fa-first-order-alt:before {
  content: '\f50a'; }

.fa-firstdraft:before {
  content: '\f3a1'; }

.fa-fish:before {
  content: '\f578'; }

.fa-fist-raised:before {
  content: '\f6de'; }

.fa-flag:before {
  content: '\f024'; }

.fa-flag-checkered:before {
  content: '\f11e'; }

.fa-flask:before {
  content: '\f0c3'; }

.fa-flickr:before {
  content: '\f16e'; }

.fa-flipboard:before {
  content: '\f44d'; }

.fa-flushed:before {
  content: '\f579'; }

.fa-fly:before {
  content: '\f417'; }

.fa-folder:before {
  content: '\f07b'; }

.fa-folder-minus:before {
  content: '\f65d'; }

.fa-folder-open:before {
  content: '\f07c'; }

.fa-folder-plus:before {
  content: '\f65e'; }

.fa-font:before {
  content: '\f031'; }

.fa-font-awesome:before {
  content: '\f2b4'; }

.fa-font-awesome-alt:before {
  content: '\f35c'; }

.fa-font-awesome-flag:before {
  content: '\f425'; }

.fa-font-awesome-logo-full:before {
  content: '\f4e6'; }

.fa-fonticons:before {
  content: '\f280'; }

.fa-fonticons-fi:before {
  content: '\f3a2'; }

.fa-football-ball:before {
  content: '\f44e'; }

.fa-fort-awesome:before {
  content: '\f286'; }

.fa-fort-awesome-alt:before {
  content: '\f3a3'; }

.fa-forumbee:before {
  content: '\f211'; }

.fa-forward:before {
  content: '\f04e'; }

.fa-foursquare:before {
  content: '\f180'; }

.fa-free-code-camp:before {
  content: '\f2c5'; }

.fa-freebsd:before {
  content: '\f3a4'; }

.fa-frog:before {
  content: '\f52e'; }

.fa-frown:before {
  content: '\f119'; }

.fa-frown-open:before {
  content: '\f57a'; }

.fa-fulcrum:before {
  content: '\f50b'; }

.fa-funnel-dollar:before {
  content: '\f662'; }

.fa-futbol:before {
  content: '\f1e3'; }

.fa-galactic-republic:before {
  content: '\f50c'; }

.fa-galactic-senate:before {
  content: '\f50d'; }

.fa-gamepad:before {
  content: '\f11b'; }

.fa-gas-pump:before {
  content: '\f52f'; }

.fa-gavel:before {
  content: '\f0e3'; }

.fa-gem:before {
  content: '\f3a5'; }

.fa-genderless:before {
  content: '\f22d'; }

.fa-get-pocket:before {
  content: '\f265'; }

.fa-gg:before {
  content: '\f260'; }

.fa-gg-circle:before {
  content: '\f261'; }

.fa-ghost:before {
  content: '\f6e2'; }

.fa-gift:before {
  content: '\f06b'; }

.fa-git:before {
  content: '\f1d3'; }

.fa-git-square:before {
  content: '\f1d2'; }

.fa-github:before {
  content: '\f09b'; }

.fa-github-alt:before {
  content: '\f113'; }

.fa-github-square:before {
  content: '\f092'; }

.fa-gitkraken:before {
  content: '\f3a6'; }

.fa-gitlab:before {
  content: '\f296'; }

.fa-gitter:before {
  content: '\f426'; }

.fa-glass-martini:before {
  content: '\f000'; }

.fa-glass-martini-alt:before {
  content: '\f57b'; }

.fa-glasses:before {
  content: '\f530'; }

.fa-glide:before {
  content: '\f2a5'; }

.fa-glide-g:before {
  content: '\f2a6'; }

.fa-globe:before {
  content: '\f0ac'; }

.fa-globe-africa:before {
  content: '\f57c'; }

.fa-globe-americas:before {
  content: '\f57d'; }

.fa-globe-asia:before {
  content: '\f57e'; }

.fa-gofore:before {
  content: '\f3a7'; }

.fa-golf-ball:before {
  content: '\f450'; }

.fa-goodreads:before {
  content: '\f3a8'; }

.fa-goodreads-g:before {
  content: '\f3a9'; }

.fa-google:before {
  content: '\f1a0'; }

.fa-google-drive:before {
  content: '\f3aa'; }

.fa-google-play:before {
  content: '\f3ab'; }

.fa-google-plus:before {
  content: '\f2b3'; }

.fa-google-plus-g:before {
  content: '\f0d5'; }

.fa-google-plus-square:before {
  content: '\f0d4'; }

.fa-google-wallet:before {
  content: '\f1ee'; }

.fa-gopuram:before {
  content: '\f664'; }

.fa-graduation-cap:before {
  content: '\f19d'; }

.fa-gratipay:before {
  content: '\f184'; }

.fa-grav:before {
  content: '\f2d6'; }

.fa-greater-than:before {
  content: '\f531'; }

.fa-greater-than-equal:before {
  content: '\f532'; }

.fa-grimace:before {
  content: '\f57f'; }

.fa-grin:before {
  content: '\f580'; }

.fa-grin-alt:before {
  content: '\f581'; }

.fa-grin-beam:before {
  content: '\f582'; }

.fa-grin-beam-sweat:before {
  content: '\f583'; }

.fa-grin-hearts:before {
  content: '\f584'; }

.fa-grin-squint:before {
  content: '\f585'; }

.fa-grin-squint-tears:before {
  content: '\f586'; }

.fa-grin-stars:before {
  content: '\f587'; }

.fa-grin-tears:before {
  content: '\f588'; }

.fa-grin-tongue:before {
  content: '\f589'; }

.fa-grin-tongue-squint:before {
  content: '\f58a'; }

.fa-grin-tongue-wink:before {
  content: '\f58b'; }

.fa-grin-wink:before {
  content: '\f58c'; }

.fa-grip-horizontal:before {
  content: '\f58d'; }

.fa-grip-vertical:before {
  content: '\f58e'; }

.fa-gripfire:before {
  content: '\f3ac'; }

.fa-grunt:before {
  content: '\f3ad'; }

.fa-gulp:before {
  content: '\f3ae'; }

.fa-h-square:before {
  content: '\f0fd'; }

.fa-hacker-news:before {
  content: '\f1d4'; }

.fa-hacker-news-square:before {
  content: '\f3af'; }

.fa-hackerrank:before {
  content: '\f5f7'; }

.fa-hammer:before {
  content: '\f6e3'; }

.fa-hamsa:before {
  content: '\f665'; }

.fa-hand-holding:before {
  content: '\f4bd'; }

.fa-hand-holding-heart:before {
  content: '\f4be'; }

.fa-hand-holding-usd:before {
  content: '\f4c0'; }

.fa-hand-lizard:before {
  content: '\f258'; }

.fa-hand-paper:before {
  content: '\f256'; }

.fa-hand-peace:before {
  content: '\f25b'; }

.fa-hand-point-down:before {
  content: '\f0a7'; }

.fa-hand-point-left:before {
  content: '\f0a5'; }

.fa-hand-point-right:before {
  content: '\f0a4'; }

.fa-hand-point-up:before {
  content: '\f0a6'; }

.fa-hand-pointer:before {
  content: '\f25a'; }

.fa-hand-rock:before {
  content: '\f255'; }

.fa-hand-scissors:before {
  content: '\f257'; }

.fa-hand-spock:before {
  content: '\f259'; }

.fa-hands:before {
  content: '\f4c2'; }

.fa-hands-helping:before {
  content: '\f4c4'; }

.fa-handshake:before {
  content: '\f2b5'; }

.fa-hanukiah:before {
  content: '\f6e6'; }

.fa-hashtag:before {
  content: '\f292'; }

.fa-hat-wizard:before {
  content: '\f6e8'; }

.fa-haykal:before {
  content: '\f666'; }

.fa-hdd:before {
  content: '\f0a0'; }

.fa-heading:before {
  content: '\f1dc'; }

.fa-headphones:before {
  content: '\f025'; }

.fa-headphones-alt:before {
  content: '\f58f'; }

.fa-headset:before {
  content: '\f590'; }

.fa-heart:before {
  content: '\f004'; }

.fa-heartbeat:before {
  content: '\f21e'; }

.fa-helicopter:before {
  content: '\f533'; }

.fa-highlighter:before {
  content: '\f591'; }

.fa-hiking:before {
  content: '\f6ec'; }

.fa-hippo:before {
  content: '\f6ed'; }

.fa-hips:before {
  content: '\f452'; }

.fa-hire-a-helper:before {
  content: '\f3b0'; }

.fa-history:before {
  content: '\f1da'; }

.fa-hockey-puck:before {
  content: '\f453'; }

.fa-home:before {
  content: '\f015'; }

.fa-hooli:before {
  content: '\f427'; }

.fa-hornbill:before {
  content: '\f592'; }

.fa-horse:before {
  content: '\f6f0'; }

.fa-hospital:before {
  content: '\f0f8'; }

.fa-hospital-alt:before {
  content: '\f47d'; }

.fa-hospital-symbol:before {
  content: '\f47e'; }

.fa-hot-tub:before {
  content: '\f593'; }

.fa-hotel:before {
  content: '\f594'; }

.fa-hotjar:before {
  content: '\f3b1'; }

.fa-hourglass:before {
  content: '\f254'; }

.fa-hourglass-end:before {
  content: '\f253'; }

.fa-hourglass-half:before {
  content: '\f252'; }

.fa-hourglass-start:before {
  content: '\f251'; }

.fa-house-damage:before {
  content: '\f6f1'; }

.fa-houzz:before {
  content: '\f27c'; }

.fa-hryvnia:before {
  content: '\f6f2'; }

.fa-html5:before {
  content: '\f13b'; }

.fa-hubspot:before {
  content: '\f3b2'; }

.fa-i-cursor:before {
  content: '\f246'; }

.fa-id-badge:before {
  content: '\f2c1'; }

.fa-id-card:before {
  content: '\f2c2'; }

.fa-id-card-alt:before {
  content: '\f47f'; }

.fa-image:before {
  content: '\f03e'; }

.fa-images:before {
  content: '\f302'; }

.fa-imdb:before {
  content: '\f2d8'; }

.fa-inbox:before {
  content: '\f01c'; }

.fa-indent:before {
  content: '\f03c'; }

.fa-industry:before {
  content: '\f275'; }

.fa-infinity:before {
  content: '\f534'; }

.fa-info:before {
  content: '\f129'; }

.fa-info-circle:before {
  content: '\f05a'; }

.fa-instagram:before {
  content: '\f16d'; }

.fa-internet-explorer:before {
  content: '\f26b'; }

.fa-ioxhost:before {
  content: '\f208'; }

.fa-italic:before {
  content: '\f033'; }

.fa-itunes:before {
  content: '\f3b4'; }

.fa-itunes-note:before {
  content: '\f3b5'; }

.fa-java:before {
  content: '\f4e4'; }

.fa-jedi:before {
  content: '\f669'; }

.fa-jedi-order:before {
  content: '\f50e'; }

.fa-jenkins:before {
  content: '\f3b6'; }

.fa-joget:before {
  content: '\f3b7'; }

.fa-joint:before {
  content: '\f595'; }

.fa-joomla:before {
  content: '\f1aa'; }

.fa-journal-whills:before {
  content: '\f66a'; }

.fa-js:before {
  content: '\f3b8'; }

.fa-js-square:before {
  content: '\f3b9'; }

.fa-jsfiddle:before {
  content: '\f1cc'; }

.fa-kaaba:before {
  content: '\f66b'; }

.fa-kaggle:before {
  content: '\f5fa'; }

.fa-key:before {
  content: '\f084'; }

.fa-keybase:before {
  content: '\f4f5'; }

.fa-keyboard:before {
  content: '\f11c'; }

.fa-keycdn:before {
  content: '\f3ba'; }

.fa-khanda:before {
  content: '\f66d'; }

.fa-kickstarter:before {
  content: '\f3bb'; }

.fa-kickstarter-k:before {
  content: '\f3bc'; }

.fa-kiss:before {
  content: '\f596'; }

.fa-kiss-beam:before {
  content: '\f597'; }

.fa-kiss-wink-heart:before {
  content: '\f598'; }

.fa-kiwi-bird:before {
  content: '\f535'; }

.fa-korvue:before {
  content: '\f42f'; }

.fa-landmark:before {
  content: '\f66f'; }

.fa-language:before {
  content: '\f1ab'; }

.fa-laptop:before {
  content: '\f109'; }

.fa-laptop-code:before {
  content: '\f5fc'; }

.fa-laravel:before {
  content: '\f3bd'; }

.fa-lastfm:before {
  content: '\f202'; }

.fa-lastfm-square:before {
  content: '\f203'; }

.fa-laugh:before {
  content: '\f599'; }

.fa-laugh-beam:before {
  content: '\f59a'; }

.fa-laugh-squint:before {
  content: '\f59b'; }

.fa-laugh-wink:before {
  content: '\f59c'; }

.fa-layer-group:before {
  content: '\f5fd'; }

.fa-leaf:before {
  content: '\f06c'; }

.fa-leanpub:before {
  content: '\f212'; }

.fa-lemon:before {
  content: '\f094'; }

.fa-less:before {
  content: '\f41d'; }

.fa-less-than:before {
  content: '\f536'; }

.fa-less-than-equal:before {
  content: '\f537'; }

.fa-level-down-alt:before {
  content: '\f3be'; }

.fa-level-up-alt:before {
  content: '\f3bf'; }

.fa-life-ring:before {
  content: '\f1cd'; }

.fa-lightbulb:before {
  content: '\f0eb'; }

.fa-line:before {
  content: '\f3c0'; }

.fa-link:before {
  content: '\f0c1'; }

.fa-linkedin:before {
  content: '\f08c'; }

.fa-linkedin-in:before {
  content: '\f0e1'; }

.fa-linode:before {
  content: '\f2b8'; }

.fa-linux:before {
  content: '\f17c'; }

.fa-lira-sign:before {
  content: '\f195'; }

.fa-list:before {
  content: '\f03a'; }

.fa-list-alt:before {
  content: '\f022'; }

.fa-list-ol:before {
  content: '\f0cb'; }

.fa-list-ul:before {
  content: '\f0ca'; }

.fa-location-arrow:before {
  content: '\f124'; }

.fa-lock:before {
  content: '\f023'; }

.fa-lock-open:before {
  content: '\f3c1'; }

.fa-long-arrow-alt-down:before {
  content: '\f309'; }

.fa-long-arrow-alt-left:before {
  content: '\f30a'; }

.fa-long-arrow-alt-right:before {
  content: '\f30b'; }

.fa-long-arrow-alt-up:before {
  content: '\f30c'; }

.fa-low-vision:before {
  content: '\f2a8'; }

.fa-luggage-cart:before {
  content: '\f59d'; }

.fa-lyft:before {
  content: '\f3c3'; }

.fa-magento:before {
  content: '\f3c4'; }

.fa-magic:before {
  content: '\f0d0'; }

.fa-magnet:before {
  content: '\f076'; }

.fa-mail-bulk:before {
  content: '\f674'; }

.fa-mailchimp:before {
  content: '\f59e'; }

.fa-male:before {
  content: '\f183'; }

.fa-mandalorian:before {
  content: '\f50f'; }

.fa-map:before {
  content: '\f279'; }

.fa-map-marked:before {
  content: '\f59f'; }

.fa-map-marked-alt:before {
  content: '\f5a0'; }

.fa-map-marker:before {
  content: '\f041'; }

.fa-map-marker-alt:before {
  content: '\f3c5'; }

.fa-map-pin:before {
  content: '\f276'; }

.fa-map-signs:before {
  content: '\f277'; }

.fa-markdown:before {
  content: '\f60f'; }

.fa-marker:before {
  content: '\f5a1'; }

.fa-mars:before {
  content: '\f222'; }

.fa-mars-double:before {
  content: '\f227'; }

.fa-mars-stroke:before {
  content: '\f229'; }

.fa-mars-stroke-h:before {
  content: '\f22b'; }

.fa-mars-stroke-v:before {
  content: '\f22a'; }

.fa-mask:before {
  content: '\f6fa'; }

.fa-mastodon:before {
  content: '\f4f6'; }

.fa-maxcdn:before {
  content: '\f136'; }

.fa-medal:before {
  content: '\f5a2'; }

.fa-medapps:before {
  content: '\f3c6'; }

.fa-medium:before {
  content: '\f23a'; }

.fa-medium-m:before {
  content: '\f3c7'; }

.fa-medkit:before {
  content: '\f0fa'; }

.fa-medrt:before {
  content: '\f3c8'; }

.fa-meetup:before {
  content: '\f2e0'; }

.fa-megaport:before {
  content: '\f5a3'; }

.fa-meh:before {
  content: '\f11a'; }

.fa-meh-blank:before {
  content: '\f5a4'; }

.fa-meh-rolling-eyes:before {
  content: '\f5a5'; }

.fa-memory:before {
  content: '\f538'; }

.fa-menorah:before {
  content: '\f676'; }

.fa-mercury:before {
  content: '\f223'; }

.fa-microchip:before {
  content: '\f2db'; }

.fa-microphone:before {
  content: '\f130'; }

.fa-microphone-alt:before {
  content: '\f3c9'; }

.fa-microphone-alt-slash:before {
  content: '\f539'; }

.fa-microphone-slash:before {
  content: '\f131'; }

.fa-microscope:before {
  content: '\f610'; }

.fa-microsoft:before {
  content: '\f3ca'; }

.fa-minus:before {
  content: '\f068'; }

.fa-minus-circle:before {
  content: '\f056'; }

.fa-minus-square:before {
  content: '\f146'; }

.fa-mix:before {
  content: '\f3cb'; }

.fa-mixcloud:before {
  content: '\f289'; }

.fa-mizuni:before {
  content: '\f3cc'; }

.fa-mobile:before {
  content: '\f10b'; }

.fa-mobile-alt:before {
  content: '\f3cd'; }

.fa-modx:before {
  content: '\f285'; }

.fa-monero:before {
  content: '\f3d0'; }

.fa-money-bill:before {
  content: '\f0d6'; }

.fa-money-bill-alt:before {
  content: '\f3d1'; }

.fa-money-bill-wave:before {
  content: '\f53a'; }

.fa-money-bill-wave-alt:before {
  content: '\f53b'; }

.fa-money-check:before {
  content: '\f53c'; }

.fa-money-check-alt:before {
  content: '\f53d'; }

.fa-monument:before {
  content: '\f5a6'; }

.fa-moon:before {
  content: '\f186'; }

.fa-mortar-pestle:before {
  content: '\f5a7'; }

.fa-mosque:before {
  content: '\f678'; }

.fa-motorcycle:before {
  content: '\f21c'; }

.fa-mountain:before {
  content: '\f6fc'; }

.fa-mouse-pointer:before {
  content: '\f245'; }

.fa-music:before {
  content: '\f001'; }

.fa-napster:before {
  content: '\f3d2'; }

.fa-neos:before {
  content: '\f612'; }

.fa-network-wired:before {
  content: '\f6ff'; }

.fa-neuter:before {
  content: '\f22c'; }

.fa-newspaper:before {
  content: '\f1ea'; }

.fa-nimblr:before {
  content: '\f5a8'; }

.fa-nintendo-switch:before {
  content: '\f418'; }

.fa-node:before {
  content: '\f419'; }

.fa-node-js:before {
  content: '\f3d3'; }

.fa-not-equal:before {
  content: '\f53e'; }

.fa-notes-medical:before {
  content: '\f481'; }

.fa-npm:before {
  content: '\f3d4'; }

.fa-ns8:before {
  content: '\f3d5'; }

.fa-nutritionix:before {
  content: '\f3d6'; }

.fa-object-group:before {
  content: '\f247'; }

.fa-object-ungroup:before {
  content: '\f248'; }

.fa-odnoklassniki:before {
  content: '\f263'; }

.fa-odnoklassniki-square:before {
  content: '\f264'; }

.fa-oil-can:before {
  content: '\f613'; }

.fa-old-republic:before {
  content: '\f510'; }

.fa-om:before {
  content: '\f679'; }

.fa-opencart:before {
  content: '\f23d'; }

.fa-openid:before {
  content: '\f19b'; }

.fa-opera:before {
  content: '\f26a'; }

.fa-optin-monster:before {
  content: '\f23c'; }

.fa-osi:before {
  content: '\f41a'; }

.fa-otter:before {
  content: '\f700'; }

.fa-outdent:before {
  content: '\f03b'; }

.fa-page4:before {
  content: '\f3d7'; }

.fa-pagelines:before {
  content: '\f18c'; }

.fa-paint-brush:before {
  content: '\f1fc'; }

.fa-paint-roller:before {
  content: '\f5aa'; }

.fa-palette:before {
  content: '\f53f'; }

.fa-palfed:before {
  content: '\f3d8'; }

.fa-pallet:before {
  content: '\f482'; }

.fa-paper-plane:before {
  content: '\f1d8'; }

.fa-paperclip:before {
  content: '\f0c6'; }

.fa-parachute-box:before {
  content: '\f4cd'; }

.fa-paragraph:before {
  content: '\f1dd'; }

.fa-parking:before {
  content: '\f540'; }

.fa-passport:before {
  content: '\f5ab'; }

.fa-pastafarianism:before {
  content: '\f67b'; }

.fa-paste:before {
  content: '\f0ea'; }

.fa-patreon:before {
  content: '\f3d9'; }

.fa-pause:before {
  content: '\f04c'; }

.fa-pause-circle:before {
  content: '\f28b'; }

.fa-paw:before {
  content: '\f1b0'; }

.fa-paypal:before {
  content: '\f1ed'; }

.fa-peace:before {
  content: '\f67c'; }

.fa-pen:before {
  content: '\f304'; }

.fa-pen-alt:before {
  content: '\f305'; }

.fa-pen-fancy:before {
  content: '\f5ac'; }

.fa-pen-nib:before {
  content: '\f5ad'; }

.fa-pen-square:before {
  content: '\f14b'; }

.fa-pencil-alt:before {
  content: '\f303'; }

.fa-pencil-ruler:before {
  content: '\f5ae'; }

.fa-penny-arcade:before {
  content: '\f704'; }

.fa-people-carry:before {
  content: '\f4ce'; }

.fa-percent:before {
  content: '\f295'; }

.fa-percentage:before {
  content: '\f541'; }

.fa-periscope:before {
  content: '\f3da'; }

.fa-phabricator:before {
  content: '\f3db'; }

.fa-phoenix-framework:before {
  content: '\f3dc'; }

.fa-phoenix-squadron:before {
  content: '\f511'; }

.fa-phone:before {
  content: '\f095'; }

.fa-phone-slash:before {
  content: '\f3dd'; }

.fa-phone-square:before {
  content: '\f098'; }

.fa-phone-volume:before {
  content: '\f2a0'; }

.fa-php:before {
  content: '\f457'; }

.fa-pied-piper:before {
  content: '\f2ae'; }

.fa-pied-piper-alt:before {
  content: '\f1a8'; }

.fa-pied-piper-hat:before {
  content: '\f4e5'; }

.fa-pied-piper-pp:before {
  content: '\f1a7'; }

.fa-piggy-bank:before {
  content: '\f4d3'; }

.fa-pills:before {
  content: '\f484'; }

.fa-pinterest:before {
  content: '\f0d2'; }

.fa-pinterest-p:before {
  content: '\f231'; }

.fa-pinterest-square:before {
  content: '\f0d3'; }

.fa-place-of-worship:before {
  content: '\f67f'; }

.fa-plane:before {
  content: '\f072'; }

.fa-plane-arrival:before {
  content: '\f5af'; }

.fa-plane-departure:before {
  content: '\f5b0'; }

.fa-play:before {
  content: '\f04b'; }

.fa-play-circle:before {
  content: '\f144'; }

.fa-playstation:before {
  content: '\f3df'; }

.fa-plug:before {
  content: '\f1e6'; }

.fa-plus:before {
  content: '\f067'; }

.fa-plus-circle:before {
  content: '\f055'; }

.fa-plus-square:before {
  content: '\f0fe'; }

.fa-podcast:before {
  content: '\f2ce'; }

.fa-poll:before {
  content: '\f681'; }

.fa-poll-h:before {
  content: '\f682'; }

.fa-poo:before {
  content: '\f2fe'; }

.fa-poop:before {
  content: '\f619'; }

.fa-portrait:before {
  content: '\f3e0'; }

.fa-pound-sign:before {
  content: '\f154'; }

.fa-power-off:before {
  content: '\f011'; }

.fa-pray:before {
  content: '\f683'; }

.fa-praying-hands:before {
  content: '\f684'; }

.fa-prescription:before {
  content: '\f5b1'; }

.fa-prescription-bottle:before {
  content: '\f485'; }

.fa-prescription-bottle-alt:before {
  content: '\f486'; }

.fa-print:before {
  content: '\f02f'; }

.fa-procedures:before {
  content: '\f487'; }

.fa-product-hunt:before {
  content: '\f288'; }

.fa-project-diagram:before {
  content: '\f542'; }

.fa-pushed:before {
  content: '\f3e1'; }

.fa-puzzle-piece:before {
  content: '\f12e'; }

.fa-python:before {
  content: '\f3e2'; }

.fa-qq:before {
  content: '\f1d6'; }

.fa-qrcode:before {
  content: '\f029'; }

.fa-question:before {
  content: '\f128'; }

.fa-question-circle:before {
  content: '\f059'; }

.fa-quidditch:before {
  content: '\f458'; }

.fa-quinscape:before {
  content: '\f459'; }

.fa-quora:before {
  content: '\f2c4'; }

.fa-quote-left:before {
  content: '\f10d'; }

.fa-quote-right:before {
  content: '\f10e'; }

.fa-quran:before {
  content: '\f687'; }

.fa-r-project:before {
  content: '\f4f7'; }

.fa-random:before {
  content: '\f074'; }

.fa-ravelry:before {
  content: '\f2d9'; }

.fa-react:before {
  content: '\f41b'; }

.fa-readme:before {
  content: '\f4d5'; }

.fa-rebel:before {
  content: '\f1d0'; }

.fa-receipt:before {
  content: '\f543'; }

.fa-recycle:before {
  content: '\f1b8'; }

.fa-red-river:before {
  content: '\f3e3'; }

.fa-reddit:before {
  content: '\f1a1'; }

.fa-reddit-alien:before {
  content: '\f281'; }

.fa-reddit-square:before {
  content: '\f1a2'; }

.fa-redo:before {
  content: '\f01e'; }

.fa-redo-alt:before {
  content: '\f2f9'; }

.fa-registered:before {
  content: '\f25d'; }

.fa-rendact:before {
  content: '\f3e4'; }

.fa-renren:before {
  content: '\f18b'; }

.fa-reply:before {
  content: '\f3e5'; }

.fa-reply-all:before {
  content: '\f122'; }

.fa-replyd:before {
  content: '\f3e6'; }

.fa-researchgate:before {
  content: '\f4f8'; }

.fa-resolving:before {
  content: '\f3e7'; }

.fa-retweet:before {
  content: '\f079'; }

.fa-rev:before {
  content: '\f5b2'; }

.fa-ribbon:before {
  content: '\f4d6'; }

.fa-ring:before {
  content: '\f70b'; }

.fa-road:before {
  content: '\f018'; }

.fa-robot:before {
  content: '\f544'; }

.fa-rocket:before {
  content: '\f135'; }

.fa-rocketchat:before {
  content: '\f3e8'; }

.fa-rockrms:before {
  content: '\f3e9'; }

.fa-route:before {
  content: '\f4d7'; }

.fa-rss:before {
  content: '\f09e'; }

.fa-rss-square:before {
  content: '\f143'; }

.fa-ruble-sign:before {
  content: '\f158'; }

.fa-ruler:before {
  content: '\f545'; }

.fa-ruler-combined:before {
  content: '\f546'; }

.fa-ruler-horizontal:before {
  content: '\f547'; }

.fa-ruler-vertical:before {
  content: '\f548'; }

.fa-running:before {
  content: '\f70c'; }

.fa-rupee-sign:before {
  content: '\f156'; }

.fa-sad-cry:before {
  content: '\f5b3'; }

.fa-sad-tear:before {
  content: '\f5b4'; }

.fa-safari:before {
  content: '\f267'; }

.fa-sass:before {
  content: '\f41e'; }

.fa-save:before {
  content: '\f0c7'; }

.fa-schlix:before {
  content: '\f3ea'; }

.fa-school:before {
  content: '\f549'; }

.fa-screwdriver:before {
  content: '\f54a'; }

.fa-scribd:before {
  content: '\f28a'; }

.fa-scroll:before {
  content: '\f70e'; }

.fa-search:before {
  content: '\f002'; }

.fa-search-dollar:before {
  content: '\f688'; }

.fa-search-location:before {
  content: '\f689'; }

.fa-search-minus:before {
  content: '\f010'; }

.fa-search-plus:before {
  content: '\f00e'; }

.fa-searchengin:before {
  content: '\f3eb'; }

.fa-seedling:before {
  content: '\f4d8'; }

.fa-sellcast:before {
  content: '\f2da'; }

.fa-sellsy:before {
  content: '\f213'; }

.fa-server:before {
  content: '\f233'; }

.fa-servicestack:before {
  content: '\f3ec'; }

.fa-shapes:before {
  content: '\f61f'; }

.fa-share:before {
  content: '\f064'; }

.fa-share-alt:before {
  content: '\f1e0'; }

.fa-share-alt-square:before {
  content: '\f1e1'; }

.fa-share-square:before {
  content: '\f14d'; }

.fa-shekel-sign:before {
  content: '\f20b'; }

.fa-shield-alt:before {
  content: '\f3ed'; }

.fa-ship:before {
  content: '\f21a'; }

.fa-shipping-fast:before {
  content: '\f48b'; }

.fa-shirtsinbulk:before {
  content: '\f214'; }

.fa-shoe-prints:before {
  content: '\f54b'; }

.fa-shopping-bag:before {
  content: '\f290'; }

.fa-shopping-basket:before {
  content: '\f291'; }

.fa-shopping-cart:before {
  content: '\f07a'; }

.fa-shopware:before {
  content: '\f5b5'; }

.fa-shower:before {
  content: '\f2cc'; }

.fa-shuttle-van:before {
  content: '\f5b6'; }

.fa-sign:before {
  content: '\f4d9'; }

.fa-sign-in-alt:before {
  content: '\f2f6'; }

.fa-sign-language:before {
  content: '\f2a7'; }

.fa-sign-out-alt:before {
  content: '\f2f5'; }

.fa-signal:before {
  content: '\f012'; }

.fa-signature:before {
  content: '\f5b7'; }

.fa-simplybuilt:before {
  content: '\f215'; }

.fa-sistrix:before {
  content: '\f3ee'; }

.fa-sitemap:before {
  content: '\f0e8'; }

.fa-sith:before {
  content: '\f512'; }

.fa-skull:before {
  content: '\f54c'; }

.fa-skull-crossbones:before {
  content: '\f714'; }

.fa-skyatlas:before {
  content: '\f216'; }

.fa-skype:before {
  content: '\f17e'; }

.fa-slack:before {
  content: '\f198'; }

.fa-slack-hash:before {
  content: '\f3ef'; }

.fa-slash:before {
  content: '\f715'; }

.fa-sliders-h:before {
  content: '\f1de'; }

.fa-slideshare:before {
  content: '\f1e7'; }

.fa-smile:before {
  content: '\f118'; }

.fa-smile-beam:before {
  content: '\f5b8'; }

.fa-smile-wink:before {
  content: '\f4da'; }

.fa-smoking:before {
  content: '\f48d'; }

.fa-smoking-ban:before {
  content: '\f54d'; }

.fa-snapchat:before {
  content: '\f2ab'; }

.fa-snapchat-ghost:before {
  content: '\f2ac'; }

.fa-snapchat-square:before {
  content: '\f2ad'; }

.fa-snowflake:before {
  content: '\f2dc'; }

.fa-socks:before {
  content: '\f696'; }

.fa-solar-panel:before {
  content: '\f5ba'; }

.fa-sort:before {
  content: '\f0dc'; }

.fa-sort-alpha-down:before {
  content: '\f15d'; }

.fa-sort-alpha-up:before {
  content: '\f15e'; }

.fa-sort-amount-down:before {
  content: '\f160'; }

.fa-sort-amount-up:before {
  content: '\f161'; }

.fa-sort-down:before {
  content: '\f0dd'; }

.fa-sort-numeric-down:before {
  content: '\f162'; }

.fa-sort-numeric-up:before {
  content: '\f163'; }

.fa-sort-up:before {
  content: '\f0de'; }

.fa-soundcloud:before {
  content: '\f1be'; }

.fa-spa:before {
  content: '\f5bb'; }

.fa-space-shuttle:before {
  content: '\f197'; }

.fa-speakap:before {
  content: '\f3f3'; }

.fa-spider:before {
  content: '\f717'; }

.fa-spinner:before {
  content: '\f110'; }

.fa-splotch:before {
  content: '\f5bc'; }

.fa-spotify:before {
  content: '\f1bc'; }

.fa-spray-can:before {
  content: '\f5bd'; }

.fa-square:before {
  content: '\f0c8'; }

.fa-square-full:before {
  content: '\f45c'; }

.fa-square-root-alt:before {
  content: '\f698'; }

.fa-squarespace:before {
  content: '\f5be'; }

.fa-stack-exchange:before {
  content: '\f18d'; }

.fa-stack-overflow:before {
  content: '\f16c'; }

.fa-stamp:before {
  content: '\f5bf'; }

.fa-star:before {
  content: '\f005'; }

.fa-star-and-crescent:before {
  content: '\f699'; }

.fa-star-half:before {
  content: '\f089'; }

.fa-star-half-alt:before {
  content: '\f5c0'; }

.fa-star-of-david:before {
  content: '\f69a'; }

.fa-star-of-life:before {
  content: '\f621'; }

.fa-staylinked:before {
  content: '\f3f5'; }

.fa-steam:before {
  content: '\f1b6'; }

.fa-steam-square:before {
  content: '\f1b7'; }

.fa-steam-symbol:before {
  content: '\f3f6'; }

.fa-step-backward:before {
  content: '\f048'; }

.fa-step-forward:before {
  content: '\f051'; }

.fa-stethoscope:before {
  content: '\f0f1'; }

.fa-sticker-mule:before {
  content: '\f3f7'; }

.fa-sticky-note:before {
  content: '\f249'; }

.fa-stop:before {
  content: '\f04d'; }

.fa-stop-circle:before {
  content: '\f28d'; }

.fa-stopwatch:before {
  content: '\f2f2'; }

.fa-store:before {
  content: '\f54e'; }

.fa-store-alt:before {
  content: '\f54f'; }

.fa-strava:before {
  content: '\f428'; }

.fa-stream:before {
  content: '\f550'; }

.fa-street-view:before {
  content: '\f21d'; }

.fa-strikethrough:before {
  content: '\f0cc'; }

.fa-stripe:before {
  content: '\f429'; }

.fa-stripe-s:before {
  content: '\f42a'; }

.fa-stroopwafel:before {
  content: '\f551'; }

.fa-studiovinari:before {
  content: '\f3f8'; }

.fa-stumbleupon:before {
  content: '\f1a4'; }

.fa-stumbleupon-circle:before {
  content: '\f1a3'; }

.fa-subscript:before {
  content: '\f12c'; }

.fa-subway:before {
  content: '\f239'; }

.fa-suitcase:before {
  content: '\f0f2'; }

.fa-suitcase-rolling:before {
  content: '\f5c1'; }

.fa-sun:before {
  content: '\f185'; }

.fa-superpowers:before {
  content: '\f2dd'; }

.fa-superscript:before {
  content: '\f12b'; }

.fa-supple:before {
  content: '\f3f9'; }

.fa-surprise:before {
  content: '\f5c2'; }

.fa-swatchbook:before {
  content: '\f5c3'; }

.fa-swimmer:before {
  content: '\f5c4'; }

.fa-swimming-pool:before {
  content: '\f5c5'; }

.fa-synagogue:before {
  content: '\f69b'; }

.fa-sync:before {
  content: '\f021'; }

.fa-sync-alt:before {
  content: '\f2f1'; }

.fa-syringe:before {
  content: '\f48e'; }

.fa-table:before {
  content: '\f0ce'; }

.fa-table-tennis:before {
  content: '\f45d'; }

.fa-tablet:before {
  content: '\f10a'; }

.fa-tablet-alt:before {
  content: '\f3fa'; }

.fa-tablets:before {
  content: '\f490'; }

.fa-tachometer-alt:before {
  content: '\f3fd'; }

.fa-tag:before {
  content: '\f02b'; }

.fa-tags:before {
  content: '\f02c'; }

.fa-tape:before {
  content: '\f4db'; }

.fa-tasks:before {
  content: '\f0ae'; }

.fa-taxi:before {
  content: '\f1ba'; }

.fa-teamspeak:before {
  content: '\f4f9'; }

.fa-teeth:before {
  content: '\f62e'; }

.fa-teeth-open:before {
  content: '\f62f'; }

.fa-telegram:before {
  content: '\f2c6'; }

.fa-telegram-plane:before {
  content: '\f3fe'; }

.fa-tencent-weibo:before {
  content: '\f1d5'; }

.fa-terminal:before {
  content: '\f120'; }

.fa-text-height:before {
  content: '\f034'; }

.fa-text-width:before {
  content: '\f035'; }

.fa-th:before {
  content: '\f00a'; }

.fa-th-large:before {
  content: '\f009'; }

.fa-th-list:before {
  content: '\f00b'; }

.fa-the-red-yeti:before {
  content: '\f69d'; }

.fa-theater-masks:before {
  content: '\f630'; }

.fa-themeco:before {
  content: '\f5c6'; }

.fa-themeisle:before {
  content: '\f2b2'; }

.fa-thermometer:before {
  content: '\f491'; }

.fa-thermometer-empty:before {
  content: '\f2cb'; }

.fa-thermometer-full:before {
  content: '\f2c7'; }

.fa-thermometer-half:before {
  content: '\f2c9'; }

.fa-thermometer-quarter:before {
  content: '\f2ca'; }

.fa-thermometer-three-quarters:before {
  content: '\f2c8'; }

.fa-thumbs-down:before {
  content: '\f165'; }

.fa-thumbs-up:before {
  content: '\f164'; }

.fa-thumbtack:before {
  content: '\f08d'; }

.fa-ticket-alt:before {
  content: '\f3ff'; }

.fa-times:before {
  content: '\f00d'; }

.fa-times-circle:before {
  content: '\f057'; }

.fa-tint:before {
  content: '\f043'; }

.fa-tint-slash:before {
  content: '\f5c7'; }

.fa-tired:before {
  content: '\f5c8'; }

.fa-toggle-off:before {
  content: '\f204'; }

.fa-toggle-on:before {
  content: '\f205'; }

.fa-toilet-paper:before {
  content: '\f71e'; }

.fa-toolbox:before {
  content: '\f552'; }

.fa-tooth:before {
  content: '\f5c9'; }

.fa-torah:before {
  content: '\f6a0'; }

.fa-torii-gate:before {
  content: '\f6a1'; }

.fa-tractor:before {
  content: '\f722'; }

.fa-trade-federation:before {
  content: '\f513'; }

.fa-trademark:before {
  content: '\f25c'; }

.fa-traffic-light:before {
  content: '\f637'; }

.fa-train:before {
  content: '\f238'; }

.fa-transgender:before {
  content: '\f224'; }

.fa-transgender-alt:before {
  content: '\f225'; }

.fa-trash:before {
  content: '\f1f8'; }

.fa-trash-alt:before {
  content: '\f2ed'; }

.fa-tree:before {
  content: '\f1bb'; }

.fa-trello:before {
  content: '\f181'; }

.fa-tripadvisor:before {
  content: '\f262'; }

.fa-trophy:before {
  content: '\f091'; }

.fa-truck:before {
  content: '\f0d1'; }

.fa-truck-loading:before {
  content: '\f4de'; }

.fa-truck-monster:before {
  content: '\f63b'; }

.fa-truck-moving:before {
  content: '\f4df'; }

.fa-truck-pickup:before {
  content: '\f63c'; }

.fa-tshirt:before {
  content: '\f553'; }

.fa-tty:before {
  content: '\f1e4'; }

.fa-tumblr:before {
  content: '\f173'; }

.fa-tumblr-square:before {
  content: '\f174'; }

.fa-tv:before {
  content: '\f26c'; }

.fa-twitch:before {
  content: '\f1e8'; }

.fa-twitter:before {
  content: '\f099'; }

.fa-twitter-square:before {
  content: '\f081'; }

.fa-typo3:before {
  content: '\f42b'; }

.fa-uber:before {
  content: '\f402'; }

.fa-uikit:before {
  content: '\f403'; }

.fa-umbrella:before {
  content: '\f0e9'; }

.fa-umbrella-beach:before {
  content: '\f5ca'; }

.fa-underline:before {
  content: '\f0cd'; }

.fa-undo:before {
  content: '\f0e2'; }

.fa-undo-alt:before {
  content: '\f2ea'; }

.fa-uniregistry:before {
  content: '\f404'; }

.fa-universal-access:before {
  content: '\f29a'; }

.fa-university:before {
  content: '\f19c'; }

.fa-unlink:before {
  content: '\f127'; }

.fa-unlock:before {
  content: '\f09c'; }

.fa-unlock-alt:before {
  content: '\f13e'; }

.fa-untappd:before {
  content: '\f405'; }

.fa-upload:before {
  content: '\f093'; }

.fa-usb:before {
  content: '\f287'; }

.fa-user:before {
  content: '\f007'; }

.fa-user-alt:before {
  content: '\f406'; }

.fa-user-alt-slash:before {
  content: '\f4fa'; }

.fa-user-astronaut:before {
  content: '\f4fb'; }

.fa-user-check:before {
  content: '\f4fc'; }

.fa-user-circle:before {
  content: '\f2bd'; }

.fa-user-clock:before {
  content: '\f4fd'; }

.fa-user-cog:before {
  content: '\f4fe'; }

.fa-user-edit:before {
  content: '\f4ff'; }

.fa-user-friends:before {
  content: '\f500'; }

.fa-user-graduate:before {
  content: '\f501'; }

.fa-user-injured:before {
  content: '\f728'; }

.fa-user-lock:before {
  content: '\f502'; }

.fa-user-md:before {
  content: '\f0f0'; }

.fa-user-minus:before {
  content: '\f503'; }

.fa-user-ninja:before {
  content: '\f504'; }

.fa-user-plus:before {
  content: '\f234'; }

.fa-user-secret:before {
  content: '\f21b'; }

.fa-user-shield:before {
  content: '\f505'; }

.fa-user-slash:before {
  content: '\f506'; }

.fa-user-tag:before {
  content: '\f507'; }

.fa-user-tie:before {
  content: '\f508'; }

.fa-user-times:before {
  content: '\f235'; }

.fa-users:before {
  content: '\f0c0'; }

.fa-users-cog:before {
  content: '\f509'; }

.fa-ussunnah:before {
  content: '\f407'; }

.fa-utensil-spoon:before {
  content: '\f2e5'; }

.fa-utensils:before {
  content: '\f2e7'; }

.fa-vaadin:before {
  content: '\f408'; }

.fa-vector-square:before {
  content: '\f5cb'; }

.fa-venus:before {
  content: '\f221'; }

.fa-venus-double:before {
  content: '\f226'; }

.fa-venus-mars:before {
  content: '\f228'; }

.fa-viacoin:before {
  content: '\f237'; }

.fa-viadeo:before {
  content: '\f2a9'; }

.fa-viadeo-square:before {
  content: '\f2aa'; }

.fa-vial:before {
  content: '\f492'; }

.fa-vials:before {
  content: '\f493'; }

.fa-viber:before {
  content: '\f409'; }

.fa-video:before {
  content: '\f03d'; }

.fa-video-slash:before {
  content: '\f4e2'; }

.fa-vihara:before {
  content: '\f6a7'; }

.fa-vimeo:before {
  content: '\f40a'; }

.fa-vimeo-square:before {
  content: '\f194'; }

.fa-vimeo-v:before {
  content: '\f27d'; }

.fa-vine:before {
  content: '\f1ca'; }

.fa-vk:before {
  content: '\f189'; }

.fa-vnv:before {
  content: '\f40b'; }

.fa-volleyball-ball:before {
  content: '\f45f'; }

.fa-volume-down:before {
  content: '\f027'; }

.fa-volume-mute:before {
  content: '\f6a9'; }

.fa-volume-off:before {
  content: '\f026'; }

.fa-volume-up:before {
  content: '\f028'; }

.fa-vuejs:before {
  content: '\f41f'; }

.fa-walking:before {
  content: '\f554'; }

.fa-wallet:before {
  content: '\f555'; }

.fa-warehouse:before {
  content: '\f494'; }

.fa-weebly:before {
  content: '\f5cc'; }

.fa-weibo:before {
  content: '\f18a'; }

.fa-weight:before {
  content: '\f496'; }

.fa-weight-hanging:before {
  content: '\f5cd'; }

.fa-weixin:before {
  content: '\f1d7'; }

.fa-whatsapp:before {
  content: '\f232'; }

.fa-whatsapp-square:before {
  content: '\f40c'; }

.fa-wheelchair:before {
  content: '\f193'; }

.fa-whmcs:before {
  content: '\f40d'; }

.fa-wifi:before {
  content: '\f1eb'; }

.fa-wikipedia-w:before {
  content: '\f266'; }

.fa-wind:before {
  content: '\f72e'; }

.fa-window-close:before {
  content: '\f410'; }

.fa-window-maximize:before {
  content: '\f2d0'; }

.fa-window-minimize:before {
  content: '\f2d1'; }

.fa-window-restore:before {
  content: '\f2d2'; }

.fa-windows:before {
  content: '\f17a'; }

.fa-wine-bottle:before {
  content: '\f72f'; }

.fa-wine-glass:before {
  content: '\f4e3'; }

.fa-wine-glass-alt:before {
  content: '\f5ce'; }

.fa-wix:before {
  content: '\f5cf'; }

.fa-wizards-of-the-coast:before {
  content: '\f730'; }

.fa-wolf-pack-battalion:before {
  content: '\f514'; }

.fa-won-sign:before {
  content: '\f159'; }

.fa-wordpress:before {
  content: '\f19a'; }

.fa-wordpress-simple:before {
  content: '\f411'; }

.fa-wpbeginner:before {
  content: '\f297'; }

.fa-wpexplorer:before {
  content: '\f2de'; }

.fa-wpforms:before {
  content: '\f298'; }

.fa-wrench:before {
  content: '\f0ad'; }

.fa-x-ray:before {
  content: '\f497'; }

.fa-xbox:before {
  content: '\f412'; }

.fa-xing:before {
  content: '\f168'; }

.fa-xing-square:before {
  content: '\f169'; }

.fa-y-combinator:before {
  content: '\f23b'; }

.fa-yahoo:before {
  content: '\f19e'; }

.fa-yandex:before {
  content: '\f413'; }

.fa-yandex-international:before {
  content: '\f414'; }

.fa-yelp:before {
  content: '\f1e9'; }

.fa-yen-sign:before {
  content: '\f157'; }

.fa-yin-yang:before {
  content: '\f6ad'; }

.fa-yoast:before {
  content: '\f2b1'; }

.fa-youtube:before {
  content: '\f167'; }

.fa-youtube-square:before {
  content: '\f431'; }

.fa-zhihu:before {
  content: '\f63f'; }

.icon--sicon-phone:before {
  content: '\e941'; }

.icon--sicon-email:before {
  content: '\e942'; }

.icon--sicon-circle-o:before {
  content: '\e943'; }

 .icon--sicon-question:before {
  content: '\e957'; }

.icon--sicon-circle-thin:before {
  content: '\e944'; }

.icon--sicon-circle:before {
  content: '\e945'; }

.icon--sicon-circle-full:before {
  content: '\e946'; }

.icon--sicon-trash-o:before {
  content: '\e947'; }

.icon--sicon-checkbox-unchecked:before {
  content: "\e94d"; }

.icon--sicon-checkbox-checked:before {
  content: "\e94e"; }

.icon--sicon-bookmark-o:before {
  content: '\e948'; }

.icon--sicon-bookmark:before {
  content: '\e949'; }

.icon--sicon-insert-photo:before {
  content: '\e94c'; }

.icon--sicon-move:before {
  content: '\e94a'; }

.icon--sicon-drag-indicator:before {
  content: "\e94b"; }


.icon--sicon-star-rating:before {
  content: ""; }

.icon--sicon-star-o:before {
  content: ""; }

.icon--sicon-union-o:before {
  content: ""; }

.icon--sicon-union:before {
  content: ""; }

.icon--sicon-home:before {
    content: '\e94f';
}

.icon--sicon-chat:before {
    content: '\e950';
}

.icon--sicon-layers:before {
    content: '\e952';
}

.icon--sicon-guide:before {
  content: '\e953';
}

.icon--sicon-drop-next:before {
  content: '\e956';
}

.icon--sicon-equal:before {
  content: '\e958';
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto; }

/*!
 * Font Awesome Free 5.4.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/fa-brands-400.eot");
  src: url("../fonts/fa-brands-400.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-brands-400.woff2") format("woff2"), url("../fonts/fa-brands-400.woff") format("woff"), url("../fonts/fa-brands-400.ttf") format("truetype"), url("../fonts/fa-brands-400.svg#fontawesome") format("svg"); }

.fab {
  font-family: 'Font Awesome 5 Brands'; }

/*!
 * Font Awesome Free 5.4.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/fa-regular-400.eot");
  src: url("../fonts/fa-regular-400.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-regular-400.woff2") format("woff2"), url("../fonts/fa-regular-400.woff") format("woff"), url("../fonts/fa-regular-400.ttf") format("truetype"), url("../fonts/fa-regular-400.svg#fontawesome") format("svg"); }

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: normal; }

/*!
 * Font Awesome Free 5.4.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  src: url("../fonts/fa-solid-900.eot");
  src: url("../fonts/fa-solid-900.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-solid-900.woff2") format("woff2"), url("../fonts/fa-solid-900.woff") format("woff"), url("../fonts/fa-solid-900.ttf") format("truetype"), url("../fonts/fa-solid-900.svg#fontawesome") format("svg"); }

.fa,
.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900; }

/*
== malihu jquery custom scrollbar plugin ==
Plugin URI: http://manos.malihu.gr/jquery-custom-content-scroller
*/
/*
CONTENTS:
	1. BASIC STYLE - Plugin's basic/essential CSS properties (normally, should not be edited).
	2. VERTICAL SCROLLBAR - Positioning and dimensions of vertical scrollbar.
	3. HORIZONTAL SCROLLBAR - Positioning and dimensions of horizontal scrollbar.
	4. VERTICAL AND HORIZONTAL SCROLLBARS - Positioning and dimensions of 2-axis scrollbars.
	5. TRANSITIONS - CSS3 transitions for hover events, auto-expanded and auto-hidden scrollbars.
	6. SCROLLBAR COLORS, OPACITY AND BACKGROUNDS
		6.1 THEMES - Scrollbar colors, opacity, dimensions, backgrounds etc. via ready-to-use themes.
*/
/*
------------------------------------------------------------------------------------------------------------------------
1. BASIC STYLE
------------------------------------------------------------------------------------------------------------------------
*/
.mCustomScrollbar {
  touch-action: pinch-zoom;
  /* direct pointer events to js */ }

.mCustomScrollbar.mCS_no_scrollbar, .mCustomScrollbar.mCS_touch_action {
  touch-action: auto; }

.mCustomScrollBox {
  /* contains plugin's markup */
  position: relative;
  overflow: hidden;
  height: 100%;
  max-width: 100%;
  outline: none;
  direction: ltr; }

.mCSB_container {
  /* contains the original content */
  overflow: hidden;
  width: auto;
  height: auto; }

/*
------------------------------------------------------------------------------------------------------------------------
2. VERTICAL SCROLLBAR
y-axis
------------------------------------------------------------------------------------------------------------------------
*/
.mCSB_inside > .mCSB_container {
  margin-right: 30px; }

.mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden {
  margin-right: 0; }

/* non-visible scrollbar */
.mCS-dir-rtl > .mCSB_inside > .mCSB_container {
  /* RTL direction/left-side scrollbar */
  margin-right: 0;
  margin-left: 30px; }

.mCS-dir-rtl > .mCSB_inside > .mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden {
  margin-left: 0; }

/* RTL direction/left-side scrollbar */
.mCSB_scrollTools {
  /* contains scrollbar markup (draggable element, dragger rail, buttons etc.) */
  position: absolute;
  width: 16px;
  height: auto;
  left: auto;
  top: 0;
  right: 0;
  bottom: 0; }

.mCSB_outside + .mCSB_scrollTools {
  right: -26px; }

/* scrollbar position: outside */
.mCS-dir-rtl > .mCSB_inside > .mCSB_scrollTools,
.mCS-dir-rtl > .mCSB_outside + .mCSB_scrollTools {
  /* RTL direction/left-side scrollbar */
  right: auto;
  left: 0; }

.mCS-dir-rtl > .mCSB_outside + .mCSB_scrollTools {
  left: -26px; }

/* RTL direction/left-side scrollbar (scrollbar position: outside) */
.mCSB_scrollTools .mCSB_draggerContainer {
  /* contains the draggable element and dragger rail markup */
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  height: auto; }

.mCSB_scrollTools a + .mCSB_draggerContainer {
  margin: 20px 0; }

.mCSB_scrollTools .mCSB_draggerRail {
  width: 2px;
  height: 100%;
  margin: 0 auto;
  border-radius: 16px; }

.mCSB_scrollTools .mCSB_dragger {
  /* the draggable element */
  cursor: pointer;
  width: 100%;
  height: 30px;
  /* minimum dragger height */
  z-index: 1; }

.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  /* the dragger element */
  position: relative;
  width: 4px;
  height: 100%;
  margin: 0 auto;
  border-radius: 16px;
  text-align: center; }

.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar {
  width: 12px;
  /* auto-expanded scrollbar */ }

.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  width: 8px;
  /* auto-expanded scrollbar */ }

.mCSB_scrollTools .mCSB_buttonUp,
.mCSB_scrollTools .mCSB_buttonDown {
  display: block;
  position: absolute;
  height: 20px;
  width: 100%;
  overflow: hidden;
  margin: 0 auto;
  cursor: pointer; }

.mCSB_scrollTools .mCSB_buttonDown {
  bottom: 0; }

/*
------------------------------------------------------------------------------------------------------------------------
3. HORIZONTAL SCROLLBAR
x-axis
------------------------------------------------------------------------------------------------------------------------
*/
.mCSB_horizontal.mCSB_inside > .mCSB_container {
  margin-right: 0;
  margin-bottom: 30px; }

.mCSB_horizontal.mCSB_outside > .mCSB_container {
  min-height: 100%; }

.mCSB_horizontal > .mCSB_container.mCS_no_scrollbar_x.mCS_x_hidden {
  margin-bottom: 0; }

/* non-visible scrollbar */
.mCSB_scrollTools.mCSB_scrollTools_horizontal {
  width: auto;
  height: 16px;
  top: auto;
  right: 0;
  bottom: 0;
  left: 0; }

.mCustomScrollBox + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox + .mCSB_scrollTools + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  bottom: -26px; }

/* scrollbar position: outside */
.mCSB_scrollTools.mCSB_scrollTools_horizontal a + .mCSB_draggerContainer {
  margin: 0 20px; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%;
  height: 2px;
  margin: 7px 0; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_dragger {
  width: 30px;
  /* minimum dragger width */
  height: 100%;
  left: 0; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  width: 100%;
  height: 4px;
  margin: 6px auto; }

.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar {
  height: 12px;
  /* auto-expanded scrollbar */
  margin: 2px auto; }

.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  height: 8px;
  /* auto-expanded scrollbar */
  margin: 4px 0; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_buttonLeft,
.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_buttonRight {
  display: block;
  position: absolute;
  width: 20px;
  height: 100%;
  overflow: hidden;
  margin: 0 auto;
  cursor: pointer; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_buttonLeft {
  left: 0; }

.mCSB_scrollTools.mCSB_scrollTools_horizontal .mCSB_buttonRight {
  right: 0; }

/*
------------------------------------------------------------------------------------------------------------------------
4. VERTICAL AND HORIZONTAL SCROLLBARS
yx-axis
------------------------------------------------------------------------------------------------------------------------
*/
.mCSB_container_wrapper {
  position: absolute;
  height: auto;
  width: auto;
  overflow: hidden;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-right: 30px;
  margin-bottom: 30px; }

.mCSB_container_wrapper > .mCSB_container {
  padding-right: 30px;
  padding-bottom: 30px;
  box-sizing: border-box; }

.mCSB_vertical_horizontal > .mCSB_scrollTools.mCSB_scrollTools_vertical {
  bottom: 20px; }

.mCSB_vertical_horizontal > .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  right: 20px; }

/* non-visible horizontal scrollbar */
.mCSB_container_wrapper.mCS_no_scrollbar_x.mCS_x_hidden + .mCSB_scrollTools.mCSB_scrollTools_vertical {
  bottom: 0; }

/* non-visible vertical scrollbar/RTL direction/left-side scrollbar */
.mCSB_container_wrapper.mCS_no_scrollbar_y.mCS_y_hidden + .mCSB_scrollTools ~ .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCS-dir-rtl > .mCustomScrollBox.mCSB_vertical_horizontal.mCSB_inside > .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  right: 0; }

/* RTL direction/left-side scrollbar */
.mCS-dir-rtl > .mCustomScrollBox.mCSB_vertical_horizontal.mCSB_inside > .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  left: 20px; }

/* non-visible scrollbar/RTL direction/left-side scrollbar */
.mCS-dir-rtl > .mCustomScrollBox.mCSB_vertical_horizontal.mCSB_inside > .mCSB_container_wrapper.mCS_no_scrollbar_y.mCS_y_hidden + .mCSB_scrollTools ~ .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  left: 0; }

.mCS-dir-rtl > .mCSB_inside > .mCSB_container_wrapper {
  /* RTL direction/left-side scrollbar */
  margin-right: 0;
  margin-left: 30px; }

.mCSB_container_wrapper.mCS_no_scrollbar_y.mCS_y_hidden > .mCSB_container {
  padding-right: 0; }

.mCSB_container_wrapper.mCS_no_scrollbar_x.mCS_x_hidden > .mCSB_container {
  padding-bottom: 0; }

.mCustomScrollBox.mCSB_vertical_horizontal.mCSB_inside > .mCSB_container_wrapper.mCS_no_scrollbar_y.mCS_y_hidden {
  margin-right: 0;
  /* non-visible scrollbar */
  margin-left: 0; }

/* non-visible horizontal scrollbar */
.mCustomScrollBox.mCSB_vertical_horizontal.mCSB_inside > .mCSB_container_wrapper.mCS_no_scrollbar_x.mCS_x_hidden {
  margin-bottom: 0; }

/*
------------------------------------------------------------------------------------------------------------------------
5. TRANSITIONS
------------------------------------------------------------------------------------------------------------------------
*/
.mCSB_scrollTools,
.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCSB_scrollTools .mCSB_buttonUp,
.mCSB_scrollTools .mCSB_buttonDown,
.mCSB_scrollTools .mCSB_buttonLeft,
.mCSB_scrollTools .mCSB_buttonRight {
  transition: opacity .2s ease-in-out, background-color .2s ease-in-out; }

.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger_bar,
.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerRail,
.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger_bar,
.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerRail {
  transition: width .2s ease-out .2s, height .2s ease-out .2s,
 margin-left .2s ease-out .2s, margin-right .2s ease-out .2s,
 margin-top .2s ease-out .2s, margin-bottom .2s ease-out .2s,
 opacity .2s ease-in-out, background-color .2s ease-in-out; }

/*
------------------------------------------------------------------------------------------------------------------------
6. SCROLLBAR COLORS, OPACITY AND BACKGROUNDS
------------------------------------------------------------------------------------------------------------------------
*/
/*
	----------------------------------------
	6.1 THEMES
	----------------------------------------
	*/
/* default theme ("light") */
.mCSB_scrollTools {
  opacity: 0.75;
  filter: "alpha(opacity=75)";
  -ms-filter: "alpha(opacity=75)"; }

.mCS-autoHide > .mCustomScrollBox > .mCSB_scrollTools,
.mCS-autoHide > .mCustomScrollBox ~ .mCSB_scrollTools {
  opacity: 0;
  filter: "alpha(opacity=0)";
  -ms-filter: "alpha(opacity=0)"; }

.mCustomScrollbar > .mCustomScrollBox > .mCSB_scrollTools.mCSB_scrollTools_onDrag,
.mCustomScrollbar > .mCustomScrollBox ~ .mCSB_scrollTools.mCSB_scrollTools_onDrag,
.mCustomScrollBox:hover > .mCSB_scrollTools,
.mCustomScrollBox:hover ~ .mCSB_scrollTools,
.mCS-autoHide:hover > .mCustomScrollBox > .mCSB_scrollTools,
.mCS-autoHide:hover > .mCustomScrollBox ~ .mCSB_scrollTools {
  opacity: 1;
  filter: "alpha(opacity=100)";
  -ms-filter: "alpha(opacity=100)"; }

.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.4);
  filter: "alpha(opacity=40)";
  -ms-filter: "alpha(opacity=40)"; }

.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.75);
  filter: "alpha(opacity=75)";
  -ms-filter: "alpha(opacity=75)"; }

.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.85);
  filter: "alpha(opacity=85)";
  -ms-filter: "alpha(opacity=85)"; }

.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.9);
  filter: "alpha(opacity=90)";
  -ms-filter: "alpha(opacity=90)"; }

.mCSB_scrollTools .mCSB_buttonUp,
.mCSB_scrollTools .mCSB_buttonDown,
.mCSB_scrollTools .mCSB_buttonLeft,
.mCSB_scrollTools .mCSB_buttonRight {
  background-image: url(mCSB_buttons.png);
  /* css sprites */
  background-repeat: no-repeat;
  opacity: 0.4;
  filter: "alpha(opacity=40)";
  -ms-filter: "alpha(opacity=40)"; }

.mCSB_scrollTools .mCSB_buttonUp {
  background-position: 0 0;
  /*
		sprites locations
		light: 0 0, -16px 0, -32px 0, -48px 0, 0 -72px, -16px -72px, -32px -72px
		dark: -80px 0, -96px 0, -112px 0, -128px 0, -80px -72px, -96px -72px, -112px -72px
		*/ }

.mCSB_scrollTools .mCSB_buttonDown {
  background-position: 0 -20px;
  /*
		sprites locations
		light: 0 -20px, -16px -20px, -32px -20px, -48px -20px, 0 -92px, -16px -92px, -32px -92px
		dark: -80px -20px, -96px -20px, -112px -20px, -128px -20px, -80px -92px, -96px -92px, -112 -92px
		*/ }

.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: 0 -40px;
  /*
		sprites locations
		light: 0 -40px, -20px -40px, -40px -40px, -60px -40px, 0 -112px, -20px -112px, -40px -112px
		dark: -80px -40px, -100px -40px, -120px -40px, -140px -40px, -80px -112px, -100px -112px, -120px -112px
		*/ }

.mCSB_scrollTools .mCSB_buttonRight {
  background-position: 0 -56px;
  /*
		sprites locations
		light: 0 -56px, -20px -56px, -40px -56px, -60px -56px, 0 -128px, -20px -128px, -40px -128px
		dark: -80px -56px, -100px -56px, -120px -56px, -140px -56px, -80px -128px, -100px -128px, -120px -128px
		*/ }

.mCSB_scrollTools .mCSB_buttonUp:hover,
.mCSB_scrollTools .mCSB_buttonDown:hover,
.mCSB_scrollTools .mCSB_buttonLeft:hover,
.mCSB_scrollTools .mCSB_buttonRight:hover {
  opacity: 0.75;
  filter: "alpha(opacity=75)";
  -ms-filter: "alpha(opacity=75)"; }

.mCSB_scrollTools .mCSB_buttonUp:active,
.mCSB_scrollTools .mCSB_buttonDown:active,
.mCSB_scrollTools .mCSB_buttonLeft:active,
.mCSB_scrollTools .mCSB_buttonRight:active {
  opacity: 0.9;
  filter: "alpha(opacity=90)";
  -ms-filter: "alpha(opacity=90)"; }

/* theme: "dark" */
.mCS-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.15); }

.mCS-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -80px 0; }

.mCS-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -80px -20px; }

.mCS-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -80px -40px; }

.mCS-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -80px -56px; }

/* ---------------------------------------- */
/* theme: "light-2", "dark-2" */
.mCS-light-2.mCSB_scrollTools .mCSB_draggerRail,
.mCS-dark-2.mCSB_scrollTools .mCSB_draggerRail {
  width: 4px;
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 1px; }

.mCS-light-2.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-2.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 4px;
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.75);
  border-radius: 1px; }

.mCS-light-2.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-dark-2.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-light-2.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-2.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  width: 100%;
  height: 4px;
  margin: 6px auto; }

.mCS-light-2.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.85); }

.mCS-light-2.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-light-2.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.9); }

.mCS-light-2.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -32px 0; }

.mCS-light-2.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -32px -20px; }

.mCS-light-2.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -40px -40px; }

.mCS-light-2.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -40px -56px; }

/* theme: "dark-2" */
.mCS-dark-2.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 1px; }

.mCS-dark-2.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 1px; }

.mCS-dark-2.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-dark-2.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-dark-2.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-dark-2.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -112px 0; }

.mCS-dark-2.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -112px -20px; }

.mCS-dark-2.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -120px -40px; }

.mCS-dark-2.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -120px -56px; }

/* ---------------------------------------- */
/* theme: "light-thick", "dark-thick" */
.mCS-light-thick.mCSB_scrollTools .mCSB_draggerRail,
.mCS-dark-thick.mCSB_scrollTools .mCSB_draggerRail {
  width: 4px;
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 2px; }

.mCS-light-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 6px;
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.75);
  border-radius: 2px; }

.mCS-light-thick.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-dark-thick.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%;
  height: 4px;
  margin: 6px 0; }

.mCS-light-thick.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-thick.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  width: 100%;
  height: 6px;
  margin: 5px auto; }

.mCS-light-thick.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.85); }

.mCS-light-thick.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-light-thick.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.9); }

.mCS-light-thick.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -16px 0; }

.mCS-light-thick.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -16px -20px; }

.mCS-light-thick.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -20px -40px; }

.mCS-light-thick.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -20px -56px; }

/* theme: "dark-thick" */
.mCS-dark-thick.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 2px; }

.mCS-dark-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75);
  border-radius: 2px; }

.mCS-dark-thick.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-dark-thick.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-dark-thick.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-dark-thick.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -96px 0; }

.mCS-dark-thick.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -96px -20px; }

.mCS-dark-thick.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -100px -40px; }

.mCS-dark-thick.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -100px -56px; }

/* ---------------------------------------- */
/* theme: "light-thin", "dark-thin" */
.mCS-light-thin.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.1); }

.mCS-light-thin.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-thin.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 2px; }

.mCS-light-thin.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-dark-thin.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%; }

.mCS-light-thin.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-thin.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  width: 100%;
  height: 2px;
  margin: 7px auto; }

/* theme "dark-thin" */
.mCS-dark-thin.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.15); }

.mCS-dark-thin.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-dark-thin.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-dark-thin.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-dark-thin.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-dark-thin.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -80px 0; }

.mCS-dark-thin.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -80px -20px; }

.mCS-dark-thin.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -80px -40px; }

.mCS-dark-thin.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -80px -56px; }

/* ---------------------------------------- */
/* theme "rounded", "rounded-dark", "rounded-dots", "rounded-dots-dark" */
.mCS-rounded.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.15); }

.mCS-rounded.mCSB_scrollTools .mCSB_dragger,
.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger,
.mCS-rounded-dots.mCSB_scrollTools .mCSB_dragger,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger {
  height: 14px; }

.mCS-rounded.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dots.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 14px;
  margin: 0 1px; }

.mCS-rounded.mCSB_scrollTools_horizontal .mCSB_dragger,
.mCS-rounded-dark.mCSB_scrollTools_horizontal .mCSB_dragger,
.mCS-rounded-dots.mCSB_scrollTools_horizontal .mCSB_dragger,
.mCS-rounded-dots-dark.mCSB_scrollTools_horizontal .mCSB_dragger {
  width: 14px; }

.mCS-rounded.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dots.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  height: 14px;
  margin: 1px 0; }

.mCS-rounded.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCS-rounded.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar {
  width: 16px;
  /* auto-expanded scrollbar */
  height: 16px;
  margin: -1px 0; }

.mCS-rounded.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-rounded.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail,
.mCS-rounded-dark.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-rounded-dark.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  width: 4px;
  /* auto-expanded scrollbar */ }

.mCS-rounded.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCS-rounded.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_dragger .mCSB_dragger_bar {
  height: 16px;
  /* auto-expanded scrollbar */
  width: 16px;
  margin: 0 -1px; }

.mCS-rounded.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-rounded.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail,
.mCS-rounded-dark.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-rounded-dark.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  height: 4px;
  /* auto-expanded scrollbar */
  margin: 6px 0; }

.mCS-rounded.mCSB_scrollTools .mCSB_buttonUp {
  background-position: 0 -72px; }

.mCS-rounded.mCSB_scrollTools .mCSB_buttonDown {
  background-position: 0 -92px; }

.mCS-rounded.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: 0 -112px; }

.mCS-rounded.mCSB_scrollTools .mCSB_buttonRight {
  background-position: 0 -128px; }

/* theme "rounded-dark", "rounded-dots-dark" */
.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.15); }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-rounded-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -80px -72px; }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -80px -92px; }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -80px -112px; }

.mCS-rounded-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -80px -128px; }

/* theme "rounded-dots", "rounded-dots-dark" */
.mCS-rounded-dots.mCSB_scrollTools_vertical .mCSB_draggerRail,
.mCS-rounded-dots-dark.mCSB_scrollTools_vertical .mCSB_draggerRail {
  width: 4px; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_draggerRail,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-rounded-dots.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-rounded-dots-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  background-color: transparent;
  background-position: center; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_draggerRail,
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAANElEQVQYV2NkIAAYiVbw//9/Y6DiM1ANJoyMjGdBbLgJQAX/kU0DKgDLkaQAvxW4HEvQFwCRcxIJK1XznAAAAABJRU5ErkJggg==");
  background-repeat: repeat-y;
  opacity: 0.3;
  filter: "alpha(opacity=30)";
  -ms-filter: "alpha(opacity=30)"; }

.mCS-rounded-dots.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-rounded-dots-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  height: 4px;
  margin: 6px 0;
  background-repeat: repeat-x; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -16px -72px; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -16px -92px; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -20px -112px; }

.mCS-rounded-dots.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -20px -128px; }

/* theme "rounded-dots-dark" */
.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAALElEQVQYV2NkIAAYSVFgDFR8BqrBBEifBbGRTfiPZhpYjiQFBK3A6l6CvgAAE9kGCd1mvgEAAAAASUVORK5CYII="); }

.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -96px -72px; }

.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -96px -92px; }

.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -100px -112px; }

.mCS-rounded-dots-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -100px -128px; }

/* ---------------------------------------- */
/* theme "3d", "3d-dark", "3d-thick", "3d-thick-dark" */
.mCS-3d.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-repeat: repeat-y;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%); }

.mCS-3d.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  background-repeat: repeat-x;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%); }

/* theme "3d", "3d-dark" */
.mCS-3d.mCSB_scrollTools_vertical .mCSB_dragger,
.mCS-3d-dark.mCSB_scrollTools_vertical .mCSB_dragger {
  height: 70px; }

.mCS-3d.mCSB_scrollTools_horizontal .mCSB_dragger,
.mCS-3d-dark.mCSB_scrollTools_horizontal .mCSB_dragger {
  width: 70px; }

.mCS-3d.mCSB_scrollTools,
.mCS-3d-dark.mCSB_scrollTools {
  opacity: 1;
  filter: "alpha(opacity=30)";
  -ms-filter: "alpha(opacity=30)"; }

.mCS-3d.mCSB_scrollTools .mCSB_draggerRail,
.mCS-3d.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  border-radius: 16px; }

.mCS-3d.mCSB_scrollTools .mCSB_draggerRail,
.mCS-3d-dark.mCSB_scrollTools .mCSB_draggerRail {
  width: 8px;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.2);
  box-shadow: inset 1px 0 1px rgba(0, 0, 0, 0.5), inset -1px 0 1px rgba(255, 255, 255, 0.2); }

.mCS-3d.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-3d.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-3d.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #555; }

.mCS-3d.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 8px; }

.mCS-3d.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-3d-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%;
  height: 8px;
  margin: 4px 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.5), inset 0 -1px 1px rgba(255, 255, 255, 0.2); }

.mCS-3d.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  width: 100%;
  height: 8px;
  margin: 4px auto; }

.mCS-3d.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -32px -72px; }

.mCS-3d.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -32px -92px; }

.mCS-3d.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -40px -112px; }

.mCS-3d.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -40px -128px; }

/* theme "3d-dark" */
.mCS-3d-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: inset 1px 0 1px rgba(0, 0, 0, 0.1); }

.mCS-3d-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1); }

.mCS-3d-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -112px -72px; }

.mCS-3d-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -112px -92px; }

.mCS-3d-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -120px -112px; }

.mCS-3d-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -120px -128px; }

/* ---------------------------------------- */
/* theme: "3d-thick", "3d-thick-dark" */
.mCS-3d-thick.mCSB_scrollTools,
.mCS-3d-thick-dark.mCSB_scrollTools {
  opacity: 1;
  filter: "alpha(opacity=30)";
  -ms-filter: "alpha(opacity=30)"; }

.mCS-3d-thick.mCSB_scrollTools,
.mCS-3d-thick-dark.mCSB_scrollTools,
.mCS-3d-thick.mCSB_scrollTools .mCSB_draggerContainer,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_draggerContainer {
  border-radius: 7px; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  border-radius: 5px; }

.mCSB_inside + .mCS-3d-thick.mCSB_scrollTools_vertical,
.mCSB_inside + .mCS-3d-thick-dark.mCSB_scrollTools_vertical {
  right: 1px; }

.mCS-3d-thick.mCSB_scrollTools_vertical,
.mCS-3d-thick-dark.mCSB_scrollTools_vertical {
  box-shadow: inset 1px 0 1px rgba(0, 0, 0, 0.1), inset 0 0 14px rgba(0, 0, 0, 0.5); }

.mCS-3d-thick.mCSB_scrollTools_horizontal,
.mCS-3d-thick-dark.mCSB_scrollTools_horizontal {
  bottom: 1px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1), inset 0 0 14px rgba(0, 0, 0, 0.5); }

.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.4);
  width: 12px;
  margin: 2px;
  position: absolute;
  height: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0; }

.mCS-3d-thick.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4); }

.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-3d-thick.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #555; }

.mCS-3d-thick.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  height: 12px;
  width: auto; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_draggerContainer {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: inset 1px 1px 16px rgba(0, 0, 0, 0.1); }

.mCS-3d-thick.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -32px -72px; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -32px -92px; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -40px -112px; }

.mCS-3d-thick.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -40px -128px; }

/* theme: "3d-thick-dark" */
.mCS-3d-thick-dark.mCSB_scrollTools {
  box-shadow: inset 0 0 14px rgba(0, 0, 0, 0.2); }

.mCS-3d-thick-dark.mCSB_scrollTools_horizontal {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1), inset 0 0 14px rgba(0, 0, 0, 0.2); }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.4), inset -1px 0 0 rgba(0, 0, 0, 0.2); }

.mCS-3d-thick-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), inset 0 -1px 0 rgba(0, 0, 0, 0.2); }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #777; }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_draggerContainer {
  background-color: #fff;
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: inset 1px 1px 16px rgba(0, 0, 0, 0.1); }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent; }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -112px -72px; }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -112px -92px; }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -120px -112px; }

.mCS-3d-thick-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -120px -128px; }

/* ---------------------------------------- */
/* theme: "minimal", "minimal-dark" */
.mCSB_outside + .mCS-minimal.mCSB_scrollTools_vertical,
.mCSB_outside + .mCS-minimal-dark.mCSB_scrollTools_vertical {
  right: 0;
  margin: 12px 0; }

.mCustomScrollBox.mCS-minimal + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox.mCS-minimal + .mCSB_scrollTools + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  bottom: 0;
  margin: 0 12px; }

/* RTL direction/left-side scrollbar */
.mCS-dir-rtl > .mCSB_outside + .mCS-minimal.mCSB_scrollTools_vertical,
.mCS-dir-rtl > .mCSB_outside + .mCS-minimal-dark.mCSB_scrollTools_vertical {
  left: 0;
  right: auto; }

.mCS-minimal.mCSB_scrollTools .mCSB_draggerRail,
.mCS-minimal-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent; }

.mCS-minimal.mCSB_scrollTools_vertical .mCSB_dragger,
.mCS-minimal-dark.mCSB_scrollTools_vertical .mCSB_dragger {
  height: 50px; }

.mCS-minimal.mCSB_scrollTools_horizontal .mCSB_dragger,
.mCS-minimal-dark.mCSB_scrollTools_horizontal .mCSB_dragger {
  width: 50px; }

.mCS-minimal.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  filter: "alpha(opacity=20)";
  -ms-filter: "alpha(opacity=20)"; }

.mCS-minimal.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-minimal.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.5);
  filter: "alpha(opacity=50)";
  -ms-filter: "alpha(opacity=50)"; }

/* theme: "minimal-dark" */
.mCS-minimal-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.2);
  filter: "alpha(opacity=20)";
  -ms-filter: "alpha(opacity=20)"; }

.mCS-minimal-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-minimal-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.5);
  filter: "alpha(opacity=50)";
  -ms-filter: "alpha(opacity=50)"; }

/* ---------------------------------------- */
/* theme "light-3", "dark-3" */
.mCS-light-3.mCSB_scrollTools .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools .mCSB_draggerRail {
  width: 6px;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.2); }

.mCS-light-3.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-3.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 6px; }

.mCS-light-3.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-dark-3.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-light-3.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%;
  height: 6px;
  margin: 5px 0; }

.mCS-light-3.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-light-3.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools_vertical.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  width: 12px; }

.mCS-light-3.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-light-3.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_dragger.mCSB_dragger_onDrag_expanded + .mCSB_draggerRail,
.mCS-dark-3.mCSB_scrollTools_horizontal.mCSB_scrollTools_onDrag_expand .mCSB_draggerContainer:hover .mCSB_draggerRail {
  height: 12px;
  margin: 2px 0; }

.mCS-light-3.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -32px -72px; }

.mCS-light-3.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -32px -92px; }

.mCS-light-3.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -40px -112px; }

.mCS-light-3.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -40px -128px; }

/* theme "dark-3" */
.mCS-dark-3.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-dark-3.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-dark-3.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-dark-3.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-dark-3.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.1); }

.mCS-dark-3.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -112px -72px; }

.mCS-dark-3.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -112px -92px; }

.mCS-dark-3.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -120px -112px; }

.mCS-dark-3.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -120px -128px; }

/* ---------------------------------------- */
/* theme "inset", "inset-dark", "inset-2", "inset-2-dark", "inset-3", "inset-3-dark" */
.mCS-inset.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-2.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-3.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_draggerRail {
  width: 12px;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.2); }

.mCS-inset.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-2.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-3.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 6px;
  margin: 3px 5px;
  position: absolute;
  height: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0; }

.mCS-inset.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-2.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-3.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools_horizontal .mCSB_dragger .mCSB_dragger_bar {
  height: 6px;
  margin: 5px 3px;
  position: absolute;
  width: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0; }

.mCS-inset.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-inset-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-inset-2.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-inset-2-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-inset-3.mCSB_scrollTools_horizontal .mCSB_draggerRail,
.mCS-inset-3-dark.mCSB_scrollTools_horizontal .mCSB_draggerRail {
  width: 100%;
  height: 12px;
  margin: 2px 0; }

.mCS-inset.mCSB_scrollTools .mCSB_buttonUp,
.mCS-inset-2.mCSB_scrollTools .mCSB_buttonUp,
.mCS-inset-3.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -32px -72px; }

.mCS-inset.mCSB_scrollTools .mCSB_buttonDown,
.mCS-inset-2.mCSB_scrollTools .mCSB_buttonDown,
.mCS-inset-3.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -32px -92px; }

.mCS-inset.mCSB_scrollTools .mCSB_buttonLeft,
.mCS-inset-2.mCSB_scrollTools .mCSB_buttonLeft,
.mCS-inset-3.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -40px -112px; }

.mCS-inset.mCSB_scrollTools .mCSB_buttonRight,
.mCS-inset-2.mCSB_scrollTools .mCSB_buttonRight,
.mCS-inset-3.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -40px -128px; }

/* theme "inset-dark", "inset-2-dark", "inset-3-dark" */
.mCS-inset-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-inset-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-inset-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-inset-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-inset-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.1); }

.mCS-inset-dark.mCSB_scrollTools .mCSB_buttonUp,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_buttonUp,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -112px -72px; }

.mCS-inset-dark.mCSB_scrollTools .mCSB_buttonDown,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_buttonDown,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -112px -92px; }

.mCS-inset-dark.mCSB_scrollTools .mCSB_buttonLeft,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_buttonLeft,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -120px -112px; }

.mCS-inset-dark.mCSB_scrollTools .mCSB_buttonRight,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_buttonRight,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -120px -128px; }

/* theme "inset-2", "inset-2-dark" */
.mCS-inset-2.mCSB_scrollTools .mCSB_draggerRail,
.mCS-inset-2-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: transparent;
  border-width: 1px;
  border-style: solid;
  border-color: #fff;
  border-color: rgba(255, 255, 255, 0.2);
  box-sizing: border-box; }

.mCS-inset-2-dark.mCSB_scrollTools .mCSB_draggerRail {
  border-color: #000;
  border-color: rgba(0, 0, 0, 0.2); }

/* theme "inset-3", "inset-3-dark" */
.mCS-inset-3.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.6); }

.mCS-inset-3-dark.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.6); }

.mCS-inset-3.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.75); }

.mCS-inset-3.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.85); }

.mCS-inset-3.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-inset-3.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.9); }

.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.75); }

.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.85); }

.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-inset-3-dark.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
  background-color: #fff;
  background-color: rgba(255, 255, 255, 0.9); }

/* ---------------------------------------- */
@font-face {
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  src: url("../fonts/AxisRound50StdN-R.otf"); }

@font-face {
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  src: url("../fonts/AxisRound50StdN-L.otf"); }

  :root {
  --app-height: 100%; 
  --bg-album: #a7a8a9;
}

body {
  color: #000000;
  font-family: "A+mfCv-AXISラウンド 50 L StdN";
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden; }

main {
  margin-top: 58px; }

.srm3 {
  color: #000000 !important;
  background-color: #fcfcfc;
  font-weight: 300;
  font-size: 13px; }
  .srm3 main {
    margin-top: 64px; }
  .srm3 img {
    max-width: 100%;
  }

.main__skeleton {
    margin-top: 0 !important;
}

.unstyled,
.unstyled li {
  list-style: none;
  padding: 0; }

a:focus, a:hover {
  text-decoration: none;}

.unstyled a:hover {
  text-decoration: none; }

.button:focus, .button:active {
  outline: 0;
  outline-offset: 0; }

video:focus {
  outline: 0; }

.custom-col {
  padding: 0 10px; }
  @media (max-width: 576px) {
    .custom-col {
      padding: 0 3px; } }

.custom-row {
  margin: 0 -10px; }
  @media (max-width: 576px) {
    .custom-row {
      margin: 0 -3px; } }

.srm-icon {
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  background-color: #a7a8a9; }

.srm-delete {
  -webkit-mask-image: url("../images/icon-delete.svg");
  mask-image: url("../images/icon-delete.svg");
  width: 24px;
  height: 24px; }

.srm-launch {
  -webkit-mask-image: url("../images/icon-launch.svg");
  mask-image: url("../images/icon-launch.svg");
  width: 24px;
  height: 24px; }

.srm-add {
  -webkit-mask-image: url("../images/icon-adds.svg");
  mask-image: url("../images/icon-adds.svg");
  width: 24px;
  height: 24px; }

.header {
  position: fixed;
  width: 100%;
  background-color: #fff;
  z-index: 9999;
  top: 0; }
  @media (min-width: 992px) {
    .header {
      box-shadow: 1px 0 8px #a7a8a9; } }

.logo-wrap {
  margin-right: 30px; }

.header-wrap {
  display: flex;
  justify-content: space-between;
  align-items: normal;
  height: 58px; }
  .header-wrap__left {
    display: flex; }
  .header-wrap__right {
    display: flex; }

.header-top-sp .header-wrap__right {
    margin-right: 70px;
}

.menu-sp {
  display: none; }

@media (max-width: 992px) {
  .menu-sp {
    display: block; }
  .menu-pc {
    display: none; } }

.menu-wrap ul {
  display: flex;
  align-items: baseline;
  padding: 0; }

.menu-wrap li {
  list-style: none; }

.menu-wrap a {
  padding: 2px 0;
  position: relative;
  transition: all .3s ease;
  letter-spacing: 2px;
  text-transform: uppercase; }
  .menu-wrap a:after {
    content: '';
    width: 100%;
    height: 2px;
    position: absolute;
    bottom: 0;
    left: 0;
    visibility: hidden;
    opacity: 0;
    transition: all .3s ease; }
  .menu-wrap a:hover {
    text-decoration: none; }
    .menu-wrap a:hover:after {
      visibility: visible;
      opacity: 1; }

.menu-wrap .active:after {
  visibility: visible;
  opacity: 1; }

.menu-wrap .item-main {
  margin-right: 25px;
  padding: 17px 0; }
  .menu-wrap .item-main a {
    color: #009ace;
    font-size: 18px; }
    @media (max-width: 1199px) {
      .menu-wrap .item-main a {
        font-size: 15px; } }
    .menu-wrap .item-main a:after {
      background-color: #009ace; }

.menu-wrap .item-second {
  margin-left: 25px; }
  .menu-wrap .item-second a {
    color: #000;
    font-size: 13px; }
    @media (max-width: 1199px) {
      .menu-wrap .item-second a {
        font-size: 13px; } }
    .menu-wrap .item-second a:after {
      background-color: #000; }

.link-header {
  display: flex;
  align-content: center;
  margin-left: 20px; }
  .link-header-login {
    padding: 15px 0; }
  .link-header img {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden; }
  .link-header__name {
    white-space: nowrap;
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0;
    line-height: 2.2;
    font-size: 12px;
    margin-right: 5px;
    color: #000; }
    .link-header__name:hover, .link-header__name:focus {
      color: #009ace; }
    @media (max-width: 576px) {
      .link-header__name {
        max-width: 70px; } }
  .link-header__dropdown {
    max-width: 200px;
    top: 100%;
    left: auto;
    right: 0; }
    .link-header__dropdown .dropdown-item a {
      white-space: normal;
      word-break: break-word;
      font-size: 12px;
      padding: 10px 12px; }
  .link-header-toggle {
    display: flex;
    align-items: center;
    padding: 15px 5px; }
    .link-header-toggle:hover, .link-header-toggle:focus {
      text-decoration: none;
      outline: none;
      cursor: pointer; }
  .link-header > .show {
    background-color: #a7a8a9; }

.account-link {
  position: relative; }
  .account-link__toggle {
    color: #000;
    font-size: 12px;
    padding: 20px 5px;
    float: left; }
    .account-link__toggle:hover, .account-link__toggle:active, .account-link__toggle:focus {
      text-decoration: none;
      color: #0076a5; }
  .account-link__dropdown {
    padding: 0;
    box-shadow: 1px 0 8px #a7a8a9; }
    .account-link__dropdown .dropdown-item a {
      white-space: normal;
      word-break: break-word;
      font-size: 12px;
      padding: 10px 12px; }
  .account-link.show .account-link__toggle {
    background-color: #a7a8a9;
    color: #000; }
  .account-link .fas {
    margin-left: 5px; }

.menu-pc .account-link:first-child {
  margin-left: 0;
}
.header-top-sp .account-link {
  margin-left: 10px;
}

.header-top-sp .account-link:first-child {
  margin-left: 20px;
}
.menu-pc {
  margin: 0 auto; }
  .menu-pc .logo-wrap {
    padding: 10px 0; }
  .menu-pc .link-header__customer {
    position: relative; }
  .menu-pc .link-header-login {
    padding: 14px 5px; }

@media (max-width: 992px) {
  .mobile-head-sp {
    position: fixed;
    background-color: #000;
    width: 100%;
    padding: 60px 0 20px;
    transition: .5s ease-in-out;
    top: -600px;
    z-index: 10; }
  .nav-toggle {
    position: fixed;
    z-index: 999;
    top: 16px;
    left: 20px;
    cursor: pointer;
    width: 30px;
    height: 25px; }
    .nav-toggle-box {
      position: relative; }
    .nav-toggle span {
      display: block;
      position: absolute;
      height: 3px;
      width: 30px;
      background: #000;
      left: 0;
      transition: .35s ease-in-out; }
      .nav-toggle span:nth-child(1) {
        top: 0; }
      .nav-toggle span:nth-child(2) {
        top: 10px; }
      .nav-toggle span:nth-child(3) {
        top: 20px; }
  .open .mobile-head-sp {
    transform: translateY(600px); }
  .open .nav-toggle span:nth-child(1) {
    top: 11px;
    transform: rotate(315deg);
    background: #fff; }
  .open .nav-toggle span:nth-child(2) {
    width: 0;
    left: 50%;
    background: #fff; }
  .open .nav-toggle span:nth-child(3) {
    top: 11px;
    transform: rotate(-315deg);
    background: #fff; }
  .header-menu-sp ul {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    background: url("../images/bg_pc_navbody.png") repeat-x bottom left;
    margin-bottom: 0; }
  .header-menu-sp li {
    padding-bottom: 12px;
    padding-top: 8px; }
  .header-menu-sp a {
    color: #009ace;
    padding: 2px 0;
    display: block;
    font-size: 17px;
    letter-spacing: 2px;
    position: relative;
    text-transform: uppercase; } }
  @media (max-width: 992px) and (max-width: 576px) {
    .header-menu-sp a {
      font-size: 12px; } }

@media (max-width: 992px) {
    .header-menu-sp a:after {
      content: '';
      width: 100%;
      height: 2px;
      position: absolute;
      bottom: 0;
      left: 0;
      visibility: hidden;
      opacity: 0;
      transition: all .3s ease;
      background-color: #009ace; }
    .header-menu-sp a:hover {
      text-decoration: none;
      color: #0076a5; }
      .header-menu-sp a:hover:after {
        visibility: visible;
        opacity: 1; }
  .nav-menu-sp {
    padding: 0;
    margin: 0 30px;
    list-style: none;
    position: static;
    right: 0;
    bottom: 0;
    font-size: 12px; }
    .nav-menu-sp > ul {
      padding: 0; }
    .nav-menu-sp li {
      list-style: none;
      float: none;
      position: static; }
    .nav-menu-sp .item-second a {
      color: #fff;
      font-size: 13px;
      padding: 10px 0;
      display: block;
      text-transform: uppercase; }
    .nav-menu-sp .item-main a {
      color: #009ace;
      font-size: 16px;
      padding: 10px 0;
      display: block;
      text-transform: uppercase; }
  .logo-wrap-sp {
    position: absolute;
    top: 10px;
    left: 0;
    right: 0;
    margin: auto;
    width: 165px; }
  .header-top-sp {
    position: relative;
    background: url("../images/bg_pc_navbody.png") repeat-x bottom left; }
    .header-top-sp .logo-wrap {
      width: 165px;
      margin: 0 auto;
      text-align: center;
      padding: 10px 0; }
    .header-top-sp .link-header {
      position: absolute;
      right: 20px;
      top: 0; } }

.menu-dropdown__content {
  display: none;
  padding-left: 10px;
  padding-right: 10px; }
  .menu-dropdown__content .item-main .menu-item {
    font-size: 13px;
    color: #fff;
    cursor: pointer; }

.menu-dropdown__icon {
  float: right; }

.menu-dropdown__item {
  cursor: pointer; }
  .menu-dropdown__item.menu-open .fa-caret-down {
    transform: rotate(180deg); }

.header-phase2 {
  box-shadow: none;
  border-bottom: 1px solid #a7a8a9; }
  .header-phase2 .header-wrap {
    align-items: center; }
    .header-phase2 .header-wrap__title-login {
      text-transform: uppercase;
      font-size: 13px;
      color: #000; }
      .header-phase2 .header-wrap__title-login:hover {
        opacity: .6; }

.srm3 .header-top-sp {
  background: none; }

.sheader {
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 999;
  top: 0; }
  .sheader-links {
    display: flex;
    align-items: center;
  }
  .sheader-link {
    color: #000;
    display: inline-block;
    margin: 0 24px;
    font-size: 13px;
    text-transform: uppercase; }
    .sheader-link .fa-bookmark {
      color: #A7A8A9;
      font-size: 20px;
    }
    @media (max-width: 992px) {
      .nav-master-admin .sheader-link {
        font-size: 8px; }
      .sheader-link .fa-bookmark {
        font-size: 20px;
      } }
    .sheader-link:hover, .sheader-link.current, .sheader-link span:hover, .sheader-link.current span{
      color: #009ace; }
    .sheader-link:first-child {
      margin-left: 0; }
    .sheader-link:last-child {
      margin-right: 0; }
    .sheader-link-block {
      text-transform: none;
    }

.sheader-pc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px; 
  padding: 0px 16px;
}
  @media (max-width: 992px) {
    .sheader-pc__left {
      display: none; } }
  .sheader-pc__right {
    display: flex;
    align-items: center; }
    @media (max-width: 992px) {
      .sheader-pc__right {
        flex: 1;
        justify-content: flex-end; } }
  @media (max-width: 1140px) {
    .sheader-pc {
      padding: 0 16px;
      background-color: #fff; } }

.sheader-info {
  display: flex;
  align-items: center;
  margin-left: 24px; }
  @media (max-width: 992px) {
    .sheader-info {
      margin-left: 24px !important;
      min-width: 35px;
    }
      .sheader-dropdown.bdropdown {
        padding-left: 4px; }
  }
  @media (max-width: 600px) {
    .sheader-info {
      margin-left: 24px !important; }
    .nav-master-admin .sheader-link {
      margin: 0 6px;
      font-size: 7px; }
  }

.sheader-account-link {
  color: #a7a8a9; }
  .sheader-account-link:hover {
    color: #a7a8a9; }

.sheader-dropdown {
  padding-left: 8px; }

.sfooter {
  background-color: #000;
  color: #fff;
  height: 40px;
  line-height: 40px;
  text-align: center; }

.banner {
  box-shadow: 0 2px 2px #a7a8a9;
  position: relative;
  margin-bottom: 35px; }
  .banner__img {
    display: block;
    width: 100vw;
    height: 100vh;
  }
    .banner__img a {
      display: block; }
  .banner img {
    width: 100%; }

.action {
  position: absolute;
  right: 10px;
  max-width: 90%;
  top: 0;
  bottom: 0; }
  .action__detail {
    position: absolute;
    bottom: 5px;
    right: 0;
    background-color: #1a97b7; }
    .action__detail:hover {
      background-color: #14728a; }
  .action__order {
    background-color: #e50914; }
    .action__order:hover {
      background-color: #b40710; }
  .action button {
    border: 0;
    color: #fff;
    border-radius: 20px;
    font-size: 10px;
    min-width: 70px;
    padding: 2px 10px; }
    .action button:focus, .action button:hover {
      outline: 0;
      color: #fff; }
    @media (min-width: 992px) {
      .action button {
        padding: 5px 20px;
        font-size: 13px;
        bottom: 10px;
        min-width: 92px; } }
  @media (min-width: 992px) {
    .action {
      padding-top: 10px; } }

.button {
  display: inline-block;
  text-align: center; }
  .button--background {
    background-color: rgba(0, 157, 196, 0.73);
    color: #fff;
    height: 44px;
    line-height: 44px;
    display: inline-block;
    padding: 0 15px;
    min-width: 155px; }
    .button--background:hover {
      color: #fff; }
    .button--background-gray {
      background-color: #53565a; }
  .button--border {
    height: 44px;
    line-height: 42px;
    min-width: 155px; }
    .button--border-primary {
      border: 1px solid #009ace;
      color: #009ace; }
      .button--border-primary:hover {
        color: #009ace; }
    .button--border-gray {
      border: 1px solid #707070;
      color: #707070; }
      .button--border-gray:hover {
        color: #707070; }
    .button--border.button-disabled {
      pointer-events: none; }
  .button--round {
    border-radius: 23px;
    overflow: hidden; }
  .button--full {
    display: block;
    width: 100%; }
  .button--icon:before {
    content: '';
    display: inline-block; }
  .button--icon-heart-o:before {
    width: 24px;
    height: 22px;
    margin-bottom: -7px;
    margin-right: 12px;
    background-image: url("../images/icon-heart-border.svg"); }
  .button--icon-heart:before {
    width: 24px;
    height: 24px;
    margin-bottom: -8px;
    margin-right: 12px;
    background-image: url("../images/icon-heart-fill.svg"); }
  .button--icon-add:before {
    width: 20px;
    height: 20px;
    background-image: url("../images/icon-add.svg"); }
  .button--icon-settings:before {
    width: 20px;
    height: 20px;
    background-image: url("../images/icon-settings.svg"); }
  .button--icon-dot:before {
    content: '...';
    width: 24px;
    height: 24px;
    background-color: #a7a8a9; }
  .button--text-primary {
    color: #009ace; }
    .button--text-primary:hover {
      color: #0076a5; }
  .button--text-gray {
    color: #707070; }
    .button--text-gray:hover {
      color: #707070; }
  .button--text-red {
    color: #e6002d; }
    .button--text-red:hover {
      color: #e6002d; }
  .button--text.button--disabled {
    color: #a7a8a9; }
  .button--disabled {
    pointer-events: none; }
  .button--gradient {
    background-image: linear-gradient(45deg, rgba(0, 161, 221, 0.73), #009ace);
    color: #fff;
    height: 44px;
    line-height: 44px;
    display: inline-block;
    padding: 0 15px;
    min-width: 140px; }
    .button--gradient:hover {
      color: #fff;
      cursor: pointer; }
    .button--gradient-blue {
      background-image: linear-gradient(60deg, rgba(148, 239, 239, 0.73), #009ace); }
    .button--gradient-gray-dark {
      background-color: #000;
      background-image: none;
      font-size: 16px;
      color: #fff;
      text-transform: uppercase; }
      .button--gradient-gray-dark:hover {
        opacity: .8;
        transition: .3s; }
    .button--gradient.disabled {
      pointer-events: none;
      background-image: linear-gradient(60deg, #f2f2f2, #a7a8a9); }
  .button--small {
    height: 30px;
    line-height: 30px; }
  .button--tiny {
    height: 24px;
    line-height: 24px; }

/* stylelint-disable */
.btn {
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 13px;
  /*font-weight: 400;*/
  padding: 8.5px 15px;
  min-width: 80px; }
  .btn .icon {
    margin-right: 6px; }
  .btn-text ~ .icon {
    margin-right: 0;
    margin-left: 6px; }
  .btn--sm {
    padding: 8px 24px; }
  .btn--lg {
    padding: 12px 24px; }
  .btn--blue {
    background-color: #009ace;
    color: #fff; }
    .btn--blue .icon {
      color: #fff; }
    .btn--blue:hover, .btn--blue:active {
      background-color: #0076a5;
      color: #fff;
      outline: none !important; }
    .btn--blue:focus {
      color: #fff;
      outline: none !important; }
    .btn--blue:disabled, .btn--blue.btn--disabled {
      cursor: not-allowed;
      pointer-events: none;
      color: #a7a8a9;
      background-color: #f0f0f0;
      border-color: #f0f0f0;
      opacity: 1; }
  .btn--blue-outline {
    background-color: #fff;
    border-color: #009ace;
    color: #009ace; }
    .btn--blue-outline .icon {
      color: #009ace; }
    .btn--blue-outline:hover, .btn--blue-outline:active {
      border-color: #0076a5;
      color: #0076a5;
      outline: none !important; }
      .btn--blue-outline:hover .icon, .btn--blue-outline:active .icon {
        color: #0076a5; }
    .btn--blue-outline:focus {
      color: #009ace;
      outline: none !important; }
    .btn--blue-outline:disabled, .btn--blue-outline.btn--disabled {
      cursor: not-allowed;
      pointer-events: none;
      color: #a7a8a9;
      border-color: #f0f0f0;
      opacity: 1; }
  .btn--outline {
    background-color: #fff; }
  .btn:disabled, .btn--disabled {
    cursor: not-allowed;
    pointer-events: none; }

  .custom-switch .form-check-label.btn:disabled,
  .custom-switch .form-check-label.btn--disabled {
    color: #a7a8a9;
  }

  .btn:focus, .btn:active {
    box-shadow: none;
    outline: 0; }
  .btn--text-default {
    background-color: transparent;
    color: #53565a;
    padding: 0;
    display: flex;
    align-items: center;
    min-width: auto; }
    .btn--text-default .icon {
      margin-right: 10px; }
    .btn--text-default .btn-text {
      line-height: 1; }
    .btn--text-default:hover, .btn--text-default:focus {
      color: #53565a; }
  .btn--text-blue {
    background-color: transparent;
    color: #009ace;
    padding: 0;
    display: flex;
    align-items: center;
    min-width: auto; }
    .btn--text-blue .icon {
      margin-right: 10px; }
    .btn--text-blue .btn-text {
      line-height: 1; }
    .btn--text-blue:hover, .btn--text-blue:focus {
      color: #009ace; }
  .btn--text-gray {
    background-color: transparent;
    color: #a7a8a9;
    padding: 0;
    display: flex;
    align-items: center;
    min-width: auto; }
    .btn--text-gray .icon {
      margin-right: 10px; }
    .btn--text-gray .btn-text {
      line-height: 1; }
    .btn--text-gray:hover, .btn--text-gray:focus {
      color: #a7a8a9; }

/* stylelint-enable */
.checkbox {
  margin: 0;
  position: relative; }
  .checkbox input[type='checkbox'] {
    width: 0;
    position: absolute;
    margin: 0;
    left: 0;
    top: 5px;
    border: 0;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none; }
    .checkbox input[type='checkbox']:focus {
      outline: none; }
  .checkbox input[type='checkbox']:before {
    content: '';
    display: block;
    position: absolute;
    width: 18px;
    height: 18px;
    top: 0;
    left: 0;
    background-color: #53565a; }
  .checkbox input[type='checkbox']:checked:after {
    content: '';
    display: block;
    width: 8px;
    height: 13px;
    border: solid #fff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    position: absolute;
    top: 0;
    left: 5px; }
  .checkbox label {
    font-size: 13px;
    color: #707070;
    margin-top: 5px;
    font-weight: 600;
    padding-left: 25px; }

.button-switch {
  height: 20px;
  position: relative;
  width: 40px; }
  .button-switch:hover {
    cursor: pointer; }
  .button-switch .switch {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none; }
    .button-switch .switch:before, .button-switch .switch:after {
      content: '';
      position: absolute; }
    .button-switch .switch:before {
      border-radius: 20px;
      background: #53565a;
      height: 20px;
      left: 0;
      top: 0;
      width: 40px;
      transition: all .3s; }
    .button-switch .switch:after {
      border-radius: 50%;
      background: #fff;
      height: 14px;
      transform: translate(0, 0);
      transition: all .3s;
      width: 14px;
      top: 3px;
      left: 3px; }
    .button-switch .switch:checked:before {
      background-color: #009ace; }
    .button-switch .switch:checked:after {
      transform: translate(20px, 0); }
    .button-switch .switch:checked ~ .lbl-off {
      opacity: 0; }
    .button-switch .switch:checked ~ .lbl-on {
      opacity: 1; }

.custom-checkbox {
  display: flex;
  flex-direction: column;
  padding-left: 0; }
  .custom-checkbox.disabled .form-check-label {
    pointer-events: none;
    cursor: not-allowed;
  }
  .custom-checkbox.disabled .form-check-label:after {
    border: 1px solid #F0F0F0;
  }
  .custom-checkbox .form-check-label {
    color: #53565a;
    cursor: pointer;
    padding-left: 30px;
    position: relative;
    height: 20px;
    line-height: 20px; }
    .custom-checkbox .form-check-label:before {
      content: '\e902';
      color: #fff;
      font-family: "soremoicons";
      transition: all .15s ease;
      position: absolute;
      top: 0;
      left: 2.5px;
      right: auto;
      bottom: auto;
      visibility: hidden;
      font-size: 12px;
      z-index: 2; }
    .custom-checkbox .form-check-label:after {
      content: '';
      border: 1px solid #a7a8a9;
      transform: translateY(-50%);
      transition: all .15s ease;
      z-index: 1;
      height: 18px;
      width: 18px;
      border-radius: 3px;
      position: absolute;
      top: 50%;
      left: 0;
      right: auto;
      bottom: auto; }
  .custom-checkbox .form-check-input {
    opacity: 0;
    margin: 0;
    position: absolute;
    z-index: -1; }
    .custom-checkbox .form-check-input:checked ~ .form-check-label:before {
      visibility: visible; }
    .custom-checkbox .form-check-input:checked ~ .form-check-label:after {
      border-color: #009ace;
      background-color: #009ace; }
    .custom-checkbox .form-check-input[disabled] ~ .form-check-label {
      color: #a7a8a9; }
      .custom-checkbox .form-check-input[disabled] ~ .form-check-label:before {
        display: none; }
      .custom-checkbox .form-check-input[disabled] ~ .form-check-label:after {
        border-color: #d3d3d3; }
    .custom-checkbox .form-check-input:checked[disabled] ~ .form-check-label {
      color: #a7a8a9; }
      .custom-checkbox .form-check-input:checked[disabled] ~ .form-check-label:before {
        display: block; }
      .custom-checkbox .form-check-input:checked[disabled] ~ .form-check-label:after {
        border-color: #d3d3d3;
        background: #d3d3d3; }

.input-box {
  transition: all .3s ease; }
  .input-box:hover, .input-box:focus, .input-box:active {
    transition: all .3s ease;
    outline: none;
    background-color: #363636;
    border-radius: 4px; }

.form-textarea .form-control {
  min-height: 90px; }

.sumo-select .SumoSelect {
  display: block;
  width: 100%; }
  .sumo-select .SumoSelect > .CaptionCont {
    border: 1px solid #53565a;
    border-radius: 0; }
  .sumo-select .SumoSelect.open > .CaptionCont, .sumo-select .SumoSelect:focus > .CaptionCont, .sumo-select .SumoSelect:hover > .CaptionCont {
    box-shadow: none; }

.form-group {
  margin-bottom: 25px; }
  /* .form-group label {
    font-size: 13px;
    font-weight: 400;
    color: #009ace;
    margin-bottom: 3px; } */
  .form-group .form-control {
    height: 30px;
    border: 1px solid #53565a;
    border-radius: 0; }
    .form-group .form-control:focus {
      box-shadow: none; }
  .form-group .input-group.date .form-control {
    border-right: 0; }
  .form-group .input-group.date .input-group-addon {
    border: 1px solid #53565a;
    border-left: 0;
    background-color: #fff;
    cursor: pointer;
    border-radius: 0; }
  .form-group .form-description {
    margin-top: 8px; }

.form-search-input {
  position: relative; }
  .form-search-input .search-input {
    padding-left: 40px; }
  .form-search-input .icon-font {
    position: absolute;
    top: 6px;
    left: 10px;
    font-size: 16px;
    color: #53565a; }

.footer {
  background-color: #fff;
  border-top: solid 1px #d0d1d4; }
  @media (min-width: 992px) {
    .footer {
      margin-top: 80px;
      margin-bottom: 50px; } }
  @media (max-width: 992px) {
    .footer {
      margin-top: 20px;
      margin-bottom: 20px; } }
  .footer__top {
    text-align: center;
    width: 100%;
    padding: 45px 0;
    border-top: solid 1px #d0d1d4;
    border-bottom: solid 1px #d0d1d4; }
    @media (min-width: 992px) {
      .footer__top {
        padding: 45px 0; } }
    @media (max-width: 992px) {
      .footer__top {
        padding: 25px 0; } }
    .footer__top-logo {
      margin: 0 auto; }
      @media (min-width: 992px) {
        .footer__top-logo {
          max-width: 200px; } }
      @media (max-width: 992px) {
        .footer__top-logo {
          max-width: 90%; }
          .footer__top-logo img {
            max-width: 180px;
            width: auto; } }
  .footer__middle-bgimage {
    height: 0;
    background: url("../images/footer_bgimage.gif") top center no-repeat;
    background-size: cover; }
    @media (min-width: 992px) {
      .footer__middle-bgimage {
        margin-top: 45px;
        padding-top: 5.138%; } }
    @media (max-width: 992px) {
      .footer__middle-bgimage {
        margin-top: 25px;
        padding-top: 10%; } }
  .footer__bottom {
    margin-top: 10px;
    text-align: center; }
    .footer__bottom ul {
      display: inline-block; }
    .footer__bottom li {
      margin-right: 15px;
      display: inline-block; }
    .footer__bottom a {
      color: #666;
      text-decoration: none;
      font-size: 12px; }
      .footer__bottom a:hover {
        color: #009ace; }
    @media (min-width: 768px) {
      .footer__bottom-link {
        float: left; } }
    @media (min-width: 768px) {
      .footer__bottom-copyright {
        float: right; } }
    .footer__bottom-copyright p {
      color: #666;
      text-decoration: none;
      font-size: 12px; }

.footers {
  font-size: 13px;
  padding: 40px 0 30px; }
  .footers__container {
    display: flex; }
    @media screen and (max-width: 767px) {
      .footers__container {
        display: block; } }
  .footers__info {
    display: flex; }
    .footers__info-list {
      padding: 0;
      margin: 0;
      list-style: none; }
    .footers__info-column {
      padding-right: 60px; }
      @media screen and (max-width: 767px) {
        .footers__info-column {
          padding-right: 15px; } }
      .footers__info-column:last-child {
        padding-right: 0; }
    .footers__info-item {
      padding: 3px 0; }
    .footers__info-link {
      color: #53565a; }
      .footers__info-link:hover {
        color: #53565a; }
  .footers__copyright {
    margin-left: auto;
    color: #53565a; }
    @media screen and (max-width: 767px) {
      .footers__copyright {
        margin-top: 15px;
        text-align: center; } }

.progressbar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  bottom: 10px;
  left: 10px;
  width: 40%; }
  @media (min-width: 992px) {
    .progressbar {
      bottom: 20px;
      left: 20px; } }
  .progressbar__title {
    margin-left: 15px;
    color: #000;
    font-size: 12px;
    font-weight: 600; }
  .progressbar .progress {
    background-color: #fff;
    box-shadow: none;
    border: 1px solid #707070;
    border-radius: 20px;
    height: 8px;
    width: 100%;
    min-width: 188px;
    margin-bottom: 0;
    overflow: visible; }
    .progressbar .progress .progress-bar {
      position: relative;
      box-shadow: none;
      margin-top: -1px;
      height: 8px;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px; }
      .progressbar .progress .progress-bar::-webkit-progress-bar, .progressbar .progress .progress-bar::-webkit-progress-value, .progressbar .progress .progress-bar::-moz-progress-bar {
        border-top-right-radius: 20px;
        border-bottom-right-radius: 20px; }
    .progressbar .progress .bg-success {
      background-color: #e50914;
      z-index: 9;
      border-radius: 20px; }
    .progressbar .progress .bg-warning, .sprogress .progress-bar.bg-warning {
      background-color: #53565a; }
    .progressbar .progress .warning-custom:after {
      content: '';
      width: 2px;
      height: 100%;
      background-color: #53565a;
      position: absolute;
      top: 0;
      left: -2px; }

.carousel {
  margin-bottom: 50px; }
  .carousel .disable-click {
    pointer-events: none;
    background-color: #a7a8a9; }
  .carousel .label-new {
    position: absolute;
    width: 50px;
    height: 25px;
    line-height: 25px;
    color: #fff;
    background-color: #e6002d;
    z-index: 10;
    top: 10px;
    right: 0;
    font-size: 12px;
    text-transform: uppercase;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px; }

.carousel-indicators {
  top: -27px;
  left: 0;
  bottom: auto;
  width: auto;
  margin: 0; }
  .carousel-indicators li {
    height: 20px;
    width: 20px;
    background-color: #53565a;
    color: #fff;
    text-indent: 0;
    font-size: 10px;
    border: 0;
    line-height: 18px;
    margin: 0; }
    .carousel-indicators li:hover {
      background-color: #fff;
      border: 1px solid #009ace;
      color: #009ace; }
  .carousel-indicators .heart-active {
    background-color: #000; }
    .carousel-indicators .heart-active:hover {
      background-color: #000;
      color: #fff;
      border: 0; }
  .carousel-indicators .active {
    background-color: #009ace;
    height: 20px;
    width: 20px; }

.comment-input {
  margin-top: 15px; }
  .comment-input__main {
    display: flex;
    border: 1px solid #009dc4;
    border-radius: 10px;
    padding: 6px 0; }
  .comment-input__btn-pin {
    width: 20px;
    height: 20px;
    background-color: #707070;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-pin.svg");
    mask-image: url("../images/icon-pin.svg");
    -webkit-mask-size: cover;
    mask-size: cover; }
    .comment-input__btn-pin.active {
      background-color: #009dc4; }
    .comment-input__btn-pin:hover {
      background-color: #009dc4;
      cursor: pointer; }
  .comment-input__time {
    font-family: "A+mfCv-AXISラウンド 50 L StdN", "Noto Sans Japanese", "sans-serif";
    font-size: 10px;
    margin-top: 1px;
    display: none; }
    .comment-input__time.active {
      display: block; }
  .comment-input__file-upload {
    display: none !important;
    /* stylelint-disable-line */ }
  .comment-input__btn-upload {
    width: 24px;
    height: 24px;
    background-color: #707070;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-upload.svg");
    mask-image: url("../images/icon-upload.svg");
    -webkit-mask-size: cover;
    mask-size: cover; }
    .comment-input__btn-upload:hover {
      cursor: pointer;
      background-color: #009dc4; }
  .comment-input__btn-send {
    width: 24px;
    height: 24px;
    background-color: #d0d0d0;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-send.svg");
    mask-image: url("../images/icon-send.svg");
    -webkit-mask-size: cover;
    mask-size: cover;
    margin: 0 15px;
    pointer-events: none; }
    .comment-input__btn-send.active {
      background-color: #707070;
      pointer-events: auto; }
      .comment-input__btn-send.active:hover {
        background-color: #009dc4;
        cursor: pointer; }
  .comment-input__left {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 15px;
    min-width: 25px; }
  .comment-input__center {
    flex: 1;
    margin-right: 15px; }
  .comment-input__right {
    display: flex;
    align-items: center;
    margin-left: auto; }
  .comment-input__comment {
    display: flex;
    align-items: center; }
  .comment-input__textarea {
    border: none;
    padding: 0;
    width: 100%;
    resize: none; }
    .comment-input__textarea {
      scrollbar-face-color: #707070;
      scrollbar-track-color: transparent; }
    .comment-input__textarea::-webkit-scrollbar {
      width: 6px;
      height: 6px; }
    .comment-input__textarea::-webkit-scrollbar-thumb {
      background: #707070;
      border-radius: 4px; }
    .comment-input__textarea::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 4px; }
    .comment-input__textarea:-ms-input-placeholder {
      color: #d0d1d4;
      transform: translateY(10px); }
    .comment-input__textarea::placeholder {
      color: #d0d1d4;
      transform: translateY(10px); }
    .comment-input__textarea:focus {
      outline: none; }
      .comment-input__textarea:focus:-ms-input-placeholder {
        color: transparent; }
      .comment-input__textarea:focus::placeholder {
        color: transparent; }
    .comment-input__textarea--empty {
      height: 20px;
      margin-top: 8px; }
  .comment-input__uploaded {
    margin: 0 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center; }
  .comment-input__file-uploaded {
    display: flex;
    align-items: center;
    margin-right: 8px; }
  .comment-input__file-name {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    margin-top: 4px;
    padding: 0 5px;
    font-size: 10px;
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis; }
  .comment-input__file-delete {
    border: 1px solid #d9d9d9;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    text-align: center;
    line-height: 12px;
    font-size: 10px;
    margin-left: 3px;
    margin-top: 4px; }
    .comment-input__file-delete:hover {
      cursor: pointer;
      color: #009dc4; }

@font-face {
  font-family: "soremoicons";
  /* src: url("../fonts/soremoicons.ttf?fd41f2") format("truetype"), url("../fonts/soremoicons.woff?fd41f2") format("woff"); */
  src: url('../fonts/soremoicons.eot');
  src: url('../fonts/soremoicons.eot?#iefix') format('embedded-opentype'),url('../fonts/soremoicons.svg#icomoon') format('svg'), url('../fonts/soremoicons.woff') format('woff'), url('../fonts/soremoicons.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: block; }

[class^='icon--'],
[class*=' icon--'] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "soremoicons" !important;
  /* stylelint-disable-line */
  /* speak: never; */
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

/* stylelint-disable */
.icon--sicon-accept:before {
  content: ""; }

.icon--sicon-mark-done:before {
  content: ""; }

.icon--sicon-star:before {
  content: ""; }

.icon--sicon-smile:before {
  content: ""; }

.icon--sicon-calendar:before {
  content: ""; }

.icon--sicon-musical:before {
  content: ""; }

.icon--sicon-stick:before {
  content: ""; }

.icon--sicon-camera:before {
  content: ""; }

.icon--sicon-gps:before {
  content: ""; }

.icon--sicon-upload:before {
  content: ""; }

.icon--sicon-trash:before {
  content: ""; }

.icon--sicon-tick:before {
  content: ""; }

.icon--sicon-settings:before {
  content: ""; }

.icon--sicon-search:before {
  content: ""; }

.icon--sicon-pencil:before {
  content: ""; }

.icon--sicon-minscreen:before {
  content: ""; }

.icon--sicon-fullscreen:before {
  content: ""; }

.icon--sicon-close:before {
  content: ""; }

.icon--sicon-add-circle-o:before {
  content: ""; }

.icon--sicon-user:before {
  content: ""; }

.icon--sicon-upload-folder:before {
  content: ""; }

.icon--sicon-update:before {
  content: ""; }

.icon--sicon-unlike:before {
  content: ""; }

.icon--sicon-share:before {
  content: ""; }

.icon--sicon-prev:before {
  content: ""; }

.icon--sicon-prev-double:before {
  content: ""; }

.icon--sicon-plus:before {
  content: ""; }

.icon--sicon-play:before {
  content: ""; }

.icon--sicon-pause:before {
  content: ""; }

.icon--sicon-next:before {
  content: ""; }

.icon--sicon-next-double:before {
  content: ""; }

.icon--sicon-minus:before {
  content: ""; }

.icon--sicon-menu:before {
  content: ""; }

.icon--sicon-like:before {
  content: ""; }

.icon--sicon-inbox:before {
  content: ""; }

.icon--sicon-heart:before {
  content: ""; }

.icon--sicon-heart-o:before {
  content: ""; }

.icon--sicon-eye-open:before {
  content: ""; }

.icon--sicon-eye-close:before {
  content: ""; }

.icon--sicon-export:before {
  content: ""; }

.icon--sicon-dropup:before {
  content: ""; }

.icon--sicon-dropdown:before {
  content: ""; }

.icon--sicon-download:before {
  content: ""; }

.loading.icon--sicon-download:before {
  content: "__";
  background-image: url(../images/icon-loading-outline-g.svg);
  background-size: contain;
  background-repeat: no-repeat;
  color: transparent;
  cursor: default;
}

.done.icon--sicon-download:before {
  content: "__";
  background-image: url(../images/icon_done.svg);
  background-size: contain;
  background-repeat: no-repeat;
  color: transparent;
  cursor: default;
}

.icon--sicon-down:before {
  content: ""; }

.icon--sicon-dashboard:before {
  content: ""; }

.icon--sicon-contract:before {
  content: ""; }

.icon--sicon-clock:before {
  content: ""; }

.icon--sicon-clip:before {
  content: ""; }

.icon--sicon-change-direction:before {
  content: ""; }

.icon--sicon-backup:before {
  content: ""; }

.icon--sicon-address:before {
  content: ""; }

.icon--sicon-add-cirlce:before {
  content: ""; }

.icon--sicon-pin:before {
  content: ""; }

.icon--sicon-reply:before {
  content: ""; }

.icon--sicon-storage:before {
  content: ""; }

.icon--sicon-filter:before {
  content: ""; }

.icon--sicon-asc:before {
  content: ""; }

.icon--sicon-desc:before {
  content: ""; }

.icon--sicon-social-share:before {
  content: ""; }

/* stylelint-enable */
.icon-image {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
  position: relative; }
  .icon-image:before, .icon-image:after {
    content: '';
    opacity: 1;
    transition: all .2s;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0; }
  .icon-image:before {
    background-size: cover;
    opacity: 1; }
  .icon-image:after {
    opacity: 0; }
  /* .icon-image:hover:before {
    opacity: 0;
  } */
  /* .icon-image:hover:after {
    opacity: 1;
  } */
  .icon-image--person {
    width: 21.4px;
    height: 26.3px; }
    .icon-image--person:before {
      background-image: url("../images/icon-person.svg"); }
  .icon-image--setting {
    width: 25.6px;
    height: 25.6px; }
    .icon-image--setting:before {
      background-image: url("../images/icon-settings.svg"); }

.icon-image--heart-all {
    width: 32px;
    height: 32px;
}

.icon-image--heart-all:before {
    background-image: url("../images/sicon-heart-all.svg");
}

/* stylelint-disable */
/* search crator */
/* .sform-group__label {
   font-weight: 400;
  color: #000000;
  margin-bottom: 6px; }
  .sform-group__label-required {
    color: #e50914;
    margin-left: 4px; } */

.sform-group__error {
  display: flex;
  justify-content: space-between; }

.sform-group__input-group {
  position: relative; }
  .sform-group__input-group .sform-control {
    padding-right: 35px; }

.sform-group__append-before .sform-control {
  padding-left: 35px;
  padding-right: 16px; }

.sform-group__append-before .sform-group__append {
  left: 0;
  right: auto; }

.sform-group__append {
  padding: 0 11px;
  position: absolute;
  top: 0;
  right: 0;
  width: auto;
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0; }
  .sform-group__append i {
    color: #a7a8a9;
    transition: color .15s ease; }

.sform-group--error__msg {
  color: #2cc84d;
  margin: 3px 0 0;
  font-size: 10px;
  margin-right: 5px;
  flex: auto;
  word-break: break-word; }
  .sform-group--error__msg:empty {
    display: none; }

.sform-group--error .sform-control {
  border-color: #2cc84d;
  color: #2cc84d; }
  .sform-group--error .sform-control__divider:after {
    background: #2cc84d; }
  .sform-group--error .sform-control ~ .sform-group__append i,
  .sform-group--error .sform-control:focus ~ .sform-group__append i {
    color: #2cc84d; }
  .sform-group--error .sform-control:focus {
    border-color: #2cc84d; }

.sform-group--success__msg {
  color: #009ace;
  margin: 3px 0 0;
  font-size: 10px;
  margin-right: 5px;
  flex: auto;
  word-break: break-word; }
  .sform-group--success__msg:empty {
    display: none; }

.sform-group--success .sform-control {
  border-color: #009ace;
  color: #009ace; }
  .sform-group--success .sform-control__divider:after {
    background: #009ace; }
  .sform-group--success .sform-control ~ .sform-group__append i,
  .sform-group--success .sform-control:focus ~ .sform-group__append i {
    color: #009ace; }
  .sform-group--success .sform-control:focus {
    border-color: #009ace; }

.sform-group--notice__msg {
  color: #a7a8a9;
  margin: 3px 0 0;
  font-size: 10px;
  margin-right: 5px;
  flex: auto;
  word-break: break-word; }
  .sform-group--notice__msg:empty {
    display: none; }

.sform-group--link .sform-control {
  color: #2994f6; }

.sform-group .sform-group--error__msg {
  width: 160px;
  -webkit-margin-end: 0;
          margin-inline-end: 0; }

.sform-group--mini .sform-control--input {
  width: 100px; }

.sform-group--large .sform-group__label {
  margin-bottom: 2px; }

.sform-control--input {
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  background-color: #fff;
  padding: 12px 8px;
  /* font-size: 13px;
  color: #000;
  padding: 0 16px; */
  height: auto;
  line-height: 200%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none; }
  .sform-control--input:focus {
    box-shadow: none;
    border-color: #009ace;
    outline: none; }
  .sform-control--input:-ms-input-placeholder {
    color: #d3d3d3; }
  .sform-control--input::placeholder {
    color: #d3d3d3; }
  .sform-control--input.js-datepicker {
    padding-right: 34px; }
  .sform-control--input:disabled, .sform-control--input:-moz-read-only {
    background-color: #f0f0f0;
    border-color: #f0f0f0;
    color: #d3d3d3; }
  .sform-control--input:disabled, .sform-control--input:read-only {
    background-color: #f0f0f0;
    border-color: #f0f0f0;
    color: #d3d3d3; }

.sform-control--lg {
  line-height: 48px; }
  .sform-control--lg ~ .sform-control__divider {
    display: none; }
  .sform-control--lg:focus ~ .sform-control__divider {
    display: none; }

.sform-control--gray {
  border-color: #d3d3d3; }

.sform-control--outline {
  border-color: #d3d3d3; }

.sform-control--full {
  width: 100%;}

.modal-sform-control--full {
  margin-bottom:15px;}

.sform-control[type='search'] {
  padding-right: 35px; }

.search-delete {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #a7a8a9;
  display: none; }
  .search-delete:hover {
    color: #009ace;
    cursor: pointer; }

/*  form input */
.sform-row--2-columns {
  display: flex; }
  @media screen and (max-width: 767px) {
    .sform-row--2-columns {
      display: block; } }
  .sform-row--2-columns .sform-group {
    flex: 0 0 50%; }
    @media screen and (max-width: 767px) {
      .sform-row--2-columns .sform-group {
        flex: 0 0 100%;
        margin-bottom: 16px; } }
    .sform-row--2-columns .sform-group:first-child {
      padding-right: 12px; }
      @media screen and (max-width: 767px) {
        .sform-row--2-columns .sform-group:first-child {
          padding-right: 0; } }
    .sform-row--2-columns .sform-group:last-child {
      padding-left: 12px; }
      @media screen and (max-width: 767px) {
        .sform-row--2-columns .sform-group:last-child {
          padding-left: 0;
          margin-bottom: 0; } }

.sform-row--3-columns {
  display: flex; }
  @media screen and (max-width: 767px) {
    .sform-row--3-columns {
      display: block; } }
  .sform-row--3-columns .sform-group {
    flex: 0 0 33.333333%;
    padding: 0 12px; }
    @media screen and (max-width: 767px) {
      .sform-row--3-columns .sform-group {
        flex: 0 0 100%;
        margin-bottom: 16px; } }
    .sform-row--3-columns .sform-group:first-child {
      padding-left: 0; }
      @media screen and (max-width: 767px) {
        .sform-row--3-columns .sform-group:first-child {
          padding-right: 0; } }
    .sform-row--3-columns .sform-group:last-child {
      padding-right: 0; }
      @media screen and (max-width: 767px) {
        .sform-row--3-columns .sform-group:last-child {
          padding-left: 0;
          margin-bottom: 0; } }

/* stylelint-enable */
.custom-radio {
  padding-left: 0; }
  .custom-radio .form-check-label {
    position: relative;
    padding-left: 32px;
    cursor: pointer; }
    .custom-radio .form-check-label:before {
      content: '';
      background-color: #009ace;
      opacity: 0;
      transform: translateY(-50%) scale(0.1);
      transition: all .15s ease;
      z-index: 2;
      border-radius: 50%;
      position: absolute;
      top: calc(50% - 1px);
      right: auto;
      bottom: auto;
      left: 3px;
      width: 12px;
      height: 12px; }
    .custom-radio .form-check-label:after {
      content: '';
      background-color: transparent;
      border: 1px solid #53565a;
      transform: translateY(-50%);
      transition: all .15s ease;
      z-index: 1;
      border-radius: 50%;
      position: absolute;
      top: calc(50% - 1px);
      right: auto;
      bottom: auto;
      left: 0;
      width: 18px;
      height: 18px; }
    .custom-radio .form-check-label span {
      display: block; }
  .custom-radio .form-check-input {
    opacity: 0;
    margin: 0;
    position: absolute;
    z-index: -1; }
    .custom-radio .form-check-input:checked ~ .form-check-label:before {
      opacity: 1;
      transform: translateY(-50%) scale(1); }
    .custom-radio .form-check-input:checked ~ .form-check-label:after {
      border-color: #009ace; }
    .custom-radio .form-check-input[disabled] ~ .form-check-label {
      color: #a7a8a9; }
      .custom-radio .form-check-input[disabled] ~ .form-check-label:before {
        display: none; }
      .custom-radio .form-check-input[disabled] ~ .form-check-label:after {
        border-color: #d3d3d3; }
    .custom-radio .form-check-input:checked[disabled] ~ .form-check-label:before {
      background: #d3d3d3;
      display: block; }

.custom-switch {
  padding-left: 0;}
.custom-switch .form-check-label {
  display: flex;
  align-items: center;
  margin: 0;}
  .custom-switch .form-check-label:hover {
    cursor: pointer; }

.custom-switch .form-check-group {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 20px;
  margin-bottom: 4px; }
  .custom-switch .form-check-group .switch-slider {
    position: absolute;
    cursor: pointer;
    top: 3px;
    left: 3px;
    right: 0;
    bottom: 0;
    background-color: #f0f0f0;
    border-radius: 14px;
    transition: .3s; }
    .custom-switch .form-check-group .switch-slider:before {
      position: absolute;
      content: '';
      height: 17px;
      width: 17px;
      background-color: #A7A8A9;
      border-radius: 50%;
      transition: .3s;
      box-shadow: 0 1px 3px 0 rgb(0 0 0 / 10%), 0 1px 2px 0 rgb(0 0 0 / 6%);
    }
  .custom-switch .form-check-group input {
    opacity: 0;
    width: 0;
    height: 0; }
    .custom-switch .form-check-group input:checked + .switch-slider {
      background-color: #e9f9ff; }
      .custom-switch .form-check-group input:checked + .switch-slider:before {
        background-color: #009ace;
        transform: translateX(13px); }
    .custom-switch .form-check-group input:focus + .switch-slider {
      box-shadow: none; }
    .custom-switch .form-check-group input[disabled] ~ .switch-slider {
      background-color: #f0f0f0; }
      .custom-switch .form-check-group input[disabled] ~ .switch-slider:before {
        background-color: #e7e7e7; }
    .custom-switch .form-check-group input:checked[disabled] ~ .switch-slider {
      background-color: #f0f0f0; }
      .custom-switch .form-check-group input:checked[disabled] ~ .switch-slider:before {
        background-color: #a7a8a9; }
      .custom-switch .form-check-group input:checked[disabled] ~ .switch-slider:after {
        border-color: #d3d3d3;
        background: #d3d3d3; }

.custom-switch .switch-label {
  font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif';
  font-size: 13px;
  font-weight: normal;
  margin-left: 2px;
  color: #a7a8a9;
}

.tag {
  display: inline-flex;
  align-items: center;
  background-color: #e9f9ff;
  border-radius: 4px;
  color: #009ace;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  padding: 0 8px; }
  .tag .icon {
    color: #fff; }
  .tag--blue {
    background-color: #009ace;
    color: #fff; }
  .tag--blue-light {
    background-color: #e9f9ff;
    color: #009ace; }
  .tag--black {
    background-color: #000;
    color: #fff; }
  .tag--gray {
    background-color: #f0f0f0;
    color: #53565a; }
  .tag--disabled {
    background-color: #f0f0f0;
    color: #a7a8a9;
    pointer-events: none; }
    .tag--disabled .icon {
      color: #a7a8a9; }
  .tag .tag-delete {
    margin-left: 10px; }
    .tag .tag-delete:hover {
      cursor: pointer; }
      .tag .tag-delete:hover .icon {
        color: #fff; }

.slabel {
  display: inline-block;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
  font-weight: 300;
  padding: 0 16px;
  border-radius: 13px; }
  .slabel--blue {
    background-color: #009ace;
    color: #fff; }
  .slabel--blue-light {
    background-color: #e9f9ff;
    color: #009ace; }
  .slabel--black {
    background-color: #000;
    color: #fff; }
  .slabel--gray {
    background-color: #f0f0f0;
    color: #53565a; }
  .slabel--small {
    font-size: 10px;
    height: 15px;
    line-height: 15px;
    padding: 0 8px;
    border-radius: 8px; }


/* stylelint-enable */
.avatar {
  display: inline-flex;
  align-items: center;
  overflow: hidden; }
  .avatar .avatar-icon {
    display: flex; }
  .avatar .avatar-icon img {
    border-radius: 50% }
  .avatar .avatar-image {
    padding-bottom: 100%;
    background-size: cover;
    background-position: center center; }
  .avatar--round {
    border-radius: 50%; }
  header .avatar--round {
    width: 32px;
    height: 32px;
    border-radius: 13px;
  }
  .avatar--14 .avatar-icon {
    font-size: 13px; }
  .avatar--14 .avatar-image {
    width: 14px; }
  .avatar--14.avatar--square {
    border-radius: 50%; }
  .avatar--24 .avatar-icon {
    font-size: 24px; }
  .avatar--24 .avatar-image {
    width: 24px; 
    border-radius: 50%; }
  .avatar--24.avatar--square {
    border-radius: 50%; }
  .avatar--32 .avatar-icon {
    font-size: 32px;
    border-radius: 13px;
    box-shadow: 2px 4px 10px 0px #E5E5E5;    
  }
  .avatar--32 .avatar-image {
    width: 32px; 
    border-radius: 13px;
    box-shadow: 2px 4px 10px 0px #E5E5E5;  
  }
  .avatar--32.avatar--square {
    border-radius: 8px; 
    box-shadow: 2px 4px 10px 0px #E5E5E5;  
  }
  .avatar--40 .avatar-icon {
    font-size: 40px;
    border-radius: 16px;
    box-shadow: 2px 4px 10px 0px #E5E5E5;  
  }
  .avatar--40 .avatar-image {
    width: 40px; 
    border-radius: 16px;
  }
  .avatar--40.avatar--square {
    border-radius: 12px; }


  .avatar--48 .avatar-icon {
    font-size: 48px; }
  .avatar--48 .avatar-image {
    width: 48px; 
    border-radius: 18px;
  }
  .avatar--48.avatar--square {
    border-radius: 12px; 
  }
  .avatar--64 .avatar-icon {
    font-size: 64px; }
  .avatar--64 .avatar-image {
    width: 64px; }
  .avatar--64.avatar--square {
    border-radius: 18px; }
  .avatar--80 .avatar-icon {
    font-size: 80px; }
  .avatar--80 .avatar-image {
    width: 80px; }
  .avatar--80.avatar--square {
    border-radius: 24px; }

/*.modal-open {
  padding-right: px; }
  .modal-open .header {
    padding-right: 15px; }*/

/*select dropdown*/

/*.modal-backdrop.fade + .modal-backdrop.fade {*/
/*  z-index: 1051; }*/

.modal-second {
  z-index: 1052; }

.select {
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  background-color: #fff;
  color: #000000;
  font-size: 13px;
  padding: 0 16px;
  height: 50px;
  line-height: 43px;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none; }
  .select:focus, .select:active {
    box-shadow: none;
    border-color: #009ace;
    border-radius: 4px;
    outline: 0; }
  .select--outline {
    border: 1px solid #f0f0f0; }
  .select--disabled {
    pointer-events: none;
    background-color: #f0f0f0;
    border-color: #f0f0f0; }
  .select-wrapper {
    position: relative; }

.dropdown-toggle:after {
  display: none; }

/* .sselect-wrapper {
  stylelint-disable
  stylelint-enable } */
  .sselect-wrapper .SumoSelect > .CaptionCont {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    padding: 8px 0px 8px 16px;
    box-shadow: none; }
    .sselect-wrapper .SumoSelect > .CaptionCont > label {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center; }
      .sselect-wrapper .SumoSelect > .CaptionCont > label:before {
        content: '';
        color: #a7a8a9;
        font-family: 'soremoicons' !important;
        /* stylelint-disable-line */ }
      .sselect-wrapper .SumoSelect > .CaptionCont > label > i {
        display: none; }
  .sselect-wrapper .SelectClass,
  .sselect-wrapper .SumoUnder {
    visibility: hidden; }
  .sselect-wrapper .SumoSelect > .CaptionCont > span.placeholder {
    font-style: normal;
    color: #d3d3d3; }
  .sselect-wrapper .SumoSelect > .CaptionCont > span {
    color: #000000;
    padding-right: 10px; }
  .sselect-wrapper .SumoSelect.open .search-txt {
    border: none;
    border-radius: 4px;
    padding: 12px 16px; }
  .sselect-wrapper .SumoSelect.open > .optWrapper {
    top: 53px;
    box-shadow: none;
    border: 1px solid #f0f0f0;
    border-radius: 4px; }
  .sselect-wrapper .SumoSelect .select-all > label,
  .sselect-wrapper .SumoSelect > .CaptionCont,
  .sselect-wrapper .SumoSelect > .optWrapper > .options li.opt label {
    font-size: 13px;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN','M PLUS 1p',sans-serif;
    font-weight: normal;
    margin-bottom: 0; }
  .sselect-wrapper .SumoSelect > .optWrapper > .options li.opt {
    font-size: 13px;
    color: #000;
    padding: 8px 0px 8px 16px;
    border-bottom: 1px solid #f0f0f0; }
    .sselect-wrapper .SumoSelect > .optWrapper > .options li.opt:last-child {
      border-bottom: none; }
    .sselect-wrapper .SumoSelect > .optWrapper > .options li.opt:hover {
      background-color: #fcfcfc; } 
    .sselect-wrapper .SumoSelect > .optWrapper > .options li.opt.selected {
      background-color: #f0f0f0; }
  .sselect-wrapper .SumoSelect .select-all > span,
  .sselect-wrapper .SumoSelect > .optWrapper.multiple > .options li.opt span {
    right: 6px;
    display: flex;
    justify-content: center; 
    align-items: center; }
  .sselect-wrapper .SumoSelect > .optWrapper > .options > li.opt:first-child {
    border-radius: 4px 4px 0 0; }
  .sselect-wrapper .SumoSelect > .optWrapper > .options > li.opt:last-child {
    border-radius: 0 0 4px 4px; }
  .sselect-wrapper .SumoSelect .select-all > span i,
  .sselect-wrapper .SumoSelect > .optWrapper.multiple > .options li.opt span i {
    display: none; }
  .sselect-wrapper .SumoSelect .select-all.partial > span:before,
  .sselect-wrapper .SumoSelect .select-all.selected > span:before,
  .sselect-wrapper .SumoSelect > .optWrapper.multiple > .options li.opt.selected span:before {
    content: '';
    color: #53565a;
    font-family: 'soremoicons' !important; }
  .sselect-wrapper.select-black .SumoSelect.open > .optWrapper {
    background-color: #000;
    border: none;
    color: #fff; }
  .sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt {
    padding: 12px 16px;
    border-bottom: 1px solid #53565a; }
    .sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt:last-child {
      border-bottom: none; }
    .sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt:hover, .sselect-wrapper.select-black .SumoSelect > .optWrapper > .options li.opt.selected {
      background-color: #232323; }
  .sselect-wrapper.select-black .SumoSelect .select-all.partial > span:before,
  .sselect-wrapper.select-black .SumoSelect .select-all.selected > span:before,
  .sselect-wrapper.select-black .SumoSelect > .optWrapper.multiple > .options li.opt.selected span:before {
    color: #fff; }

.sdropdown-toggle {
  display: block;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  background-color: #fff;
  color: #53565a;
  font-size: 13px;
  padding: 0 45px 0 16px;
  position: relative;
  height: 45px;
  line-height: 43px;
  width: 100%;
  text-align: left; }
  .sdropdown-toggle:hover, .sdropdown-toggle:focus {
    color: #53565a; }
  .sdropdown-toggle .icon {
    color: #a7a8a9;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%); }

.sdropdown-menu {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  box-shadow: none;
  width: 100%;
  padding: 0;
  margin-top: 7.5px; }

.sdropdown-item {
  color: #53565a;
  display: block;
  line-height: 43px;
  padding: 0 16px;
  font-size: 13px; }
  .sdropdown-item:hover {
    background-color: #f0f0f0;
    color: #53565a; }

.sdropdown--disabled {
  pointer-events: none; }
  .sdropdown--disabled .dropdown-toggle {
    background-color: #f0f0f0;
    color: #a7a8a9; }

.bdropdown {
  position: relative; }

.bdropdown-toggle .icon {
  color: #a7a8a9;
  font-size: 16px; }

.bdropdown-menu {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  box-shadow: none;
  width: clamp(256px - 32px, 10vw, 320px - 32px);
  padding: 0px 8px;
  margin-top: 8px;
  margin-left: -24px;
  /* stylelint-disable */
  right: 0 !important;
  left: auto !important;
  transform: none !important;
  top: 100% !important;
  /* stylelint-enable */ }

.bdropdown-item {
  font-size: 13px !important;
  color: #000;
  display: block;
  line-height: 150%;
  padding: 16px 0px 16px;
  font-size: 13px; }
  .bdropdown-item:hover {
    background-color: #fcfcfc;
    color: #000; }


.login-caution__avatar {
  width: 35px;
  height: 35px;
  margin-right: 10px;
  float: left; }
  .login-caution__avatar img {
    border-radius: 50%;
    max-width: 100%;
    overflow: hidden;
    height: 100%; }

.login-caution__content {
  min-height: 60px;
  width: calc(100% - 100px);
  background-color: #a7a8a9;
  border: 1px solid #a7a8a9;
  border-radius: 20px;
  padding: 10px;
  position: relative;
  font-size: 12px;
  margin-left: 45px; }

.login-caution__emoticon {
  width: calc(100% - 58px); }
  .login-caution__emoticon-images {
    width: 150px;
    float: right; }
  .login-caution__emoticon img {
    max-width: 100%; }

.login-caution__submit {
  overflow: hidden;
  width: 100%;
  padding: 30px; }
  .login-caution__submit .btn {
    width: 180px;
    border-radius: 30px;
    background-color: #009ace;
    color: #fff; }
    .login-caution__submit .btn:hover {
      background-color: #007399; }

.auth {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; }
  .auth__main {
    position: relative;
    width: 100%;
    /* max-width: 320px; */
    padding: 0; }
  .auth__form {
    background-color: #fff; }
    .auth__form-title {
      margin: 0 0 40px;
      padding-bottom: 5px;
      border-bottom: 2px solid #d0d1d4;
      font-weight: 600; }
    .auth__form-forgotpass, .auth__form-register, .auth__form-note {
      color: #a7a8a9; }
      .auth__form-forgotpass a, .auth__form-register a, .auth__form-note a {
        color: #a7a8a9;
        cursor: pointer;
        }
      .auth__form-forgotpass a, .auth__form-register a, .auth__form-note a:hover {
        color: #009ace; }
    .auth__form-button {
      width: 150px;
      background-color: #009acc;
      color: #fff;
      border: 0;
      padding: 7px 20px;
      border-radius: 20px; }
      .auth__form-button:hover {
        color: #fff;
        background-color: #007399; }
    .auth__form-login {
      color: #000;
    }
    .auth__form-btnlogin {
      color: #fff;
      background-color: #000;
      font-size: 16px;
      border-radius: 20px;
      padding: 5px 20px; }
      .auth__form-btnlogin:hover {
        color: #fff;
        opacity: .9; }
    .auth__form .btn-signup {
      width: 80%;
      font-size: 20px;
    }
  .auth .form-group {
    padding: 4px 0px;
    clear: both; }
    .auth .form-group .input-box {
      width: 100%;
      border-radius: 20px;
      border: 1px solid #f0f0f0;
      background-color: transparent;
      padding: 6px 15px; }
    .auth .form-group input:-ms-input-placeholder {
      color: #a7a8a9; }
    .auth .form-group input::placeholder {
      color: #a7a8a9; }
  .auth__content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%; }

.banner__img-pc {
  width: 100vw;
  height: 100vh;
  overflow: hidden; }

.error-messager {
  color: #2CC84D;
  margin-bottom: 0;
  font-size: 10px;
  margin-top: 5px;
  word-break: break-word;
  display: flex; }
  .error-messager:before {
    content: '＊'; }

.mg-25 {
  margin-bottom: 25px; }

.mg-40 {
  margin-bottom: 40px; }

.mg-80 {
  margin-bottom: 80px; }

.project-list__sort {
  width: 20px; }
  .project-list__sort .icon {
    color: #53565a; }
    .project-list__sort .icon:hover, .project-list__sort .icon:active {
      color: #009ace; }
  .project-list__sort .sort-up {
    display: none; }
  .project-list__sort.show-item .sort-up {
    display: block; }
  .project-list__sort.show-item .sort-down {
    display: none; }

.project-list__filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0; }
  .project-list__filter-left {
    display: flex;
    justify-content: flex-start;
    align-items: center; }
    .project-list__filter-left .icon-new:hover, .project-list__filter-left .icon-new.active {
      color: #fff;
      background-color: #e6002d; }
    .project-list__filter-left .icon-order {
      width: 37px;
      height: 37px; }
    .project-list__filter-left a {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #53565a;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      color: #fff;
      margin-right: 15px;
      font-size: 8px;
      text-transform: uppercase; }
      .project-list__filter-left a:hover {
        color: #fff;
        background-color: #009ace; }
    .project-list__filter-left .active {
      background-color: #009ace; }
    .project-list__filter-left .disable-click {
      pointer-events: none;
      background-color: #a7a8a9; }
  .project-list__filter-right {
    display: flex;
    justify-content: flex-start;
    align-items: center; }
  .project-list__filter .sort-menu {
    position: relative;
    width: 20px;
    margin-top: -3px; }
    .project-list__filter .sort-menu__toggle {
      color: #53565a; }
      .project-list__filter .sort-menu__toggle:hover, .project-list__filter .sort-menu__toggle:active {
        color: #009ace; }
    .project-list__filter .sort-menu__dropdown {
      left: auto;
      right: 0;
      padding: 10px;
      min-width: 100px;
      border-radius: 13px; }
  .project-list__filter .btn-radio {
    margin-right: 10px; }

.project-list__item {
  position: relative;
  margin-bottom: 5px;}
  .project-list__item img {
    width: 100%;
    min-height: 129px;
    object-fit: cover; }
  .project-list__item-add {
    background-color: #f8f8f8; }

.project-list__button {
  background-color: #e50914;
  color: #fff;
  border-radius: 20px;
  font-size: 16px;
  padding: 10px 20px;
  width: 200px;
  margin: 50px 0; }
  .project-list__button:hover {
    background-color: #b40710;
    color: #fff; }

.project-list .sp-img {
  display: none; }

@media (max-width: 992px) {
  .project-list .sp-img {
    display: block; }
  .project-list .pc-img {
    display: none; } }

.project-list__tag {
  color: #fff;
  text-align: center;
  margin-bottom: 5px;
  width: 164px;
  float: right; }
  @media (min-width: 992px) {
    .project-list__tag {
      font-size: 18px; } }
  .project-list__tag span {
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    display: block;
    padding: 5px 0; }
  .project-list__tag-new {
    background-color: #e6002d; }
  .project-list__tag-fix {
    background-color: #000; }

.project-list__info {
  position: absolute;
  right: 0;
  max-width: 90%;
  top: 0;
  bottom: 0;
  text-align: right;
  padding-top: 10px; }
  @media (min-width: 992px) {
    .project-list__info {
      text-align: left; } }

.sproject-banner-rating {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.sproject-banner-rating span.rating_value, .sproject-banner-rating span.number_vote {
  font-size: 11px;
  color: #a5a6a7;
  margin-left: 4px;
  /* margin-bottom: 1px; */
}

.sproject-banner-rating .stars {
    position: relative;
    display: inline-block;
    font-size: 11px;
    letter-spacing: 2px;
}

.sproject-banner-rating .stars span:before {
    content: '\e940';
    color: #707070;
    font-family: 'soremoicons';
}

.sproject-banner-rating .stars span.selected:before {
    content: '\e93f';
    color: #a7a8a9;
}

.project-list__star {
  letter-spacing: 1px; }
  .project-list__star .number-rate {
    clear: both;
    font-size: 30px;
    color: #fff; }
    @media (min-width: 992px) {
      .project-list__star .number-rate {
        font-size: 40px; } }
  .project-list__star .icon-rate {
    clear: both;
    width: 100%;
    float: right;
    color: #f0ea0c;
    font-size: 20px;
    margin-top: -10px; }
    @media (min-width: 992px) {
      .project-list__star .icon-rate {
        font-size: 25px;
        margin-bottom: 15px; } }

.project-list__time {
  color: #b6b5b6;
  letter-spacing: 1px;
  font-size: 16px;
  text-align: right; }
  @media (min-width: 992px) {
    .project-list__time {
      font-size: 13px; } }
  .project-list__time p {
    margin-bottom: 5px; }

.setting-edit {
  display: flex;
  align-items: center; }
  .setting-edit__item {
    color: #fff;
    font-size: 10px;
    margin: 0 3px; }
    .setting-edit__item:hover, .setting-edit__item:active, .setting-edit__item:focus {
      color: #fff;
      text-decoration: none; }
  .setting-edit__box {
    width: 0;
    transition: all .2s;
    transform: scale(0); }
    .setting-edit__box.open {
      transform: scale(1);
      transition: all .2s; }

.scenes__filter-top {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 25px; }
  .scenes__filter-top .icon.icon-new {
    font-size: 10px;
    text-transform: uppercase;
    width: 37px;
    height: 37px; }
    .scenes__filter-top .icon.icon-new:hover, .scenes__filter-top .icon.icon-new.active {
      background-color: #e6002d; }
  .scenes__filter-top .icon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    background-color: #53565a;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    color: #fff;
    margin-right: 15px; }
    .scenes__filter-top .icon:hover {
      color: #fff;
      background-color: #009ace; }
    .scenes__filter-top .icon.disable-click {
      pointer-events: none;
      background-color: #a7a8a9; }
  .scenes__filter-top .active {
    background-color: #009ace; }

.scenes .sort-menu {
  position: absolute;
  top: 4px;
  right: 60px; }
  .scenes .sort-menu__toggle {
    color: #53565a; }
    .scenes .sort-menu__toggle:hover {
      color: #009ace; }
  .scenes .sort-menu__dropdown {
    left: auto;
    right: -5px;
    padding: 10px;
    min-width: 100px;
    border-radius: 13px; }
    .scenes .sort-menu__dropdown-star {
      color: #f0ea0c; }
  .scenes .sort-menu .btn-radio {
    margin-right: 10px; }

.scenes__rate {
  position: absolute;
  top: 0;
  left: 2px; }
  .scenes__rate .stars {
    padding: 0; }
  .scenes__rate .star {
    float: left;
    font-size: 10px;
    color: #f0ea0c;
    letter-spacing: 1px;
    list-style-type: none; }

.scenes__edit-title {
  margin-left: 10px; }
  .scenes__edit-title .setting-edit__box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5px;
    background-color: #757575;
    border-radius: 20px;
    height: 18px;
    padding-left: 5px;
    padding-right: 5px; }
    @media (min-width: 768px) and (max-width: 1199px) {
      .scenes__edit-title .setting-edit__box {
        width: 40px;
        visibility: visible;
        opacity: 1;
        transition: all .2s;
        transform: scale(1); } }
    .scenes__edit-title .setting-edit__box.open {
      width: 40px; }
  .scenes__edit-title .setting-edit__links {
    line-height: 1;
    margin-top: -7px;
    cursor: pointer; }
    @media (min-width: 768px) and (max-width: 1199px) {
      .scenes__edit-title .setting-edit__links {
        display: none; } }

.scenes__edit-item {
  position: absolute;
  right: 2px;
  top: 84.2px;
  background-color: #757575;
  border-radius: 20px;
  height: 18px;
  float: left;
  visibility: hidden;
  opacity: 0;
  transition: all .2s; }
  @media (max-width: 1199px) {
    .scenes__edit-item {
      visibility: visible;
      opacity: 1; } }
  .scenes__edit-item .setting-edit__box {
    margin: 0;
    display: flex; }
    .scenes__edit-item .setting-edit__box.open {
      width: 32px; }
  .scenes__edit-item .setting-edit__links {
    cursor: pointer;
    font-size: 10px;
    color: #fff;
    margin: 0 3px; }

.scenes__main {
  padding-top: 0; }

.scenes__title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20px;
  padding: 5px 0;
  position: relative; }
  .scenes__title h3 {
    font-size: 20px;
    color: #000000;
    margin: 0;
    text-transform: none; }
  @media (min-width: 1199px) {
    .scenes__title .setting-edit__links {
      display: none; }
    .scenes__title:hover .setting-edit__box {
      transition: all .2s;
      visibility: visible;
      opacity: 1;
      width: 40px;
      transform: scale(1); } }

.scenes__filter {
  position: absolute;
  right: 0; }
  .scenes__filter .link-icon {
    color: #53565a;
    font-size: 18px;
    cursor: pointer; }
    .scenes__filter .link-icon:hover, .scenes__filter .link-icon:active {
      color: #009ace; }

.scenes__sort {
  position: absolute;
  top: 5px;
  right: 46px; }
  .scenes__sort-box {
    position: relative;
    height: 26px; }
  .scenes__sort-up {
    position: absolute;
    top: 2px; }
  .scenes__sort-down {
    position: absolute;
    bottom: 2px; }
  .scenes__sort a {
    font-size: 16px;
    color: #53565a; }
    .scenes__sort a:hover, .scenes__sort a:active {
      color: #009ace; }
  .scenes__sort .fas {
    line-height: .5;
    float: left; }

.scenes__list {
  display: flex;
  flex-wrap: wrap;
  margin-right: -4px; }

.scenes__item {
  margin-right: 4px;
  margin-bottom: 20px;
  position: relative; }
  .scenes__item .label-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 30px;
    height: 30px;
    z-index: 10;
    color: #fff;
    background-color: #e6002d;
    border-radius: 50%;
    top: -10px;
    right: 0;
    font-size: 8px;
    padding: 5px;
    text-transform: uppercase; }
    .scenes__item .label-icon.label-num {
      width: 20px;
      height: 20px; }
  .scenes__item .label-done {
    position: absolute;
    top: -17px;
    right: 0;
    z-index: 10;
    color: #e50914;
    font-size: 30px; }
  .scenes__item.add-upload {
    width: 104.2px;
    height: 104.2px;
    background-color: #a7a8a9;
    border: 1px solid #b6b5b6; }
    .scenes__item.add-upload .link-update {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      margin: 0 auto;
      color: #fff;
      width: 100%;
      height: 100%; }
  .scenes__item-img {
    background-color: #009ace;
    border: 1px solid #b6b5b6;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat; }
  .scenes__item-date {
    color: #009ace;
    font-size: 10px;
    margin-top: 5px;
    margin-bottom: 5px; }
  .scenes__item-title {
    font-size: 13px;
    color: #363636;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 16px;
    max-height: 48px;
    -webkit-line-clamp: 3;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /*! autoprefixer: on */
    word-wrap: break-word; }
  .scenes__item:hover .setting-edit {
    visibility: visible;
    opacity: 1; }

.list-scenes {
  padding-left: 0; }
  .list-scenes li.scenes__title:first-child a.up,
  .list-scenes li.scenes__title:last-child a.down {
    display: none; }


.width-sm .scenes__item-img,
.width-md .scenes__item-img,
.width-lg .scenes__item-img,
.width-free .scenes__item-img {
  height: 104.2px;
  width: 100%; }

.width-sm {
  width: 52.1px; }

.width-md {
  width: 185.24444px; }

.width-lg {
  width: 138.93333px; }

.width-free {
  min-width: 50px; }

@media (max-width: 576px) {
  .width-lg,
  .width-md {
    width: 32%;
    max-width: calc((100vw - 43px) / 3); }
  .width-free {
    max-width: calc((100vw - 43px) / 3);
    width: calc((100vw - 43px) / 3) !important; } }

.massenger__column {
  margin-bottom: 40px; }

.massenger__videodone {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  bottom: -40px;
  left: 0;
  width: 100%;
  font-size: 30px; }
  .massenger__videodone-rate {
    margin: 0 5px; }
  .massenger__videodone .stars {
    padding: 0; }
  .massenger__videodone .star {
    float: left;
    font-size: 13px;
    color: #53565a;
    letter-spacing: 2px;
    list-style-type: none;
    cursor: pointer; }
    .massenger__videodone .star.selected, .massenger__videodone .star:hover {
      color: #f0ea0c; }

.massenger .banner {
  margin-bottom: 0; }

.massenger__header {
  position: relative;
  padding: 10px 0 10px;
  border-bottom: 1px solid #e9e9e9;
  margin-top: 10px; }
  .massenger__header-icon {
    position: absolute;
    top: 2px;
    left: 0;
    font-size: 33px;
    color: #53565a; }
    .massenger__header-icon:hover {
      color: #009ace; }
  .massenger__header--icon-prev {
    right: 0;
    left: auto; }
  .massenger__header .disable-click {
    pointer-events: none;
    color: #a7a8a9; }
  .massenger__header .active {
    color: #009ace; }

.massenger__nav {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin: 0 20px; }

.massenger__breadcrumbs {
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; }
  .massenger__breadcrumbs a {
    color: #000;
    font-size: 18px; }
  .massenger__breadcrumbs span {
    font-size: 13px;
    margin-top: 5px; }

.massenger__filter {
  position: absolute;
  right: 0;
  top: 10px; }
  .massenger__filter .far {
    display: block; }
  .massenger__filter .fas {
    display: none; }
  .massenger__filter .active .far {
    display: none; }
  .massenger__filter .active .fas {
    display: block;
    color: #e50914; }
  .massenger__filter .link-icon {
    color: #a7a8a9;
    font-size: 25px; }
    .massenger__filter .link-icon:hover, .massenger__filter .link-icon:active, .massenger__filter .link-icon:focus {
      text-decoration: none; }

.massenger__left {
  position: relative; }

.massenger__main {
  padding-top: 30px; }

.massenger__video {
  width: 100%;
  margin: 5px 0; }
  .massenger__video-addvideo {
    position: absolute;
    top: -25px;
    right: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #53565a;
    text-align: center; }
    .massenger__video-addvideo a {
      color: #fff;
      display: block;
      text-decoration: none;
      line-height: 19px; }
    .massenger__video-addvideo:hover, .massenger__video-addvideo:active {
      background-color: #009ace; }
  .massenger__video-bookmark {
    color: #53565a;
    position: absolute;
    top: 0;
    right: 0;
    font-size: 26px; }
    .massenger__video-bookmark.active {
      color: #009ace; }
    .massenger__video-bookmark:hover {
      color: #009ace; }
  .massenger__video video {
    background-color: #a7a8a9; }
  @media (max-width: 992px) {
    .massenger__video {
      margin-bottom: 40px; } }

.massenger__taskbar {
  height: 6px;
  left: 0;
  right: 0;
  bottom: 15px;
  position: absolute;
  width: calc(100% - 80px);
  margin: 0 auto; }
  .massenger__taskbar-content {
    position: relative; }
  .massenger__taskbar-item {
    border: 1px solid #a7a8a9;
    background-color: #fff;
    height: 6px;
    border-radius: 10px; }
  .massenger__taskbar-pin {
    height: 4px;
    position: absolute;
    bottom: 12px;
    transform: rotate(40deg); }
    .massenger__taskbar-pin a {
      color: #000;
      font-size: 12px; }

.massenger__add {
  min-height: 200px; }
  .massenger__add-link {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #a7a8a9;
    border: 1px solid #a7a8a9;
    font-size: 80px;
    color: #fff;
    height: 100%;
    min-height: 200px; }
    .massenger__add-link:hover, .massenger__add-link:active, .massenger__add-link:focus {
      text-decoration: none;
      color: #fff; }
    .massenger__add-link:nth-child(2) {
      border-left: 0; }
      @media (max-width: 768px) {
        .massenger__add-link:nth-child(2) {
          border-left: 1px solid #a7a8a9; } }

.massenger__config {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: none; }
  .massenger__config-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative; }
  .massenger__config-close {
    position: absolute;
    top: -10px;
    left: -5px;
    width: 20px;
    height: 20px; }
    .massenger__config-close a {
      color: #fff;
      font-size: 20px; }
    .massenger__config-close i {
      border: 1px solid #b6b5b6;
      border-radius: 50%; }
  .massenger__config-hr {
    width: 100%;
    float: left;
    background-color: #fff;
    height: 2px;
    position: absolute;
    top: 50%; }
  .massenger__config-up {
    position: absolute;
    top: 0;
    left: 0;
    right: 0; }
  .massenger__config-down {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0; }
  .massenger__config-box {
    width: 80px;
    height: 120px;
    text-align: center;
    position: relative; }
    .massenger__config-box a {
      font-size: 60px;
      line-height: 1;
      color: #fff;
      -webkit-text-emphasis: center;
              text-emphasis: center; }
  .massenger__config-number {
    position: absolute;
    bottom: 0;
    left: 5px; }
    .massenger__config-number span {
      font-size: 24px;
      color: #fff; }
  .massenger__config-edit {
    position: absolute;
    bottom: 1px;
    right: 5px; }
    .massenger__config-edit-link {
      font-size: 22px;
      cursor: pointer;
      color: #53565a; }
      .massenger__config-edit-link:hover, .massenger__config-edit-link:active {
        color: #009ace; }
      .massenger__config-edit-link:focus {
        color: #53565a; }

.massenger .container {
  position: relative; }

.massenger .show-item {
  display: block; }

.edit-thumb__content {
  margin-bottom: 20px; }

.edit-thumb__submit .button {
  width: 130px;
  border-radius: 5px;
  background-color: #009ace;
  color: #fff; }
  .edit-thumb__submit .button:hover {
    background-color: #007399; }

.comment__top-pin {
  display: flex;
  justify-content: start;
  align-items: unset; }

.comment__download {
  margin-left: 5px; }
  .comment__download-top {
    margin-left: 5px;
    color: #000;
    font-size: 13px; }
    .comment__download-top:hover, .comment__download-top.active {
      color: #009ace; }
  .comment__download-icon-down {
    color: #fff;
    font-size: 10px;
    background-color: #53565a;
    padding: 3px 10px 5px;
    border-radius: 20px;
    text-decoration: none;
    display: inline-block;
    height: 18px;
    line-height: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 180px; }
    .comment__download-icon-down.active, .comment__download-icon-down:hover {
      background-color: #009ace;
      color: #fff; }
    @media (max-width: 320px) {
      .comment__download-icon-down {
        max-width: 72px !important; } }
    @media (max-width: 576px) {
      .comment__download-icon-down {
        max-width: 90px; } }
    @media (min-width: 576px) and (max-width: 992px) {
      .comment__download-icon-down {
        max-width: 120px; } }
  .comment__download .fa-download {
    font-size: 13px;
    margin-right: 5px; }
  .comment__download--reply {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 5px;
    margin: 0 40px 0 0; }
  .comment__download--bottom {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-left: 40px;
    padding-top: 5px; }

.comment__pintime {
  display: flex;
  justify-content: flex-start;
  align-items: end;
  margin-bottom: 5px; }
  .comment__pintime .fa-thumbtack {
    font-size: 11px;
    transform: rotate(40deg);
    width: 17px;
    height: 17px;
    color: #fff;
    background-color: #53565a;
    border-radius: 50%;
    line-height: 20px;
    text-align: center;
    cursor: pointer; }
    .comment__pintime .fa-thumbtack:hover, .comment__pintime .fa-thumbtack.active {
      background-color: #009ace; }
  .comment__pintime .fa-play-circle,
  .comment__pintime .fa-pause-circle {
    font-size: 16px;
    color: #53565a;
    border-radius: 50%;
    line-height: 20px;
    text-align: center;
    cursor: pointer; }
  .comment__pintime-time {
    color: #000;
    text-decoration: underline;
    margin-left: 5px;
    font-size: 13px; }
  .comment__pintime.active-item .fa-play-circle,
  .comment__pintime.active-item .fa-pause-circle,
  .comment__pintime.active-item .comment__pintime-time, .comment__pintime:hover .fa-play-circle,
  .comment__pintime:hover .fa-pause-circle,
  .comment__pintime:hover .comment__pintime-time {
    color: #009ace; }
  .comment__pintime.active-item .fa-thumbtack,
  .comment__pintime.active-item .comment__download-icon-down, .comment__pintime:hover .fa-thumbtack,
  .comment__pintime:hover .comment__download-icon-down {
    color: #fff;
    background-color: #009ace; }
  .comment__pintime.disabled-item {
    pointer-events: none; }
    .comment__pintime.disabled-item .fas {
      color: #a7a8a9; }
    .comment__pintime.disabled-item .comment__pintime-time {
      color: #a7a8a9; }
    .comment__pintime.disabled-item .comment__download-icon-down {
      background-color: #a7a8a9; }
  .comment__pintime.fixed-item .fas {
    color: #000; }
  .comment__pintime.fixed-item .comment__pintime-time {
    color: #000; }
  .comment__pintime.fixed-item .comment__download-icon-down {
    background-color: #000; }

.comment__item {
  width: 100%;
  margin-bottom: 10px; }
  .comment__item-bgr {
    display: flex;
    justify-content: flex-start;
    align-items: normal; }
  .comment__item-right {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    width: 100%; }

.comment__date {
  border: 1px solid #009ace;
  text-align: center;
  font-size: 9px;
  color: #009ace;
  margin-bottom: 10px;
  border-radius: 10px;
  padding: 2px; }

.comment__form {
  width: 100%;
  clear: both; }

.comment__emoticon {
  width: 100px;
  height: 100px; }

.comment__avatar {
  width: 35px;
  height: 35px;
  margin-right: 10px; }
  .comment__avatar img {
    border-radius: 50%; }

.comment img {
  max-width: 100%;
  overflow: hidden;
  height: 100%; }

.fa-download:hover {
    color: #0076a5;
}

.comment__content {
  min-height: 40px;
  background-color: #a7a8a9;
  border: 1px solid #a7a8a9;
  border-radius: 20px;
  padding: 10px;
  position: relative;
  max-width: 360px;
  width: calc(100% - 66px); }
  .comment__content p,
  .comment__content a {
    margin-bottom: 0;
    word-break: break-word; }
  .comment__content-desc {
    font-size: 12px; }

.comment__reply {
  margin: 10px 0; }
  .comment__reply .open {
    display: block; }
  .comment__reply .none {
    display: none; }
  .comment__reply-right {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    position: relative;
    width: 100%; }
  .comment__reply-bgr {
    display: flex;
    justify-content: flex-end;
    align-items: normal; }
  .comment__reply-avatar {
    width: 35px;
    height: 35px;
    margin-left: 10px; }
    .comment__reply-avatar img {
      border-radius: 50%; }
  .comment__reply-view {
    min-height: 15px; }
  .comment__reply-list {
    display: inline-block;
    position: relative;
    margin-bottom: 0; }
    .comment__reply-list > li {
      float: right;
      margin-left: 0;
      margin-right: 7px; }
    .comment__reply-list ul {
      left: 0;
      right: auto; }
  .comment__reply-content {
    min-height: 40px;
    border: 1px solid #a7a8a9;
    border-radius: 20px;
    padding: 10px;
    position: relative;
    width: calc(100% - 66px);
    max-width: 360px;
    z-index: 1; }
  .comment__reply-setting {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 10px;
    overflow: hidden;
    position: absolute;
    height: 100%;
    width: 90px;
    left: -100px;
    top: -13px; }
    @media (max-width: 1199px) {
      .comment__reply-setting {
        max-width: 120px; } }
  .comment__reply-link {
    cursor: pointer; }
  .comment__reply-edit {
    margin-left: 5px; }
    .comment__reply-edit i {
      line-height: 20px; }
  .comment__reply-remove {
    margin-right: 5px; }
    .comment__reply-remove i {
      line-height: 20px; }
  .comment__reply-edit-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5px;
    background-color: #757575;
    border-radius: 20px;
    height: 18px;
    width: 0;
    transition: all .1s;
    transform: scale(0);
    margin-top: 9px; }
    .comment__reply-edit-box.edit-show {
      transform: scale(1);
      transition: all .1s;
      width: 50px; }
    .comment__reply-edit-box a {
      color: #fff;
      margin: 0 5px;
      font-size: 12px; }
  .comment__reply-desc {
    font-size: 12px; }
    .comment__reply-desc p {
      word-break: break-word;
      margin-bottom: 0; }

.comment__view-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 5px; }

.comment__view-item {
  width: 15px;
  height: 15px;
  margin-left: 7px;
  border-radius: 50%;
  position: relative; }
  .comment__view-item--add {
    width: auto;
    border: 0; }
    .comment__view-item--add .dropdown-link {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #363636;
      font-size: 7px;
      cursor: pointer;
      float: right;
      border: 1px solid #a7a8a9;
      text-align: center;
      border-radius: 50%;
      padding: 0;
      margin-top: 4px;
      height: 15px;
      min-width: 15px;
      letter-spacing: -1px; }
  .comment__view-item img {
    border-radius: 50%; }

.comment__view-dropdown {
  display: none;
  max-width: 200px;
  left: auto;
  right: 0;
  margin-top: 10px;
  z-index: 11; }
  .comment__view-dropdown .dropdown-item a {
    font-size: 12px;
    white-space: normal;
    word-break: break-word; }
  .comment__view-dropdown p {
    margin-top: 3px;
    margin-bottom: 0; }
  .comment__view-dropdown img {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    overflow: hidden;
    float: left;
    margin-right: 10px; }

.comment__textarea {
  position: relative;
  margin-top: 25px; }
  .comment__textarea-emoticon {
    font-size: 21px;
    color: #009ace;
    position: absolute;
    top: 0;
    right: 48px;
    z-index: 10; }
  .comment__textarea-emoticoninfo {
    top: auto;
    bottom: 50px; }
    .comment__textarea-emoticoninfo.show-icon {
      display: block; }
  .comment__textarea-emoticonimg {
    width: 100px;
    height: 100px;
    float: left; }
    .comment__textarea-emoticonimg img {
      border-radius: 0; }
  .comment__textarea-edit {
    width: 100%;
    border: 0;
    display: none; }
    .comment__textarea-edit:focus {
      outline: 0; }
  .comment__textarea-item {
    width: 100%;
    position: relative; }
  .comment__textarea-file {
    position: absolute;
    top: -15px;
    left: 0;
    font-size: 10px;
    line-height: 1; }
    .comment__textarea-file span {
      max-width: 120px;
      word-break: break-word;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      float: left; }
    .comment__textarea-file .close {
      font-size: 10px; }
  .comment__textarea-upload {
    font-size: 13px;
    color: #000;
    margin-right: 5px;
    transform: rotate(40deg);
    position: absolute;
    top: 5px;
    left: 10px;
    z-index: 10; }
    .comment__textarea-upload:hover {
      color: #000; }
  .comment__textarea-pin {
    font-size: 13px;
    color: #000;
    transform: rotate(48deg);
    position: absolute;
    top: 5px;
    left: 30px;
    z-index: 10; }
    .comment__textarea-pin:hover {
      color: #000; }
  .comment__textarea-submit {
    float: right;
    margin-top: -4px; }
    .comment__textarea-submit .submit-link {
      color: #707070;
      font-size: 24px; }
    .comment__textarea-submit .notEmpty {
      color: #009ace; }
  .comment__textarea textarea {
    border: 1px solid #a7a8a9;
    border-radius: 20px;
    padding: 5px 30px 5px 50px;
    width: calc(100% - 40px);
    resize: none;
    height: 30px; }
    .comment__textarea textarea::-webkit-scrollbar {
      display: none; }
    .comment__textarea textarea:focus {
      outline: none; }
  .comment__textarea input[type='submit'] {
    background-color: #009ace;
    width: 100%;
    padding: 10px;
    border: 0;
    color: #fff;
    border-radius: 4px;
    transition: all .3s ease;
    max-width: 150px;
    float: right;
    text-transform: capitalize;
    margin-top: 10px; }
    .comment__textarea input[type='submit']:hover {
      background-color: #007399; }

.comment .wd-auto {
  width: auto; }

@media (max-width: 576px) {
  .comment__textarea-emoticoninfo {
    top: 100%;
    bottom: auto; } }

.setting-config {
  font-size: 22px;
  cursor: pointer;
  color: #53565a; }
  .setting-config.active {
    color: #009ace; }

.star.selected:hover .fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400; }

.videoicon .icon {
  display: none;
  color: #53565a; }

.videoicon.disable-click .null {
  display: inline-block;
  pointer-events: none;
  color: #a7a8a9; }

.videoicon.ic-done .done {
  display: inline-block;
  color: #e50914; }

.videoicon.ic-null .null {
  display: inline-block; }
  .videoicon.ic-null .null:hover {
    color: #e50914; }

.videoicon.heart-active:hover .done {
  display: none; }

.videoicon.heart-active:hover .null {
  display: inline-block;
  color: #e50914; }

.order__btnnew {
  text-align: center;
  padding: 30px 0;
  background-color: #f8f8f8; }
  .order__btnnew button {
    padding: 5px 20px;
    border-radius: 20px;
    border: 0;
    background-color: #000;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 1px; }
    .order__btnnew button:hover, .order__btnnew button:active {
      background-color: #1a1a1a;
      color: #fff; }

.user-info__main {
  padding-top: 30px; }

.user-info__images img {
  max-width: 100%; }

.user-info__form {
  max-width: 800px;
  margin: 0 auto; }
  .user-info__form label {
    margin-left: 13px; }
  .user-info__form .form-control {
    border-radius: 20px; }

.user-info__title {
  margin-top: 5px;
  color: #707070; }

.user-info__upload {
  position: relative;
  overflow: hidden;
  display: inline-block; }
  .user-info__upload-title {
    color: #fff;
    background-color: #a7a8a9;
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    margin-left: 0; }
  .user-info__upload-file {
    font-size: 0;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0; }

.user-info__notifi {
  font-weight: 600; }
  .user-info__notifi-title {
    color: #a7a8a9;
    margin: 0 0 0 13px; }
  .user-info__notifi input {
    margin-right: 3px;
    width: 20px;
    height: 20px;
    position: relative;
    top: 5px; }

.user-info__project {
  position: relative; }
  .user-info__project-header {
    display: flex;
    justify-content: space-between;
    align-items: center; }
  .user-info__project-title {
    color: #a7a8a9;
    margin: 0 13px;
    padding-bottom: 5px; }
  .user-info__project-icon {
    cursor: pointer; }
    .user-info__project-icon:hover {
      color: #009ace; }
  .user-info__project-content {
    right: 0;
    left: auto;
    width: 100%;
    min-height: 35px;
    max-height: 152px;
    overflow-x: hidden; }
    .user-info__project-content.show-item {
      display: block; }
  .user-info__project-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 15px; }
  .user-info__project-des {
    width: calc(100% - 45px);
    text-align: left; }

.user-info__email {
  clear: both; }

q.user-info__submit .btn {
  width: 180px;
  border-radius: 30px;
  background-color: #009ace;
  color: #fff; }
  .user-info__submit .btn:hover {
    background-color: #007399; }

.user-info__form-bgr {
  background-color: #f8f8f8;
  padding: 15px;
  margin-bottom: 15px; }

.user-info .switch {
  position: relative;
  display: inline-block;
  width: 45px;
  height: 25px;
  margin-bottom: 0; }
  .user-info .switch input {
    opacity: 0;
    width: 0;
    height: 0; }
  .user-info .switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #757575;
    transition: .4s; }
    .user-info .switch .slider:before {
      position: absolute;
      content: '';
      height: 18px;
      width: 18px;
      left: 4px;
      bottom: 4px;
      background-color: #fff;
      transition: .4s; }
  .user-info .switch input:checked + .slider {
    background-color: #009ace; }
  .user-info .switch input:focus + .slider {
    box-shadow: 0 0 1px #009ace; }
  .user-info .switch input:checked + .slider:before {
    transform: translateX(18px); }
  .user-info .switch .slider.round {
    border-radius: 34px; }
    .user-info .switch .slider.round:before {
      border-radius: 50%; }

.acc-manager__header-title {
  margin: 0 0 40px;
  padding-bottom: 5px;
  border-bottom: 2px solid #f0f0f0;
  }

.acc-manager__title {
  color: #009ace; }

.acc-manager__done {
  color: #e50914; }

.acc-manager__rating {
  color: #f0ea0c; }

.acc-manager__images {
  display: block;
  width: 40px;
  height: 40px;
  float: left;
  margin-right: 20px; }
  @media (max-width: 768px) {
    .acc-manager__images {
      width: 20px;
      height: 20px;
      margin-right: 10px; } }
  .acc-manager__images img {
    max-width: 100%;
    border-radius: 50%; }

.acc-manager__config {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-height: 16px;
  max-height: 32px;
  -webkit-line-clamp: 2;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  word-wrap: break-word; }

.acc-manager__seting, .acc-manager__close {
  margin-left: 10px;
  color: #53565a;
  font-size: 16px; }

.acc-manager__email, .acc-manager__ip {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  word-break: break-word; }

.acc-manager__ip {
  justify-content: center; }

.acc-manager__email span {
  width: calc(100% - 30px); }

.acc-manager__add {
  font-size: 30px;
  color: #53565a; }
  @media (max-width: 768px) {
    .acc-manager__add {
      font-size: 25px; } }

.acc-manager__close {
  margin-left: 0; }

.acc-manager__button {
  width: 150px;
  background-color: #009ace;
  color: #fff;
  border: 0;
  padding: 5px 20px;
  border-radius: 20px; }

.acc-manager a {
  color: #53565a; }
  .acc-manager a:hover {
    color: #009ace; }

.acc-manager .checkbox {
  width: 18px;
  height: 18px;
  margin: 0 auto; }
  .acc-manager .checkbox input[type='checkbox'] {
    top: 0; }

.acc-manager .table {
  table-layout: fixed; }

.acc-manager .table > tbody > tr > td,
.acc-manager .table > tbody > tr > th,
.acc-manager .table > tfoot > tr > td,
.acc-manager .table > tfoot > tr > th,
.acc-manager .table > thead > tr > td,
.acc-manager .table > thead > tr > th {
  vertical-align: middle;
  border: 0;
}

.acc-manager .table > thead > tr,
.acc-manager .table > tbody > tr:nth-child(2n) {
  background-color: #f8f8f8; }

.acc-manager .table > thead > tr > th {
  font-size: 16px; }
  @media (max-width: 768px) {
    .acc-manager .table > thead > tr > th {
      font-size: 13px; } }

.acc-manager .table > tbody > tr > td {
  font-size: 11px; }
  @media (max-width: 768px) {
    .acc-manager .table > tbody > tr > td {
      font-size: 10px; } }

.acc-manager .wd-45 {
  width: 45%; }

.acc-manager .wd-31 {
  width: 31%; }

.acc-manager .wd-8 {
  width: 8%; }

.messenger {
  font-size: 10px; }
  .messenger__header {
    height: 45px;
    display: flex;
    padding: 0 10px;
    align-items: center;
    position: relative;
    z-index: 2; }
    @media screen and (max-width: 767px) {
      .messenger__header {
        padding: 0; } }
  .messenger__header-icon {
    margin-left: auto; }
  .messenger .header-icon {
    margin-right: 15px; }
    .messenger .header-icon:last-child {
      margin-right: 0; }
  .messenger__list {
    height: calc(100vh - 103px);
    overflow: auto; }
    @media screen and (max-width: 767px) {
      .messenger__list {
        margin: 0 -15px;
        height: auto; } }
  .messenger__avatar {
    margin-right: 15px;
    width: 75px;
    padding-left: 15px;
    align-self: flex-end;
    position: relative; }
    .messenger__avatar-img {
      border-radius: 50%;
      width: 60px; }
  .messenger__user-active {
    position: absolute;
    width: 15px;
    height: 15px;
    right: 0;
    bottom: 1px;
    background-color: #009ace;
    border: 1px solid #fff;
    border-radius: 50%; }
  .messenger__user-deactive {
    position: absolute;
    width: 15px;
    height: 15px;
    right: 0;
    bottom: 1px;
    background-color: #e6002d;
    border: 1px solid #fff;
    border-radius: 50%; }
  .messenger__info {
    margin-left: 10px;
    width: calc(100% - 75px);
    -ms-grid-row-align: center;
        align-self: center; }
  .messenger__name {
    color: #7e7e7e;
    font-size: 16px; }
  .messenger__work {
    color: #6e6e6e;
    margin-top: 2px; }
  .messenger__mess {
    color: #6e6e6e;
    line-height: 2; }
    .messenger__mess-item {
      margin-bottom: 5px; }
  .messenger__seen {
    position: absolute;
    bottom: 13px;
    right: 25px; }
    .messenger__seen-img {
      width: 15px; }
  .messenger__status {
    position: absolute;
    top: 15px;
    right: 0;
    color: #53565a; }
  .messenger__tag {
    background-color: #009ace;
    color: #fff;
    height: 20px;
    line-height: 20px;
    text-transform: uppercase;
    padding: 0 5px 0 8px;
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;
    }

  .messenger__time {
    font-size: 8px;
    padding-right: 15px;
    padding-bottom: 5px;
    align-self: flex-end;
  }

  .messenger__item {
    background-color: #fbfbfb;
    display: flex;
    margin-bottom: 30px;
    padding: 9px 10px 9px 10px;
    position: relative;
    border-radius: 10px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .messenger__item--selected {
      border: 1px solid #009ace; }
    .messenger__item--new, .messenger__item--detail {
      background-color: #a7a8a9; }
      .messenger__item--new .messenger__name, .messenger__item--detail .messenger__name {
        color: #000; }
      .messenger__item--new .messenger__mess, .messenger__item--detail .messenger__mess {
        color: #6e6e6e; }
    .messenger__item--detail, .messenger__item--left {
      padding-right: 140px;
      margin-bottom: 5px; }
      @media screen and (max-width: 767px) {
        .messenger__item--detail, .messenger__item--left {
          padding-right: 50px; } }
    .messenger__item--left {
      background-color: #a7a8a9; }
    .messenger__item--right {
      flex-direction: row-reverse;
      padding-left: 140px;
      margin-bottom: 5px; }
      @media screen and (max-width: 767px) {
        .messenger__item--right {
          padding-left: 50px; } }
      .messenger__item--right .messenger__avatar {
        padding-right: 15px;
        padding-left: 0; }
      .messenger__item--right .messenger__info {
        margin-left: 0;
        margin-right: 10px; }
      .messenger__item--right .messenger__mess-item {
        float: right;
        clear: right; }
      .messenger__item--right .messenger__status {
        left: 0; }
      .messenger__item--right .messenger__time {
        padding-left: 10px; }
      .messenger__item--right .messenger__seen {
        left: 25px;
        right: auto; }
  .messenger__notice {
    background-color: #53565a;
    padding: 8px 50px;
    color: #fff;
    margin-bottom: 5px;
    line-height: 2; }
  .messenger__column-left {
    padding-right: 0;
    width: 38.2%; }
    @media screen and (max-width: 767px) {
      .messenger__column-left {
        padding-right: 15px;
        width: 100%; } }
    .messenger__column-left .messenger__mess {
      max-width: 90%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis; }
  .messenger__column-right {
    padding-left: 0;
    width: 61.8%; }
    @media screen and (max-width: 767px) {
      .messenger__column-right {
        padding-left: 15px;
        width: 100%; } }

.header-button {
  border-bottom: 1px solid #a7a8a9;
  display: inline-block;
  position: relative;
  color: #009ace;
  overflow: hidden; }
  .header-button:hover {
    color: #009ace;
    cursor: pointer; }
  .header-button:before {
    content: '';
    position: absolute;
    bottom: 0;
    background-color: #a7a8a9;
    height: 1px;
    width: 28px; }
  .header-button--back {
    padding: 5px 13px 2px 24px; }
    .header-button--back:before {
      transform: rotate(-45deg);
      left: -12px; }
  .header-button--next {
    padding: 5px 24px 2px 13px; }
    .header-button--next:before {
      transform: rotate(45deg);
      right: -12px; }

.messenger-detail {
  height: calc(100vh - 103px);
  position: relative; }
  @media screen and (max-width: 767px) {
    .messenger-detail {
      /* margin: 0 -15px; */
      height: auto; } }
  @media screen and (max-width: 767px) {
    .messenger-detail--order-accepted {
      margin-top: -45px;
      z-index: 0; } }
  .messenger-detail .messenger-detail-content {
    max-height: calc(100vh - 154px);
    height: calc(100% - 54px);
    overflow: auto; }
    @media screen and (max-width: 767px) {
      .messenger-detail .messenger-detail-content {
        height: auto;
        max-height: none !important;
        padding-bottom: 54px; } }
  .messenger-detail__offer {
    padding-left: 100px;
    padding-right: 140px;
    margin-bottom: 5px; }
    @media screen and (max-width: 767px) {
      .messenger-detail__offer {
        padding-left: 0;
        padding-right: 0; } }
  .messenger-detail__accept-offer {
    padding-right: 100px;
    text-align: right;
    margin: 10px 0 5px; }
  .messenger-detail__done-offer {
    margin-bottom: 5px; }
  .messenger-detail__thumb {
    position: relative; }
    .messenger-detail__thumb-img {
      width: 100%; }
    .messenger-detail__thumb .button {
      position: absolute;
      font-size: 20px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: calc(100% - 76px); }
  .messenger-detail__conditions {
    background-color: #a7a8a9;
    margin-top: 5px;
    padding: 5px 40px; }
    .messenger-detail__conditions-title {
      background-color: #009ace;
      color: #fff;
      height: 16px;
      line-height: 16px;
      display: inline-block;
      border-radius: 10px;
      padding: 0 30px;
      margin-bottom: 8px; }
    .messenger-detail__conditions-item {
      display: flex;
      padding: 3px 8px; }
  .messenger-detail .box-item {
    text-align: center; }
    .messenger-detail .box-item__icon {
      width: 50px;
      height: 50px;
      background-color: #fff;
      border-radius: 50%;
      position: relative; }
    .messenger-detail .box-item__img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%); }
    .messenger-detail .box-item__title {
      color: #53565a;
      margin-top: 6px; }
    .messenger-detail .box-item__desc {
      color: #000;
      margin-top: 3px; }
  .messenger-detail__boxes {
    background-color: #a7a8a9;
    display: flex;
    justify-content: space-between;
    padding: 5px 60px; }
  .messenger-detail__contract {
    background-color: #a7a8a9;
    padding: 0 10px 8px 0;
    text-align: right; }
  .messenger-detail__confirm {
    background-color: #a7a8a9;
    padding: 0 10px 8px 0;
    text-align: right; }
  .messenger-detail__action {
    border-top: 1px solid #a7a8a9;
    background-color: #fff;
    padding-top: 5px;
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 54px; }
    @media screen and (max-width: 767px) {
      .messenger-detail__action {
        position: fixed; } }
    .messenger-detail__action .button {
      margin: 0 7px; }
      .messenger-detail__action .button:first-child {
        margin-left: 0; }
      .messenger-detail__action .button:last-child {
        margin-right: 0; }
    .messenger-detail__action-input {
      display: flex;
      padding: 0 25px;
      justify-content: space-between;
      align-items: center; }
  .messenger-detail__input {
    border: none;
    width: calc(100% - 40px);
    text-align: left;
    display: flex; }
    .messenger-detail__input-attach {
      display: none !important;
      /* stylelint-disable-line */ }
    .messenger-detail__input label {
      margin: 0;
      -ms-grid-row-align: center;
          align-self: center; }
      .messenger-detail__input label:hover {
        cursor: pointer; }
    .messenger-detail__input-text {
      border: none;
      padding: 15px 0 0 10px;
      height: 30px;
      min-height: 30px;
      width: 100%;
      overflow: hidden;
      resize: none; }
      .messenger-detail__input-text:focus {
        outline: none; }
    .messenger-detail__input .cs-textarea-wrapper {
      height: 45px;
      width: 100%;
      overflow: hidden; }
  .messenger-detail__button-send {
    width: 25px;
    text-align: center; }

.visible-mobile {
  display: none; }
  @media screen and (max-width: 767px) {
    .visible-mobile {
      display: block; } }

.custom-scrollbar .mCSB_scrollTools {
  margin: 0 !important;
  /* stylelint-disable-line */
  width: 5px; }

.hiddendiv {
  position: absolute;
  top: -9999px;
  left: -9999px;
  visibility: hidden; }

.audio-content {
  position: relative;
  margin-top: 15px; }
  .audio-content .audio-controls {
    display: flex;
    position: relative; }
  .audio-content .audio-remain {
    position: absolute;
    right: 0;
    top: 0;
    color: #009ace; }
    @media (max-width: 768px) {
      .audio-content .audio-remain {
        font-size: 10px; } }
  .audio-content .button--icon-settings {
    position: absolute;
    right: 0;
    bottom: -10px; }
    @media (max-width: 768px) {
      .audio-content .button--icon-settings {
        bottom: -23px; } }

.audio-list {
  position: absolute;
  left: 0;
  top: -10px;
  z-index: 9; }
  @media (max-width: 768px) {
    .audio-list {
      top: 0; } }
  .audio-list__title {
    margin-bottom: 20px;
    position: relative; }
    @media (max-width: 768px) {
      .audio-list__title {
        margin-bottom: 10px; } }
    .audio-list__title-content {
      white-space: nowrap;
      position: relative;
      left: 0;
      margin: 0; }
  .audio-list__items {
    display: flex;
    align-items: center; }
  .audio-list__item {
    width: 5px;
    height: 5px;
    background-color: #a7a8a9;
    border-radius: 50%;
    margin: 0 5px; }
    .audio-list__item:first-child {
      margin-left: 0; }
    .audio-list__item:last-child {
      margin-right: 0; }
    .audio-list__item.active {
      width: 7px;
      height: 7px;
      background-color: #009ace; }

.audio-control {
  display: flex;
  align-items: center;
  margin: 0 auto;
  min-width: 180px;
  text-align: center; }
  @media (max-width: 768px) {
    .audio-control {
      min-width: 102px; } }
  .audio-control__prev {
    background-image: url("../images/icon-back.svg"); }
  .audio-control__next {
    background-image: url("../images/icon-next.svg"); }
  .audio-control__prev, .audio-control__next {
    width: 23px;
    height: 37px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat; }
    .audio-control__prev:hover, .audio-control__next:hover {
      cursor: pointer; }
    @media (max-width: 768px) {
      .audio-control__prev, .audio-control__next {
        width: 12px;
        height: 19px; } }
  .audio-control__playpause {
    font-size: 0;
    width: 34px;
    height: 38px;
    background-image: url("../images/icon-play.svg");
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    margin: 0 50px; }
    .audio-control__playpause:hover {
      cursor: pointer; }
    @media (max-width: 768px) {
      .audio-control__playpause {
        width: 17px;
        height: 19px;
        margin: 0 30px; } }
    .audio-control__playpause.active {
      background-image: url("../images/icon-pause.svg"); }

.work-item {
  padding: 0 5px;
  margin-bottom: 25px;
  position: relative; }
  @media (max-width: 768px) {
    .work-item {
      padding: 0 2.5px; } }
  .work-item__label {
    font-size: 10px;
    position: absolute;
    top: 15px;
    right: 5px;
    background-color: #009ace;
    color: #fff;
    height: 20px;
    line-height: 20px;
    text-transform: uppercase;
    padding: 0 5px 0 8px;
    border-top-left-radius: 13px;
    border-bottom-left-radius: 13px;
    font-weight: 700; }
    @media (max-width: 768px) {
      .work-item__label {
        font-size: 8px;
        height: 13px;
        line-height: 13px;
        right: 2.5px;
        top: 5px;
        padding: 0 3px 0 5px; } }
  .work-item__image {
    padding-bottom: 56.25%;
    background-size: cover;
    background-position: center center; }
  .work-item__info {
    padding-top: 10px; }
  .work-item__date {
    color: #009ace; }
    @media (max-width: 768px) {
      .work-item__date {
        font-size: 10px; } }
  .work-item__title {
    color: #53565a;
    padding-top: 10px; }
    @media (max-width: 768px) {
      .work-item__title {
        font-size: 10px;
        padding-top: 5px; } }

.original-item {
  padding: 0 7.5px;
  margin-bottom: 60px; }
  @media (max-width: 768px) {
    .original-item {
      padding: 15px;
      background-color: #fcfcfc;
      margin-bottom: 20px; } }
  @media (max-width: 768px) {
    .original-item__content {
      display: flex; } }
  .original-item__image-bg {
    padding-bottom: 100%;
    background-size: cover;
    background-position: center center; }
    @media (max-width: 768px) {
      .original-item__image-bg {
        width: 105px; } }
  .original-item__info {
    padding-top: 20px; }
    @media (max-width: 768px) {
      .original-item__info {
        width: calc(100% - 105px);
        padding: 0 0 0 10px; } }
  .original-item__title {
    font-size: 16px;
    color: #000; }
    @media (max-width: 768px) {
      .original-item__title {
        font-size: 13px; } }
  .original-item__price {
    color: #009ace;
    font-size: 16px;
    padding-top: 8px; }
    @media (max-width: 768px) {
      .original-item__price {
        font-size: 13px;
        padding-top: 5px; } }
  .original-item__desc {
    color: #53565a;
    padding-top: 18px;
    line-height: 1.5; }
    @media (max-width: 768px) {
      .original-item__desc {
        font-size: 10px;
        padding-top: 5px;
        height: 50px;
        overflow: hidden; } }
  .original-item__button {
    text-align: center;
    padding-top: 30px; }
    @media (max-width: 768px) {
      .original-item__button .button {
        width: calc(100% - 20px); } }

.columns-5 .work-item {
  width: 20%; }
  @media (max-width: 768px) {
    .columns-5 .work-item {
      width: 105px;
      flex: 0 0 auto; } }

.columns-3 .original-item {
  width: 33.333333%; }
  @media (max-width: 768px) {
    .columns-3 .original-item {
      width: 100%; } }

.table-order > thead > tr > th {
  border-bottom: none;
  font-weight: 400;
  text-align: center; }

.table-order > tbody > tr > td {
  border-top: none;
  color: #000;
  text-align: center; }
  .table-order > tbody > tr > td:first-child {
    padding-left: 25px;
    text-align: left; }

.table-order > tbody > tr.row-month {
  /* stylelint-disable-line */
  border-bottom: 1px solid #a7a8a9; }
  .table-order > tbody > tr.row-month > td {
    padding-left: 0; }

.table-order__time {
  padding-left: 25px; }

.table-order__download {
  width: 24px;
  height: 16px;
  display: block;
  margin-top: 8px;
  background-image: url("../images/icon-download.svg");
  background-size: cover; }

.table-order__status {
  width: 20%; }
  @media (max-width: 768px) {
    .table-order__status {
      width: 23%; } }
  .table-order__status .input-checkbox {
    display: inline-block;
    line-height: 20px;
    height: 20px; }
    .table-order__status .input-checkbox input[type='checkbox'] {
      top: 0; }
      .table-order__status .input-checkbox input[type='checkbox']:before {
        border-radius: 50%; }
    .table-order__status .input-checkbox label {
      /* stylelint-disable-line */
      padding-left: 20px;
      margin-top: 0; }

@media (max-width: 768px) {
  .table-order__fee {
    width: 23%; } }

.table-order__category {
  color: #009ace;
  margin: 10px 0 0; }

.table-order__row-space {
  height: 15px; }
  .table-order__row-space:last-child {
    height: 0; }

.table-order.table-payment > tbody > tr > td {
  background-color: #fcfcfc;
  text-align: left; }
  .table-order.table-payment > tbody > tr > td.align-center {
    /* stylelint-disable-line */
    text-align: center; }
    @media (max-width: 768px) {
      .table-order.table-payment > tbody > tr > td.align-center {
        padding-right: 0;
        padding-left: 3px; } }
  .table-order.table-payment > tbody > tr > td a.align-center {
    /* stylelint-disable-line */
    margin: 5px auto 0 auto; }

.input-radio {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 15px;
  line-height: 20px; }
  .input-radio:hover {
    cursor: pointer; }
  .input-radio .check-mark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border-radius: 50%;
    border: 2px solid #53565a; }
    .input-radio .check-mark:after {
      top: 3px;
      left: 3px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #009ace;
      content: '';
      position: absolute;
      display: none; }
  .input-radio input {
    position: absolute;
    opacity: 0;
    width: 0;
    cursor: pointer; }
    .input-radio input:checked ~ .check-mark {
      border: 2px solid #009ace; }
      .input-radio input:checked ~ .check-mark:after {
        display: block; }

.input-checkbox input[type='checkbox']:before {
  background: #fff;
  border: 2px solid #53565a;
  border-radius: 3px;
  width: 20px;
  height: 20px; }

.input-checkbox input[type='checkbox']:checked:after {
  border-color: #53565a;
  top: 3px;
  left: 7px;
  width: 6px;
  height: 11px; }

.input-checkbox label {
  padding-left: 30px; }
  .input-checkbox label a {
    color: #009ace; }
    .input-checkbox label a:hover {
      color: #009ace; }

.account {
  font-size: 13px; }
  .account__container {
    padding: 0 20%; }
    @media screen and (max-width: 767px) {
      .account__container {
        padding: 0; } }
  .account__info {
    margin: 30px 0 45px; }
  .account__title {
    margin-bottom: 25px; }
    @media screen and (max-width: 767px) {
      .account__title {
        color: #fff;
        background-image: linear-gradient(to right, #a7a8a9, #333);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        margin-left: -15px;
        display: inline-block;
        min-width: 125px;
        padding: 1px 15px; } }
  .account__sub-title {
    margin-bottom: 15px;
    border-top: 1px solid #a7a8a9;
    padding-top: 10px; }
  .account__form-group {
    padding: 0 15px; }
  .account__personal-info {
    margin-bottom: 45px; }
  .account__notice {
    font-size: 10px;
    color: #53565a;
    margin-bottom: 25px;
    margin-top: -20px; }
  .account__column-2 {
    display: flex;
    align-items: flex-end; }
    .account__column-2 .form-group {
      width: 50%; }
      .account__column-2 .form-group:first-child {
        padding-right: 7.5px; }
      .account__column-2 .form-group:nth-child(2) {
        padding-left: 7.5px; }
    .account__column-2 .button {
      margin-bottom: 25px; }
  .account__input-title {
    color: #000;
    font-size: 16px;
    margin-bottom: 15px; }
  .account .button--background {
    height: 30px;
    line-height: 30px; }
  .account .combodate {
    display: flex;
    justify-content: space-between; }
    .account .combodate select {
      height: 30px;
      background-color: transparent;
      border-radius: 0;
      -webkit-appearance: none;
      appearance: none;
      border-color: #53565a;
      background-image: url("../images/icon-select-dropdown.svg");
      background-position: calc(100% - 10px) 50%;
      background-repeat: no-repeat;
      padding-left: 10px; }
      .account .combodate select:focus {
        outline: none; }
    .account .combodate .year {
      min-width: calc(50% - 7.5px); }
    .account .combodate .month,
    .account .combodate .day {
      min-width: calc(25% - 10.5px); }
  .account__action {
    margin: 45px 0 20px;
    text-align: center; }
    .account__action .button {
      font-size: 20px;
      min-width: 325px;
      text-transform: uppercase; }
      @media screen and (max-width: 767px) {
        .account__action .button {
          width: 100%;
          min-width: auto; } }
  .account__withdraw-notice {
    color: #009ace;
    padding-left: 28px;
    margin-top: 10px; }
  .account__tradeoff {
    padding-top: 5px;
    margin-bottom: 40px; }
  .account__trade-slider {
    display: flex;
    margin: 0 -10%; }
  .account__trade-item {
    width: 20%;
    position: relative; }
    .account__trade-item:hover {
      cursor: pointer; }
    .account__trade-item:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 5px;
      background-color: #a7a8a9;
      top: 50%;
      left: 50%;
      transform: translateY(-50%); }
    .account__trade-item:after {
      content: '';
      position: absolute;
      left: 50%;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      border: 3px solid #a7a8a9;
      background-color: #fff;
      transform: translate(-50%, -50%); }
    .account__trade-item:first-child:after {
      left: calc(50% - 7px); }
    .account__trade-item:last-child:after {
      left: calc(50% + 7px); }
    .account__trade-item:last-child:before {
      display: none; }
    .account__trade-item.active:after {
      width: 25px;
      height: 25px;
      background-color: #009ace;
      border: 3px solid #009ace; }
    .account__trade-item.active:first-child:after {
      left: calc(50% - 1.5px); }
    .account__trade-item.active:last-child:after {
      left: calc(50% + 1.5px); }
  .account__trade-label {
    display: flex;
    justify-content: space-between;
    padding-top: 15px;
    text-transform: uppercase; }
  .account__trade-description {
    margin-bottom: 35px; }
  .account__form-policy label {
    color: #53565a; }
  .account__form-policy .form-textarea {
    position: relative; }
  .account__form-policy .form-control {
    min-height: 120px;
    background-color: transparent; }
  .account__form-policy .fake-placeholder {
    position: absolute;
    top: 8px;
    left: 12px;
    color: #a7a8a9;
    z-index: -1; }
  .account__calendar {
    padding: 15px 0 10px; }
  .account .checkbox {
    margin-bottom: 15px; }
  .account__form-multi {
    display: flex;
    align-items: center;
    margin-bottom: 15px; }
    .account__form-multi .input-radio {
      margin-bottom: 0; }
  .account .notification-time {
    display: flex;
    align-items: center;
    border: 1px solid #53565a;
    height: 30px;
    padding: 0 15px;
    margin-left: 25px; }
    @media (max-width: 768px) {
      .account .notification-time {
        margin-left: auto; } }
    .account .notification-time .input-text {
      border: none; }
      @media (max-width: 768px) {
        .account .notification-time .input-text {
          width: 100px; } }
    .account .notification-time input:focus {
      outline: none; }
  .account .input-arrow {
    display: flex;
    flex-direction: column; }
  .account .input-button {
    background: none;
    border: none;
    color: #a7a8a9;
    padding: 0;
    font-size: 8px; }
  .account__checkbox-list {
    padding-top: 15px; }
  .account__form-label label {
    color: #707070; }
  .account__blocklist {
    margin-top: 40px; }
    .account__blocklist-description {
      color: #53565a;
      margin-bottom: 30px; }
    .account__blocklist .form-group {
      /* stylelint-disable-line */
      margin-bottom: 15px; }
    .account__blocklist-button {
      margin: 30px 0 25px;
      text-align: center; }
    .account__blocklist-date-title {
      color: #009ace;
      padding-top: 5px; }
    .account__blocklist-unblock {
      position: absolute;
      top: 0;
      right: 0;
      opacity: 0;
      visibility: hidden; }
      @media (max-width: 768px) {
        .account__blocklist-unblock .button {
          height: 40px;
          line-height: 40px; } }
    .account__blocklist-item {
      background-color: #fafbfb;
      display: flex;
      padding: 8px 15px;
      margin-bottom: 5px; }
      @media (max-width: 768px) {
        .account__blocklist-item {
          font-size: 10px; } }
      .account__blocklist-item:hover {
        background-color: #a7a8a9;
        transition: all .3s; }
        .account__blocklist-item:hover .account__blocklist-date-title,
        .account__blocklist-item:hover .account__blocklist-date-time {
          opacity: 0;
          visibility: hidden;
          transition: all .3s; }
        .account__blocklist-item:hover .account__blocklist-unblock {
          opacity: 1;
          visibility: visible;
          transition: all .3s; }
    .account__blocklist-right {
      margin-left: auto;
      position: relative; }
    .account__blocklist-name {
      color: #000;
      font-size: 16px; }
    .account__blocklist-url {
      color: #009ace;
      padding-top: 3px; }

.datepicker .datepicker-switch {
  text-align: center; }

.datepicker .prev {
  text-align: left;
  position: relative;
  font-size: 0; }
  .datepicker .prev:before {
    content: '';
    position: absolute;
    width: 8px;
    height: 13px;
    left: 0;
    top: 50%;
    background-image: url("../images/icon-arrow-left.svg");
    background-size: cover;
    transform: translate(-50%, -50%); }

.datepicker .next {
  text-align: right;
  position: relative;
  font-size: 0; }
  .datepicker .next:before {
    content: '';
    position: absolute;
    width: 8px;
    height: 13px;
    right: 0;
    top: 50%;
    background-image: url("../images/icon-arrow-right.svg");
    background-size: cover;
    transform: translate(-50%, -50%); }

.datepicker .dow {
  border-bottom: 1px solid #a7a8a9;
  padding: 13px;
  text-align: center; }

.datepicker .table-condensed > tbody > tr > td {
  padding: 13px;
  text-align: center; }

.datepicker .table-condensed {
  margin: 0 auto; }

.datepicker .day {
  /* stylelint-disable-line */ }
  .datepicker .day.old, .datepicker .day.new {
    font-size: 0;
    position: relative;
    pointer-events: none; }
    .datepicker .day.old:before, .datepicker .day.new:before {
      content: '-';
      font-size: 13px; }
  .datepicker .day.today {
    background-color: rgba(0, 157, 196, 0.05); }
  .datepicker .day.active {
    position: relative; }
    .datepicker .day.active:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 26px;
      height: 26px;
      border: 2px solid #53565a;
      border-radius: 50%;
      transform: translate(-50%, -50%); }
    .datepicker .day.active:after {
      content: '';
      position: absolute;
      width: 26px;
      height: 2px;
      background-color: #53565a;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(45deg); }
    .datepicker .day.active.old:before, .datepicker .day.active.old:after, .datepicker .day.active.new:before, .datepicker .day.active.new:after {
      display: none; }

.datepicker tfoot .today {
  opacity: 0;
  visibility: hidden;
  height: 0; }

.directer-offer {
  background-color: #707070;
  font-size: 13px;
  overflow-x: hidden; }
  @media (max-width: 768px) {
    .directer-offer .step {
      height: calc(100vh - 113px); } }
  .directer-offer .step form {
    max-width: 550px;
    margin: 0 auto; }
    @media (max-width: 768px) {
      .directer-offer .step form {
        max-width: 100%; } }
  .directer-offer .step-content {
    width: 100%;
    position: relative; }
    .directer-offer .step-content__item {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      visibility: hidden;
      display: none;
      background-color: #707070; }
      .directer-offer .step-content__item-content {
        padding: 30px 15px 100px; }
        @media (max-width: 768px) {
          .directer-offer .step-content__item-content {
            /* stylelint-disable-line */
            padding: 30px 15px 50px; } }
      .directer-offer .step-content__item.active {
        visibility: visible;
        position: static;
        display: block; }
  .directer-offer__upload-video {
    margin-top: 5px; }
    .directer-offer__upload-video label {
      font-size: 13px;
      background-color: #53565a; }
  .directer-offer__banner-title {
    background-color: #f8f8f8;
    font-size: 20px;
    color: #009ace;
    margin: 0;
    padding: 10px; }
  .directer-offer__banner-content {
    position: relative; }
    .directer-offer__banner-content .button {
      position: absolute;
      left: 50%;
      top: 50%;
      bottom: 50%;
      transform: translate(-50%, -50%);
      font-size: 20px;
      width: calc(100% - 76px); }
  .directer-offer .form-group > label {
    color: #fff; }
  .directer-offer .form-group .CaptionCont.SelectBox {
    color: #009ace; }
  .directer-offer .header-button--back {
    margin-bottom: 30px; }
  .directer-offer .composer-group-user {
    margin: 50px 0 30px;
    border-top: 1px solid #fff;
    padding-top: 10px;
    max-width: 526px;
    overflow: hidden; }
    @media (max-width: 768px) {
      .directer-offer .composer-group-user {
        max-width: 100%; } }
    .directer-offer .composer-group-user__wrap {
      overflow-x: auto;
      white-space: nowrap;
      width: 100%; }
    .directer-offer .composer-group-user__item {
      max-width: 100px;
      text-align: center;
      margin-right: 12px;
      display: inline-block; }
      .directer-offer .composer-group-user__item:last-child {
        margin-right: 0; }
      .directer-offer .composer-group-user__item.active img {
        border: 4px solid #31c8ff; }
    .directer-offer .composer-group-user__title {
      color: #fff;
      margin-bottom: 15px; }
    .directer-offer .composer-group-user__avatar {
      display: block; }
      .directer-offer .composer-group-user__avatar-img {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        margin-bottom: 10px;
        border: 4px solid transparent; }
    .directer-offer .composer-group-user__name {
      display: block;
      color: #000;
      margin-bottom: 5px; }
    .directer-offer .composer-group-user__day {
      color: #fff; }
    .directer-offer .composer-group-user__price {
      color: #009ace;
      position: relative;
      height: 20px; }
      .directer-offer .composer-group-user__price-noti {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        color: #fff;
        background-color: #009ace;
        display: inline-block;
        line-height: 20px;
        margin-left: 8px; }
    .directer-offer .composer-group-user__like {
      color: #fff; }
      .directer-offer .composer-group-user__like .icon-font {
        margin-right: 5px; }
  .directer-offer__group-button-bottom {
    position: fixed;
    bottom: 0;
    background-color: #fff;
    text-align: center;
    padding: 5px 30px;
    width: 100%;
    left: 0; }
    .directer-offer__group-button-bottom .button {
      max-width: 550px; }
  .directer-offer .select-demonstration-period {
    display: none; }
    .directer-offer .select-demonstration-period .close {
      display: none;
      font-size: 17px;
      line-height: 1;
      color: #fff;
      text-shadow: none;
      opacity: 1;
      position: relative;
      top: -5px;
      padding-right: 5px; }
      .directer-offer .select-demonstration-period .close:hover {
        opacity: .5; }
    .directer-offer .select-demonstration-period .date-last .close {
      display: block; }
    .directer-offer .select-demonstration-period .input-group.date {
      margin-bottom: 15px; }
  .directer-offer .button--gradient-primary {
    font-size: 20px; }
    .directer-offer .button--gradient-primary.disable-click {
      pointer-events: none;
      background-color: #d0d0d0;
      background-image: none;
      opacity: .7; }
  .directer-offer .info-composer {
    margin-bottom: 30px;
    width: 50%;
    float: left; }
    .directer-offer .info-composer__avatar {
      max-width: 90px;
      display: block;
      text-align: center; }
      .directer-offer .info-composer__avatar-img {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        margin-bottom: 10px; }
    .directer-offer .info-composer__name {
      color: #000;
      display: block; }
    .directer-offer .info-composer__like .icon-font {
      margin-right: 5px; }
    .directer-offer .info-composer--right {
      color: #fff;
      padding-top: 15px; }
  .directer-offer__polyci {
    margin-bottom: 50px;
    clear: both; }
    .directer-offer__polyci-title {
      color: #009ace;
      margin-bottom: 10px; }
    .directer-offer__polyci-info {
      background-color: #fff;
      padding: 10px 20px;
      line-height: 22px; }
  .directer-offer .edit-offer {
    background-color: #f8f8f8;
    padding: 30px 30px;
    color: #53565a; }
    .directer-offer .edit-offer__edit-price-icon {
      cursor: pointer;
      margin-left: 5px;
      font-size: 13px;
      color: #000; }
      .directer-offer .edit-offer__edit-price-icon--edit {
        opacity: 0; }
      .directer-offer .edit-offer__edit-price-icon--save, .directer-offer .edit-offer__edit-price-icon--cancel {
        display: none;
        margin-left: 15px; }
      .directer-offer .edit-offer__edit-price-icon .show {
        display: block; }
    .directer-offer .edit-offer__form-edit-price span,
    .directer-offer .edit-offer__form-edit-price label,
    .directer-offer .edit-offer__form-edit-price input {
      float: left; }
    .directer-offer .edit-offer__form-edit-price:hover .edit-offer__edit-price-icon--edit {
      opacity: 1; }
    .directer-offer .edit-offer__form-edit-music > span, .directer-offer .edit-offer__form-edit-date > span {
      float: left; }
    .directer-offer .edit-offer__info {
      margin-bottom: 10px;
      display: inline-block; }
      .directer-offer .edit-offer__info .form-group {
        margin-bottom: 0;
        clear: both; }
        .directer-offer .edit-offer__info .form-group > label {
          color: #000; }
      .directer-offer .edit-offer__info .form-control {
        width: auto;
        display: inline-block;
        font-size: 13px;
        color: #53565a;
        border: 0;
        box-shadow: none;
        background-color: #f8f8f8;
        position: relative;
        top: -6px; }
      .directer-offer .edit-offer__info .select-edit-music {
        margin-bottom: 0;
        clear: none;
        float: left;
        position: relative;
        top: -4px; }
        .directer-offer .edit-offer__info .select-edit-music .CaptionCont {
          border: 0;
          color: #53565a;
          background-color: #f8f8f8; }
    .directer-offer .edit-offer__edit-price {
      max-width: 80px; }
    .directer-offer .edit-offer .forcus-edit {
      border: 1px solid #b6b5b6;
      border-radius: 4px; }
    .directer-offer .edit-offer .input-group.date {
      z-index: 0;
      float: left; }
      .directer-offer .edit-offer .input-group.date .input-group-addon {
        position: absolute;
        top: -5px;
        right: 20px;
        border: 0;
        z-index: 9;
        background-color: #f8f8f8; }
    .directer-offer .edit-offer__general {
      overflow: hidden;
      clear: both; }
      .directer-offer .edit-offer__general-wrap {
        width: 33.33%;
        float: left; }
      .directer-offer .edit-offer__general-image {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #fff;
        margin: 0 auto 7px; }
      .directer-offer .edit-offer__general-content {
        color: #000; }
      .directer-offer .edit-offer__general-icon {
        width: 30px;
        height: 30px;
        margin: 0 auto;
        position: relative;
        top: 10px; }
        .directer-offer .edit-offer__general-icon--coppyright {
          background-image: url("/images/icon-coppry-right.png");
          cursor: pointer;
          transition: .3s; }
        .directer-offer .edit-offer__general-icon.change-icon {
          background-image: url("/images/icon-coppry-right-2.png");
          transform: rotateX(180deg);
          transition: .3s; }
        .directer-offer .edit-offer__general-icon--name {
          width: 25px;
          height: 30px;
          background-image: url("/images/icon-display-name.png"); }
        .directer-offer .edit-offer__general-icon--results {
          background-image: url("/images/icon-result.png"); }

.datepicker-orient-left .day.active:before {
  border: 2px solid #009ace; }

.datepicker-orient-left .day.active:after {
  display: none; }

.datepicker-orient-left .prev:before {
  left: 8px; }

@keyframes slideInRight {
  from {
    margin-left: 100%;
    visibility: visible; }
  to {
    margin-left: 0; } }

.slideInRight {
  animation-name: slideInRight; }

@keyframes slideInLeft {
  from {
    margin-left: -100%;
    visibility: visible; }
  to {
    margin-left: 0; } }

.slideInLeft {
  animation-name: slideInLeft; }

.animated {
  animation-duration: .5s;
  animation-fill-mode: both; }

.dashboard__content {
  padding: 55px 0 30px;
  border-bottom: 1px solid #a7a8a9; }

.dashboard__title {
  font-size: 20px;
  color: #000;
  border-bottom: 1px solid #a7a8a9;
  padding-bottom: 15px;
  margin-bottom: 40px; }
  @media (max-width: 768px) {
    .dashboard__title {
      font-size: 16px;
      margin-bottom: 10px;
      padding-bottom: 7px; } }

.dashboard__banner {
  background-color: #a7a8a9;
  height: 752px;
  position: relative; }
  .dashboard__banner-content {
    position: absolute;
    top: 50%;
    transform: translateY(-50%); }
    @media (max-width: 768px) {
      .dashboard__banner-content ul {
        padding-left: 15px; } }
    .dashboard__banner-content li {
      color: #000;
      font-size: 30px;
      margin-bottom: 23px;
      list-style-type: none; }
      .dashboard__banner-content li:last-child {
        margin-bottom: 0; }
      @media (max-width: 768px) {
        .dashboard__banner-content li {
          font-size: 45px;
          color: #fff; } }
  .dashboard__banner-button {
    position: absolute;
    left: 50%;
    bottom: 65px;
    transform: translateX(-50%);
    width: 281px; }
    .dashboard__banner-button .button {
      font-size: 20px;
      padding: 0 50px; }

.dashboard .case-study {
  margin-bottom: 147px; }
  @media (max-width: 768px) {
    .dashboard .case-study {
      margin-bottom: 40px; } }
  .dashboard .case-study__item {
    text-align: center;
    height: 182px;
    position: relative;
    margin-bottom: 20px; }
    @media (max-width: 576px) {
      .dashboard .case-study__item {
        height: 65px; } }
    .dashboard .case-study__item-info {
      width: 100%;
      height: 100%; }
      .dashboard .case-study__item-info--orange {
        background-image: linear-gradient(50deg, #f0ea0c, #c45300); }
      .dashboard .case-study__item-info--blue {
        background-image: linear-gradient(1deg, #4cd3de, #1f6dff); }
      .dashboard .case-study__item-info--cyan {
        background-image: linear-gradient(1deg, #1e9864, #27d4ff); }
      .dashboard .case-study__item-info--carrot {
        background-color: #ff9271; }
      .dashboard .case-study__item-info--red {
        background-image: linear-gradient(1deg, #793939, #ff0032); }
      .dashboard .case-study__item-info--green {
        background-color: #a5e5a0; }
      .dashboard .case-study__item-info--pink {
        background-image: linear-gradient(1deg, #ed89ce, #ffdbf3); }
      .dashboard .case-study__item-info--black {
        background-color: #000; }
      .dashboard .case-study__item-info--yellow {
        background-image: linear-gradient(1deg, #ebff00, #a7a8a9); }
      .dashboard .case-study__item-info a {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
        right: 10px;
        color: #fcfcfc;
        text-transform: uppercase;
        font-size: 20px; }
        .dashboard .case-study__item-info a.text-red {
          color: #e6002d; }
        @media (max-width: 576px) {
          .dashboard .case-study__item-info a {
            left: 0;
            right: 0;
            font-size: 12px; } }

@media (max-width: 992px) {
  .dashboard .introduction {
    margin-bottom: 60px; } }

@media screen and (min-width: 1270px) {
  .dashboard .introduction__video--right {
    margin-right: -50px; }
  .dashboard .introduction__video--left {
    margin-left: -50px; } }

@media (max-width: 768px) {
  .dashboard .introduction__video video {
    margin-bottom: 10px; } }

.dashboard .introduction__video video:focus {
  outline: none; }

@media (max-width: 768px) {
  .dashboard .introduction__video .button {
    font-size: 13px;
    height: 22px;
    line-height: 23px; } }

.dashboard .introduction__item {
  margin-bottom: 130px; }
  @media (max-width: 768px) {
    .dashboard .introduction__item {
      margin-bottom: 40px; } }

.dashboard .introduction__content-right {
  padding-left: 80px;
  float: right; }
  @media (max-width: 992px) {
    .dashboard .introduction__content-right {
      padding-left: 15px; } }
  @media (max-width: 576px) {
    .dashboard .introduction__content-right {
      float: none; } }

.dashboard .introduction__title {
  font-size: 40px;
  color: #000;
  margin-bottom: 22px; }
  @media (max-width: 992px) {
    .dashboard .introduction__title {
      font-size: 24px; } }
  @media (max-width: 768px) {
    .dashboard .introduction__title {
      text-align: center; } }

.dashboard .introduction__info {
  color: #53565a;
  font-size: 20px;
  margin-bottom: 35px; }
  @media (max-width: 992px) {
    .dashboard .introduction__info {
      font-size: 16px; } }
  @media (max-width: 768px) {
    .dashboard .introduction__info {
      text-align: center; } }
  .dashboard .introduction__info p {
    margin-bottom: 5px; }

.dashboard .faq .panel-title a {
  display: block;
  position: relative;
  padding: 20px 40px 20px 20px; }
  @media (max-width: 768px) {
    .dashboard .faq .panel-title a {
      padding: 10px 10px 10px 5px; } }

@media (max-width: 768px) {
  .dashboard .faq .panel-title {
    font-size: 12px; } }

.dashboard .faq a[aria-expanded='false']:after {
  position: absolute;
  top: 50%;
  right: 20px;
  content: '＋';
  font-size: 16px;
  color: #000;
  transform: translateY(-50%); }
  @media (max-width: 768px) {
    .dashboard .faq a[aria-expanded='false']:after {
      right: 5px; } }

.dashboard .faq a[aria-expanded='true']:after {
  position: absolute;
  top: 50%;
  right: 20px;
  content: '';
  width: 11px;
  height: 1px;
  background-color: #000;
  display: inline-block;
  transform: translateY(-50%); }
  @media (max-width: 768px) {
    .dashboard .faq a[aria-expanded='true']:after {
      right: 5px; } }

.dashboard .faq .panel-default {
  border: 0; }
  .dashboard .faq .panel-default > .panel-heading {
    background-color: #f8f8f8;
    border: 0;
    padding: 0; }

.dashboard .faq .panel-collapse > .panel-body {
  border: 0; }

.dashboard .panel-group .panel + .panel {
  margin-top: 15px; }

.sale-content {
  font-size: 13px; }
  .sale-content__title {
    display: flex; }
  .sale-content__page-title {
    margin: 30px 0; }
    @media (max-width: 768px) {
      .sale-content__page-title {
        color: #fff;
        background-image: linear-gradient(to right, #a7a8a9, #333);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        margin-left: -15px;
        display: inline-block;
        min-width: 125px;
        padding: 1px 15px; } }
  .sale-content__cover {
    margin: 0 15vw; }
    @media (max-width: 768px) {
      .sale-content__cover {
        margin: 0 -15px; } }
  .sale-content__cover-image {
    padding: 0 0 100%;
    background-size: cover;
    background-position: center center;
    position: relative; }
    .sale-content__cover-image .button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%); }
  .sale-content__button {
    margin-left: auto; }
  .sale-content__section-title {
    font-size: 20px;
    color: #000;
    margin-bottom: 35px; }
    @media (max-width: 768px) {
      .sale-content__section-title {
        font-size: 13px;
        color: #fff;
        background-image: linear-gradient(to right, #a7a8a9, #333);
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        margin-left: -15px;
        display: inline-block;
        min-width: 125px;
        padding: 1px 15px;
        margin-bottom: 25px; } }
  .sale-content__original {
    padding-top: 25px; }
    .sale-content__original-title {
      font-size: 20px;
      color: #000;
      margin-bottom: 35px; }
    .sale-content__original-list {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -7.5px; }
      @media (max-width: 768px) {
        .sale-content__original-list {
          margin: 0 -15px; } }
    .sale-content__original-add {
      margin-bottom: 30px;
      text-align: center; }
      @media (max-width: 768px) {
        .sale-content__original-add {
          margin-top: 15px; } }
  .sale-content .original-item__info-top {
    display: flex;
    justify-content: space-between; }
  .sale-content .original-item__status {
    height: 24px;
    line-height: 24px;
    border: 1px solid #009ace;
    color: #009ace;
    padding: 0 15px; }
  .sale-content .original-item__button {
    padding-top: 45px; }
    @media (max-width: 768px) {
      .sale-content .original-item__button {
        padding-top: 15px; } }
    .sale-content .original-item__button .button--text {
      display: block;
      margin-top: 10px; }
  .sale-content .audio-control {
    min-width: 102px; }
    .sale-content .audio-control__prev, .sale-content .audio-control__next {
      width: 12px;
      height: 19px; }
    .sale-content .audio-control__playpause {
      margin: 0 30px;
      width: 17px;
      height: 19px; }
  .sale-content .audio-content .audio-remain {
    top: -10px; }
  .sale-content .audio-content .button {
    bottom: -20px;
    color: #000;
    position: absolute;
    top: auto;
    right: 0; }
  .sale-content__info {
    margin: 30px 0;
    padding: 0 15vw; }
    @media (max-width: 768px) {
      .sale-content__info {
        padding: 0; } }
  .sale-content__price-row {
    display: flex;
    margin-bottom: 10px; }
    .sale-content__price-row .form-group {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px; }
    .sale-content__price-row .SumoSelect {
      width: 50%;
      max-width: 155px;
      margin-left: auto;
      text-align: right; }
  .sale-content__commission-value, .sale-content__profit-value {
    width: 50%;
    margin-left: auto;
    padding-right: 39px;
    text-align: right; }
  .sale-content__description {
    margin-top: 50px; }
    .sale-content__description .form-control {
      min-height: 180px; }
  .sale-content__file {
    background-color: #fcfcfc;
    text-align: center;
    padding: 38px 0;
    margin-bottom: 60px; }
    .sale-content__file-select {
      display: none !important;
      /* stylelint-disable-line */ }
  .sale-content__audio-player {
    display: none; }
    .sale-content__audio-player.active {
      display: block; }
  .sale-content__policy {
    text-align: center; }
    .sale-content__policy .input-checkbox {
      display: inline-block; }
  .sale-content__file-list {
    margin: 10px 15vw 0 15vw; }
    @media (max-width: 768px) {
      .sale-content__file-list {
        margin: 10px 0 0; } }
  .sale-content__file-preview {
    margin-bottom: 5px;
    background-color: #fcfcfc;
    padding: 5px 0;
    display: flex; }
  .sale-content__preview-image {
    width: 30%; }
    .sale-content__preview-image-bg {
      padding-bottom: 100%;
      background-size: cover;
      background-position: center center;
      background-repeat: no-repeat; }
  .sale-content__preview-content {
    margin-left: 10px;
    -ms-grid-row-align: center;
        align-self: center; }
  .sale-content__preview-title {
    color: #000;
    font-size: 16px;
    margin-bottom: 3px; }
  .sale-content__preview-time {
    color: #009ace; }
  .sale-content__action {
    margin: 45px 0 20px;
    text-align: center; }
    .sale-content__action .button--gradient {
      font-size: 20px;
      min-width: 325px;
      margin-bottom: 35px;
      text-transform: uppercase; }

.join-form__page-title {
  font-size: 16px;
  margin: 30px 0; }
  @media (max-width: 768px) {
    .join-form__page-title {
      color: #fff;
      background-image: linear-gradient(to right, #a7a8a9, #333);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      margin-left: -15px;
      display: inline-block;
      min-width: 125px;
      padding: 1px 15px;
      font-size: 13px; } }

.join-form__intro {
  font-size: 16px; }
  .join-form__intro-title {
    color: #009ace;
    padding-top: 10px; }

.join-form__action {
  margin: 45px 0 20px;
  text-align: center; }
  .join-form__action .button {
    font-size: 20px;
    min-width: 325px;
    text-transform: uppercase; }
    @media (max-width: 768px) {
      .join-form__action .button {
        width: 100%;
        min-width: auto; } }

.join-form__form-group {
  padding: 0 15px; }

.join-form__role-title {
  margin-bottom: 30px;
  text-align: center;
  font-size: 16px; }

.join-form__role-item-title {
  background-color: #fcfcfc;
  font-size: 16px;
  padding: 25px 0;
  text-align: center;
  transition: all .3s; }
  .join-form__role-item-title:hover {
    background-color: #009ace;
    color: #fff;
    transition: all .3s; }

.join-form__role-item-info {
  background-color: rgba(0, 157, 196, 0.05);
  padding: 10px 25px; }

.join-form__role-list {
  padding: 0 15vw; }
  @media (max-width: 768px) {
    .join-form__role-list {
      padding: 0; } }

.join-form__role-item {
  margin-bottom: 15px; }
  .join-form__role-item:last-child {
    margin-bottom: 0; }
  .join-form__role-item.active .join-form__role-item-title {
    background-color: #009ace;
    color: #fff;
    transition: all .3s; }

.join-form__steps {
  text-align: center; }

.join-form__step {
  border: 1px solid #009ace;
  color: #009ace;
  display: inline-block;
  border-radius: 50%;
  line-height: 25px;
  margin: 0 15px;
  width: 25px;
  height: 25px; }
  .join-form__step:hover {
    color: #009ace; }
  .join-form__step:first-child {
    margin-left: 0; }
  .join-form__step:last-child {
    margin-right: 0; }
  .join-form__step.current {
    background-color: #009ace;
    color: #fff; }
  .join-form__step:not(.complete) {
    pointer-events: none; }

.join-form__step-title {
  color: #009ace;
  margin-top: 8px; }

.join-form__social-login {
  margin-top: 35px;
  text-align: center; }

.join-form__social-list {
  margin: 25px 0 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #a7a8a9; }

.join-form__social-network {
  display: inline-block;
  border-radius: 50%;
  background-size: cover;
  background-position: center center;
  margin: 0 15px;
  width: 45px;
  height: 45px; }
  .join-form__social-network:first-child {
    margin-left: 0; }
  .join-form__social-network:last-child {
    margin-right: 0; }
  .join-form__social-network.apple {
    background-image: url("../images/social-apple.svg"); }
  .join-form__social-network.google {
    background-image: url("../images/social-google.svg"); }
  .join-form__social-network.facebook {
    background-image: url("../images/social-facebook.svg"); }

.join-form__input-form {
  padding: 0 15vw;
  margin-top: 30px; }
  @media (max-width: 768px) {
    .join-form__input-form {
      padding: 0; } }
  .join-form__input-form .SumoSelect > .CaptionCont > span {
    text-align: center; }

.join-form .textarea-large .form-control {
  min-height: 140px; }

.join-form__sub-title {
  margin-bottom: 15px;
  border-top: 1px solid #a7a8a9;
  padding-top: 10px; }

.upload {
  font-size: 13px; }
  .upload__file {
    background-color: #fcfcfc;
    padding: 35px 0;
    text-align: center; }
    .upload__file-input {
      display: none !important;
      /* stylelint-disable-line */ }
    .upload__file .button {
      height: 30px;
      line-height: 30px; }
  .upload__audio {
    background-color: #fcfcfc;
    margin-top: 30px;
    padding-top: 20px; }
    .upload__audio-player {
      padding-bottom: 30px;
      display: none; }
      .upload__audio-player.active {
        display: block; }
  .upload__file-info {
    display: flex;
    justify-content: space-between; }
  .upload__form {
    padding: 0 15vw; }
    @media (max-width: 768px) {
      .upload__form {
        padding: 0; } }
  .upload__slider-item {
    margin-bottom: 30px; }
  .upload__slider-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px; }
  .upload__slider-content {
    display: flex;
    align-items: center; }
  .upload__slider-min, .upload__slider-max {
    color: #009ace;
    width: 45px; }
  .upload__slider-max {
    text-align: right; }
  .upload__slider-bar {
    width: calc(100% - 90px); }
  .upload .noUi-target {
    background: #a7a8a9;
    border: none;
    box-shadow: none;
    height: 5px;
    border-radius: 4px; }
  .upload .noUi-handle {
    border: 3px solid #009ace;
    box-shadow: none;
    border-radius: 50%;
    width: 18px;
    height: 18px; }
    .upload .noUi-handle:before, .upload .noUi-handle:after {
      display: none; }
    .upload .noUi-handle:focus {
      outline: none; }
  .upload .noUi-connect {
    background: #009ace; }
  .upload__action {
    margin: 45px 0 20px;
    text-align: center; }
    .upload__action .button--gradient {
      font-size: 20px;
      min-width: 325px;
      margin-bottom: 35px;
      text-transform: uppercase; }

.player-component {
  padding-top: 30px; }

.audio-player-control-playpause {
  font-size: 0;
  width: 25px;
  height: 25px;
  background-image: url("../images/icon-play-circle2.svg");
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  z-index: 10;}
  .audio-player-control-playpause:hover {
    cursor: pointer; }
  .audio-player-control-playpause.active {
    background-image: url("../images/icon-pause-circle.svg"); }
.audio-player-control-playpause.gray {
  background-image: url("../images/icon-play-circle2-gray.svg");
}
.audio-player-control-playpause.gray.active {
  background-image: url("../images/icon-pause-circle-gray.svg");
}

.audio-player-component {
  background-color: #fcfcfc;
  margin-bottom: 5px;
  padding: 10px 0 0; }
  .audio-player-component .button-setting {
    margin-left: auto; }
  .audio-player-component .button-change,
  .audio-player-component .button-delete {
    opacity: 0;
    visibility: hidden;
    transition: all .3s; }
  .audio-player-component .button-change {
    margin-right: 15px; }
    .audio-player-component .button-change:hover {
      color: #009ace; }
  .audio-player-component .button-delete:hover {
    color: #e6002d; }
  .audio-player-component:hover .button-change,
  .audio-player-component:hover .button-delete {
    opacity: 1;
    visibility: visible;
    transition: all .3s; }
  .audio-player-component.editing:hover .button-change,
  .audio-player-component.editing:hover .button-delete {
    opacity: 0;
    visibility: hidden; }

.audio-player-content {
  padding: 0 10px; }

.audio-player-controls {
  display: flex;
  align-items: center; }

.audio-player-title {
  padding-left: 8px; }

.audio-player-time {
  display: flex;
  margin-left: auto;
  align-items: center; }

.audio-player-duration {
  padding-left: 15px;
  margin-left: 20px;
  border-left: 1px solid #a7a8a9;
  line-height: 1; }

.audio-player-waveform {
  background-color: #fff;
  border: 1px solid #a7a8a9;
  margin: 6px 0; }

.audio-player-action {
  display: flex;
  padding: 0 10px; }

.audio-player-edit {
  margin-top: -25px;
  display: none; }

.audio-player-edit-action {
  padding: 0 10px; }
  .audio-player-edit-action .button-save {
    margin-right: 15px; }

.audio-player-edit-info {
  margin-top: 5px;
  padding: 15px 25px 25px 25px;
  text-align: center;
  position: relative; }
  .audio-player-edit-info .form-group {
    margin-bottom: 0;
    text-align: left; }
  .audio-player-edit-info label {
    color: #707070; }
  .audio-player-edit-info:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.83);
    opacity: 0;
    visibility: hidden; }
  .audio-player-edit-info .saving-progress {
    transform: rotate(-90deg) translate(55%, -50%);
    position: absolute;
    top: 50%;
    left: 50%;
    opacity: 0;
    visibility: hidden; }
  .audio-player-edit-info .saving-progress__value {
    stroke-dasharray: 213.52; }
  .audio-player-edit-info.saving:before {
    opacity: 1;
    visibility: visible; }
  .audio-player-edit-info.saving .saving-progress {
    opacity: 1;
    visibility: visible; }

.audio-player-file {
  padding: 8px 0;
  color: #009ace; }

.audio-player-file-input {
  display: none !important;
  /* stylelint-disable-line */ }

.audio-player-add {
  background-color: #fcfcfc;
  padding: 0;
  text-align: center;
  height: 0;
  visibility: hidden; }
  @media (max-width: 768px) {
    .audio-player-add {
      margin: 0 -15px; } }
  .audio-player-add.active {
    padding: 45px 0;
    height: auto;
    visibility: visible; }

.audio-player-collapse-button {
  background-image: url("../images/icon-arrow-up.svg");
  width: 24px;
  height: 24px;
  margin: 15px auto; }
  .audio-player-collapse-button.active {
    background-image: url("../images/icon-arrow-down.svg"); }
  .audio-player-collapse-button:hover {
    cursor: pointer; }

.audio-player-bullets {
  width: 183px;
  height: 0;
  visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between; }
  .audio-player-bullets.active {
    margin: -32px auto 0 auto;
    height: auto;
    visibility: visible; }

.audio-player-bullet-list {
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  padding: 1px 0; }

.audio-player-bullet {
  width: 5px;
  height: 5px;
  background-color: #a7a8a9;
  border-radius: 50%;
  margin: 0 5px;
  flex: 0 0 auto; }
  .audio-player-bullet:hover {
    cursor: pointer; }
  .audio-player-bullet.active {
    width: 7px;
    height: 7px;
    background-color: #009ace;
    margin-top: -1px; }

.audio-player-bullet-prev {
  background-image: url("../images/icon-arrow-prev.svg"); }

.audio-player-bullet-next {
  background-image: url("../images/icon-arrow-next.svg"); }

.audio-player-bullet-prev,
.audio-player-bullet-next {
  flex: 0 0 24px;
  height: 24px;
  background-size: cover; }
  .audio-player-bullet-prev.disable,
  .audio-player-bullet-next.disable {
    pointer-events: none;
    opacity: .5; }
  .audio-player-bullet-prev:hover,
  .audio-player-bullet-next:hover {
    cursor: pointer; }

.audio-player-add-input {
  display: none !important;
  /* stylelint-disable-line */ }

.audio-player-delete-wrap {
  background-color: #fcfcfc;
  border-top: 1px solid #a7a8a9;
  position: fixed;
  bottom: 0;
  transform: translateY(100%);
  width: 100%;
  z-index: 99;
  transition: all .3s; }
  .audio-player-delete-wrap.active {
    transform: translateY(0);
    transition: all .3s; }

.audio-player-delete {
  display: flex;
  height: 54px;
  align-items: center; }
  .audio-player-delete .audio-player-delete-title {
    color: #009ace; }
  .audio-player-delete .audio-player-undo {
    margin-left: auto; }

@media (max-width: 768px) {
  .audio-player-wrap {
    margin: 0 -15px; } }

.audio-player-wrap.active .audio-player-component {
  height: 0;
  visibility: hidden; }
  .audio-player-wrap.active .audio-player-component:not(.active) {
    padding: 0;
    margin-bottom: 0; }
  .audio-player-wrap.active .audio-player-component.active {
    height: auto;
    visibility: visible; }

.audio-player-wrap.active .audio-player-edit-info {
  margin-top: 5px; }


.project-tab {
  display: none; }
  .project-tab.active {
    display: block; }

.project-item {
  position: relative;
  margin-bottom: 14px;}
  .project-item__info {
    background-size: 100% auto;
    background-position: top center;
    border-radius: 4px;
    padding: 0 10px;
    position: absolute;
    width: 100%;
    height: 200%;
    top: 0;
    left: 0;
  }
  .project-item__title {
    color: #009ace; }
    .project-item__title:before {
      content: '▶︎'; }
    .project-item__title:hover {
      cursor: pointer; }
    .project-item.active .project-item__title:before {
      content: '▼'; }
  .project-item__member {
    margin-left: auto;
    display: flex;
    height: auto; }
  .project-item__member-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center}
  .project-item__member-item {
    width: 20px;
    height: 20px; }
    .project-item__member-item:not(:first-child) {
      margin-left: 0; }
    .project-item__member-item img {
      max-width: 20px; }
  .project-item__member-add {
    margin-left: 0; }

  .project-item__member-add .button--round {
    display: flex;
    align-items: center;
    justify-content: center; }
  .project-item__content {
    display: none;}

  .project-item__top {
    display: flex;
    align-items: center;
    height: 50px; }
  .project-item__filter {
    display: flex;
      z-index: 8; }

  .project-item__filter-item {
    color: #009ace;
    height: 30px;
    line-height: 30px;
    padding: 0 30px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;}

  .project-item__filter-item.gray svg path {
    fill: #a7a8a9;
  }

  .project-item__filter-item.deepgray svg path {
    fill: #53565a;
  }


  .project-item__filter-item svg path {
    fill: #009ace;
  }
    .project-item__search {
      margin: 5px 0 !important;}
    .project-item__filter-item.active svg path {
      fill: #0076a5;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);}
    .project-item__filter-item:hover svg path {
      fill: #0076a5;
      cursor: pointer;}
  .project-item__setting {
    margin: 0 30px 0 auto; }

  .project-item__search {
    background-image: url(../images/icon_search.svg);
    width: 30px;
    height: 30px;
    background-size: 20px 20px;
    color: gray;
    background-repeat: no-repeat;
    margin: 5px 20px;
    filter: invert(32%) sepia(15%) saturate(151%) hue-rotate(175deg) brightness(95%) contrast(90%);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 50px;
    background-position: 5px;
  }
  .project-item__search:hover {
    filter: invert(42%) sepia(92%) saturate(1717%) hue-rotate(165deg) brightness(92%) contrast(101%);
  }

  .project-item__search.active {
    width: 230px;
  }
  .project-item__search input {
    background: none;
    outline: none;
    border: none;
    position: absolute;
    right: 5px;
    top: 5px;
    width: 190px;
    opacity: 0;
    transition: .2s;
    transition-delay: .4s;
  }

  .project-item__search.active input {
    opacity: 1;
  }

  .project-item__right {
    display: flex;
    margin-left: auto;
    align-items: center; }
  .project-item__video-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px; }
  .project-item.active .project-item__info {
    margin-bottom: 0; }
  .project-item.active .project-item__content {
    display: block; }
    .project-item__general {
    position: relative;
    padding-top: 10%;
    z-index: 12;}
    @media (max-width: 576px) {
      .project-item__general {
        padding-top: 10%; } }
  .project-item__info-top {
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding: 0 10px;
    z-index: 13; }
  .project-item__bar {
    position: absolute;
    left: 10px;
    bottom: -10px;
    width: 40%;
    z-index: 11;}
  /*.project-item:not(.active) {*/
  /*  margin-bottom: 12%;}*/
  .project-item__general .project-item__info-bottom {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 100%;
    left: 0;
    padding: 0 10px;
    z-index: 10;}
    @media (max-width: 576px) {
      .project-item__bar {
        width: calc(100% - 20px);
        bottom: -15px;
      }

      .project-item:not(.active) {
        margin-bottom: 10px;
      }

      .project-item.on-mobile .project-item__info {
        height: 400%;
        background-size: auto 100%;
        background-position: left;
        background-repeat: no-repeat;
      }

      .project-item__general .project-item__info-bottom {
        top: 300%;
      }
  }


  .project-item__more {
    display: flex;
    padding: 10px;
    position: absolute;
    width: 100%;
    height: 100%;
    bottom: 0;
    left: 0;
    align-items: flex-end; }
    @media (max-width: 576px) {
      .project-item__more {
        display: block;
        padding: 5px 10px;} }
  .project-item__more-left {
    flex: 0 0 40%; }
    @media (max-width: 576px) {
      .project-item__more-left {
        flex: 0 0 100%; } }
  .project-item__more-center {
    padding: 0 40px; }
    @media (max-width: 576px) {
      .project-item__more-center {
        padding: 20px 0 10px; } }
  .project-item__more-right {
    flex: 0 0 150px;
    text-align: right;
    margin-left: auto;}
    @media (max-width: 576px) {
      .project-item__more-right {
        flex: none;
        display: flex;
        justify-content: space-between;
        align-items: end;
        margin-right: auto;
        margin-bottom: -10px;
        margin-left: 0;} }
    .project-item__more-right a {
      display: block;
      text-align: right; }
      @media (max-width: 576px) {
        .project-item__more-right a {
          display: inline-block; } }
  .project-item__category {
    padding: 0;
    margin: 0;
    list-style: none; }
    .project-item__category li {
      display: inline-block;
      color: #707070;
      text-transform: uppercase;
      margin: 0 10px; }
      .project-item__category li:first-child {
        margin-left: 0; }
      .project-item__category li:last-child {
        margin-right: 0; }
  .project-item__remind {
    color: #000;
    display: inline-block;
    background-color: #fff;
    border: 1px solid #d6d6d6;
    padding: 7px 30px;
    font-size: 10px; }
    .project-item__remind span {
      color: #009dc4; }
  .project-item__more-left-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px; }

  .project-item__more-left-top.active .project-item__progress-success,
  .project-item__more-left-top.active .project-item__progress-warning {
    opacity: 1;
  }

  .project-item__progress-percent {
    display: flex; }
    .project-item__progress-percent > div {
      line-height: 1; }
  .project-item__progress-success {
    color: #009dc4;
    opacity: 0;}
  .project-item__progress-warning {
    color: #707070;
    padding: 0 6px;
    margin: 0 6px;
    border-left: 1px solid #707070;
    border-right: 1px solid #707070;
    opacity: 0;}
  .project-item__progress-total {
    color: #d6d6d6; }
  .project-item__description {
    color: #000;
    margin-bottom: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /* autoprefixer: off */
    -webkit-box-orient: vertical;}
  .project-item__setting-btn {
    font-size: 20px;
    color: #838383;
    margin-top: 18px; }
    @media (max-width: 576px) {
      .project-item__setting-btn {
        margin-top: 0; } }
    .project-item__setting-btn:hover {
      color: #009dc4; }
  .project-item.hover-active {
    z-index: 9; }

.progressbar-new .progress {
height: 4px;
background-color: #d6d6d6;
border-radius: 0;
border: none; }
.progressbar-new .progress .progress-bar {
  height: 4px;
  margin-top: 0; }
.progressbar-new .progress .bg-success {
  background-color: #009dc4;
  border-radius: 0; }
.progressbar-new .progress .bg-warning {
  background-color: #53565a;
  border-radius: 0; }

.video-item-list {
  display: flex;
  flex-wrap: wrap; }

.video-item-chapter {
  position: absolute;
  top: 15px;
  font-size: 16px;
  color: #000;
  white-space: nowrap;
  max-width: 90%;
  overflow: hidden;
}

.video-item-variation {
  position: absolute;
  bottom: 15px;
  left: 20px;
  color: #fff;
  background: #53565a;
  border-radius: 50px;
  padding: 0 10px;
  height: 20px;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
}

.video-item-update-time {
  position: absolute;
  top: 10px;
  right: 20px;
  color: #fff; }

.video-item-edit {
  position: absolute;
  width: 30px;
  height: 30px;
  top: 15px;
  right: 15px;
  text-align: center;
  border-radius: 50%;
  z-index: 9;
  opacity: 0;
  visibility: hidden;
  transition: all .3s; }
  .video-item-edit:hover {
    background-color: rgba(58, 58, 58, 0.9);}
  .video-item-edit:before {
    content: '';
    display: block;
    width: 20px;
    height: 20px;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-mask-image: url(../images/icon-settings.svg);
    mask-image: url(../images/icon-settings.svg);
    background-size: contain;
    margin: 5px auto 0;
    background-position: center center;
  }

.video-item-share {
  position: absolute;
  width: 30px;
  height: 30px;
  bottom: 65px;
  right: 15px;
  text-align: center;
  border-radius: 50%;
  z-index: 9;
  opacity: 0;
  visibility: hidden;
  transition: all .3s; }
  .video-item-share:hover {
    background-color: rgba(58, 58, 58, 0.9);}
  .video-item-share:before {
    content: '';
    display: block;
    width: 13px;
    height: 13px;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-share-solid.svg");
    mask-image: url("../images/icon-share-solid.svg");
    background-size: cover;
    margin: 8px auto 0; }

.video-item-button-top {
  position: absolute;
  top: 5%;
  left: 50%;
  width: 80px;
  height: 80px;
  padding-top: 10px;
  transform: translateX(-50%);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  opacity: 0;
  z-index: 2;
  transition: .3s linear; }
  @media (max-width: 768px) {
    .video-item-button-top {
      width: 65px;
      height: 65px;
      top: 2%;
      padding-top: 5px; }
    .owner-top:not(.on-mobile) .video-item-button-top:hover, .video-item-button-top.show {
      top: 2% !important;}}
  .video-item-button-top:before {
    content: '';
    display: block;
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-image: url("../images/icon-check.svg");
    background-size: contain;
    background-position: center;
    filter: invert(93%) sepia(93%) saturate(28%) hue-rotate(20deg) brightness(107%) contrast(105%);
    margin: 0 auto 3px; }
  .video-item-button-top:hover, .video-item-button-top.show {
    top: 5%;
    opacity: 1;
    transition: .3s linear; }

.video-item-button-bottom {
  position: absolute;
  bottom: calc(50% - 80px);
  left: 50%;
  width: 125px;
  height: 125px;
  padding-top: 20px;
  transform: translateX(-50%);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  opacity: 0;
  z-index: 9;
  transition: .3s linear;
  display: flex !important;
  justify-content: center;
  flex-direction: column;
  }
  @media (max-width: 1199px) {
    .video-item-button-bottom {
      width: 105px;
      height: 105px;
      padding-top: 15px;
      bottom: calc(50% - 70px); } }
  .video-item-button-bottom:before {
    content: '';
    display: block;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-image: url("../images/icon-comment.svg");
    background-size: contain;
    background-position: center;
    filter: invert(93%) sepia(93%) saturate(28%) hue-rotate(20deg) brightness(107%) contrast(105%);}
  .owner-top:not(.on-mobile) .video-item-button-bottom:hover, .video-item-button-bottom.show {
    opacity: 1;
    transition: .3s linear; }

.video-item-button-right {
  position: absolute;
  right: 18%;
  top: 50%;
  width: 80px;
  height: 80px;
  padding-top: 20px;
  transform: translateY(-50%);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  opacity: 0;
  z-index: 2;
  transition: .3s linear;
  display: block !important;}
  @media (max-width: 1199px) {
    .video-item-button-right {
      width: 70px;
      height: 70px;
      padding-top: 15px; } }
  @media (max-width: 768px) {
    .video-item-button-right {
      right: 15%; } }
  .video-item-button-right:before {
    content: '';
    display: block;
    width: 20px;
    height: 17.65px;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-heart.svg");
    mask-image: url("../images/icon-heart.svg");
    background-size: cover;
    margin: 0 auto 3px; }
  .video-item-button-right:hover, .video-item-button-right.show {
    right: 18%;
    opacity: 1;
    visibility: visible;
    transition: .3s linear; }
    @media (max-width: 768px) {
      .owner-top:not(.on-mobile) .video-item-button-right:hover, .video-item-button-right.show {
        right: 15%; } }

.video-item-button-left {
  position: absolute;
  left: 18%;
  top: 50%;
  width: 80px;
  height: 80px;
  padding-top: 20px;
  transform: translateY(-50%);
  border-radius: 50%;
  color: #fff;
  text-align: center;
  opacity: 0;
  z-index: 2;
  transition: .3s linear;
  display: block !important;}
  @media (max-width: 1199px) {
    .video-item-button-left {
      width: 70px;
      height: 70px;
      padding-top: 15px; } }
  @media (max-width: 768px) {
    .video-item-button-left {
      left: 15%; } }
  .video-item-button-left:before {
    content: '';
    display: block;
    width: 15.93px;
    height: 19.3px;
    background-color: #fff;
    background-repeat: no-repeat;
    -webkit-mask-image: url("../images/icon-replay.svg");
    mask-image: url("../images/icon-replay.svg");
    background-size: cover;
    margin: 0 auto 3px; }
  .owner-top:not(.on-mobile) .video-item-button-left:hover, .video-item-button-left.show {
    left: 18%;
    opacity: 1;
    visibility: visible;
    transition: .3s linear; }
    @media (max-width: 768px) {
      .video-item-button-left:hover, .video-item-button-left.show {
        left: 15%; } }

@media (max-width: 576px) {
  .video-button {
    font-size: 12px;
    transition: .5s !important;} }

.video-button {
  background-color: rgba(58, 58, 58, 0.75);
}

.video-button:hover {
  cursor: pointer;
  background-color: rgba(58, 58, 58, 0.9); }


@media (max-width: 768px) {
  .on-mobile .video-item-button-top,
  .on-mobile .video-item-button-bottom,
  .on-mobile .video-item-button-left,
  .on-mobile .video-item-button-right {
    top: -100%;
  }

  .on-mobile .video-item-button-top.show,
  .on-mobile .video-item-button-bottom.show,
  .on-mobile .video-item-button-left.show,
  .on-mobile .video-item-button-right.show {
    opacity: 1;
    visibility: visible;
  }

  .on-mobile .video-item-button-top.show {
    top: 15% !important;
    left: 50%;
  }

  .on-mobile .video-item-button-bottom.show {
    bottom: 20%;
    left: 50%;
    right: auto;
    top: auto;;
  }

  .on-mobile .video-item-button-left.show {
    top: 50%;
    left: 15%;
    right: auto;
  }

  .on-mobile .video-item-button-right.show {
    top: 50%;
    right: 15%;
    left: auto
  }
}

.video-time-slider-item {
  margin-bottom: 10px; }
  .video-time-slider-item .video-time-slider-content {
    display: flex;
    align-items: center; }
  .video-time-slider-item .video-time-slider-start,
  .video-time-slider-item .video-time-slider-end {
    color: #009ace;
    width: 85px; }
  .video-time-slider-item .video-time-slider-end {
    text-align: right; }
    .video-time-slider-item .video-time-slider-end.disabled {
      color: #a7a8a9; }
  .video-time-slider-item .video-time-slider-label {
    display: flex;
    justify-content: space-between;
    color: #009ace;
    margin-top: 5px; }
    .video-time-slider-item .video-time-slider-label .disabled {
      color: #53565a; }
  .video-time-slider-item .video-time-slider-bar {
    width: calc(100% - 170px); }
  .video-time-slider-item .noUi-target {
    background: #a7a8a9;
    border: none;
    box-shadow: none;
    height: 5px;
    border-radius: 4px; }
  .video-time-slider-item .noUi-handle {
    border: 3px solid #009ace;
    box-shadow: none;
    border-radius: 50%;
    width: 18px;
    height: 18px; }
    .video-time-slider-item .noUi-handle:before, .video-time-slider-item .noUi-handle:after {
      display: none; }
    .video-time-slider-item .noUi-handle:focus {
      outline: none; }
  .video-time-slider-item .noUi-connect {
    background: #009ace; }

.video-item-component {
  display: none;
  width: 100%;
  position: relative;
  overflow: hidden; }
  .video-item-component .video-item-component-content-video {
    position: relative;
    overflow: hidden; }
  .video-item-component video {
    display: block;
    z-index: 2; }
  .video-item-component.active {
    display: block; }
  .video-item-component:hover .video-item-edit,
  .video-item-component:hover .video-item-share {
    opacity: 1;
    visibility: visible;
    transition: all .3s; }

.video-item-bullets-wrap {
  position: relative; }

.video-item-bullets {
  width: 183px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  height: 30px; }
  .video-item-bullets.active {
    margin: -32px auto 0 auto;
    height: auto;
    visibility: visible; }

.video-item-bullet-list {
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  padding: 1px 0;
  width: 183px;
  justify-content: center; }

.video-item-bullet {
  width: 5px;
  height: 5px;
  background-color: #a7a8a9;
  border-radius: 50%;
  margin: 0 5px;
  flex: 0 0 auto; }
  .video-item-bullet:hover {
    cursor: pointer; }
  .video-item-bullet.active {
    width: 7px;
    height: 7px;
    background-color: #009ace;
    margin-top: -1px; }

.video-item-bullet-prev {
  background-image: url("../images/icon-arrow-prev.svg");
  left: 0; }

.video-item-bullet-next {
  background-image: url("../images/icon-arrow-next.svg");
  right: 0; }

.video-item-bullet-prev,
.video-item-bullet-next {
  height: 24px;
  width: 24px;
  background-size: cover;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.71);
  border-radius: 50%;
  margin: 0 10px;
  top: -170px;
}
  .video-item-bullet-prev.disable,
  .video-item-bullet-next.disable {
    pointer-events: none;
    opacity: 0; }
  .video-item-bullet-prev:hover,
  .video-item-bullet-next:hover {
    cursor: pointer;
    transition: .2s;
    background-color: rgba(255, 255, 255, 1);}

.video-item-thumbnail-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -20px; }
  @media (max-width: 768px) {
    .video-item-thumbnail-list {
      margin: 0 -10px; } }
  @media (max-width: 576px) {
    .video-item-thumbnail-list {
      margin: 0 -3px; } }

.video-item-thumbnails {
  display: none;
  margin-top: 20px; }

.project-chapter-video-item-content {
  position: relative; }
  .project-chapter-video-item-content video {
    display: block; }

.video-item-thumbnail {
  width: 20%;
  padding: 20px; }
  @media (max-width: 768px) {
    .video-item-thumbnail {
      padding: 10px; } }
  @media (max-width: 576px) {
    .video-item-thumbnail {
      padding: 3px; } }
  .video-item-thumbnail video {
    width: 100%; }
  .video-item-thumbnail.active, .video-item-thumbnail:hover {
    cursor: pointer; }
    .video-item-thumbnail.active video, .video-item-thumbnail:hover video {
      border: 2px solid #009ace; }

.video-item-collapse-button {
  background-image: url("../images/icon-arrow-down.svg");
  width: 24px;
  height: 24px;
  margin: 15px auto; }
  .video-item-collapse-button.active {
    background-image: url("../images/icon-arrow-up.svg"); }
  .video-item-collapse-button:hover {
    cursor: pointer; }

.pin-enable {
  border: 3px solid #a7a8a9;
  box-shadow: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  position: absolute;
  right: -17px;
  top: -6px;
  z-index: 9;
  overflow: hidden; }

.share-modal .modal-dialog {
  width: 500px; }

.share-modal .modal-header {
  padding: 32px 16px 32px 16px;
  border-bottom: none; }

.share-modal .modal-title {
  color: #000;
  font-size: 13px; }

.share-modal .close {
  font-size: 13px;
  color: #009ace;
  opacity: 1;
  margin-top: -15px; }
  .share-modal .close:focus {
    outline: none; }

.share-modal .modal-body {
  padding: 15px 25px 30px 25px; }

.share-modal .modal-share-link {
  position: relative; }

.share-modal .video-share-link {
  width: 100%;
  border-radius: 4px;
  border: 1px solid #009ace;
  padding: 5px 105px 5px 10px;
  color: #000; }
  .share-modal .video-share-link:focus {
    outline: none; }

.share-modal .video-item-share-btn {
  position: absolute;
  padding: 5px 15px 5px 5px;
  right: 0;
  top: 50%;
  transform: translateY(-50%); }
  .share-modal .video-item-share-btn:focus {
    color: #009ace; }

.share-modal .video-time-slider-item {
  /* stylelint-disable-line */
  padding: 0 8%; }

.share-modal .video-time-slider-end,
.share-modal .video-time-slider-label-end {
  opacity: 0;
  visibility: hidden; }

.share-modal .noUi-handle-upper {
  display: none; }

.share-modal .pin-enable {
  display: none; }

.video-item-history {
  padding-left: 30%; }

.video-item-history-item {
  margin-bottom: 20px;
  position: relative; }

.video-item-history-icon {
  width: 24px;
  height: 24px;
  line-height: 24px;
  background-color: #707070;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  margin: 10px 0 10px auto; }

.video-item-history-btn {
  text-align: right; }

.project-video-item {
  padding: 0 15px;
  width: 50%; }
  @media (max-width: 576px) {
    .project-video-item {
      width: 100%; } }
  .project-video-item .video-item-comment {
    display: none;
    width: calc(50% - 15px);
    background-color: #fcfcfc;
    padding: 5px 10px 10px 15px;
    margin-left: 15px; }
  .project-video-item .video-item-comment-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px; }
  .project-video-item .video-item-comment-order-right {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end; }
    .project-video-item .video-item-comment-order-right .form-group {
      margin: 0; }
    .project-video-item .video-item-comment-order-right .sumo-select .SumoSelect > .CaptionCont {
      background-color: transparent;
      border: none;
      text-transform: uppercase;
      color: #009ace; }
    .project-video-item .video-item-comment-order-right .sumo-select .SumoSelect > .CaptionCont > label {
      opacity: 0; }
  .project-video-item .video-item-comment-order-arrow {
    color: #009ace; }
    .project-video-item .video-item-comment-order-arrow:before {
      content: '▼';
      display: inline-block; }
    .project-video-item .video-item-comment-order-arrow:hover {
      cursor: pointer; }
  .project-video-item .video-item-comment-resolved {
    display: flex;
    align-items: center; }
    .project-video-item .video-item-comment-resolved .button-switch {
      transform: scale(0.7); }
    .project-video-item .video-item-comment-resolved .video-item-comment-resolved-label {
      margin-left: 3px; }
  .project-video-item .video-item-comment-close {
    color: #009ace;
    transform: rotate(-180deg); }
    .project-video-item .video-item-comment-close:hover {
      cursor: pointer; }
  .project-video-item .video-comment-item {
    padding: 15px 10px 10px 10px;
    border: 1px solid #a7a8a9;
    border-radius: 4px;
    margin-bottom: 15px; }
    .project-video-item .video-comment-item.sub-item {
      margin-left: 10%; }
    .project-video-item .video-comment-item .video-time-slider-item {
      padding: 0 8%; }
  .project-video-item .video-comment-item-reply-content {
    width: calc(100% - 40px); }
  .project-video-item .video-comment-item-reply {
    padding: 15px 10px 10px 10px;
    border: 1px solid #a7a8a9;
    border-radius: 4px;
    margin-bottom: 15px;
    display: flex; }
    .project-video-item .video-comment-item-reply.sub-item {
      margin-left: 10%; }
  .project-video-item .video-comment-reply-content {
    display: flex; }
  .project-video-item .video-comment-seen {
    width: 40px;
    align-self: flex-end;
    margin-bottom: 40px;
    display: flex;
    flex-wrap: wrap; }
  .project-video-item .video-comment-seen-item {
    width: 14px;
    height: 14px;
    line-height: 14px;
    border-radius: 50%; }
    .project-video-item .video-comment-seen-item:not(:first-child) {
      margin-left: 0; }
    .project-video-item .video-comment-seen-item.more {
      background-color: #bebebe;
      color: #fff;
      text-align: center;
      line-height: 8px; }
  .project-video-item .video-comment-seen-item-img {
    max-width: 100%; }
  .project-video-item .video-comment-content {
    width: calc(100% - 40px); }
  .project-video-item .video-comment-item-reply-user {
    width: 40px;
    text-align: right; }
  .project-video-item .video-comment-user-img {
    max-width: 35px; }
  .project-video-item .video-comment-time {
    margin-top: 8px;
    font-size: 10px; }
  .project-video-item .video-pin-time {
    display: flex;
    color: #009ace;
    height: 50px;
    margin-bottom: 0; }
  .project-video-item .video-pin-time:before {
    content: '︎';
    display: inline-block;
    margin-right: 5px;
    padding: 15px;
    background: url('../images/icon-play-circle2.svg') no-repeat top center;
    background-size: contain;
    z-index: 10;
  }

  .project-video-item .gray.video-pin-time:before {
    background: url('../images/icon-play-circle2-gray.svg') no-repeat top center;
    background-size: contain;
  }

  .project-video-item .playing.video-pin-time:before {
    content: '';
    display: inline-block;
    margin-right: 5px;
    padding: 15px;
    background: url('../images/icon-pause-circle.svg') no-repeat top center;
    background-size: contain;
  }

  .project-video-item .playing.gray.video-pin-time:before {
    background: url('../images/icon-pause-circle-gray.svg') no-repeat top center;
    background-size: contain;
  }

  .project-video-item .video-pin-time:hover:before,
  .project-video-item .playing.video-pin-time:hover:before {
    filter: brightness(84%);
  }
    .project-video-item .video-pin-time .video-pin-sign {
      padding: 0 8px; }
  .project-video-item .video-comment-audio-wave {
    background-color: #fff;
    border: 1px solid #a7a8a9;
    border-radius: 4px;
    margin-bottom: 5px; }
  .project-video-item .video-comment-audio-title {
    margin-bottom: 5px; }
  .project-video-item .video-comment-text {
    margin-bottom: 15px; }
  .project-video-item .video-comment-text p {
    margin-bottom: 5px;
    color: #000333; }
  .project-video-item .video-comment-action {
    display: flex; }
    .project-video-item .video-comment-action .video-comment-reply {
      margin-right: 35px; }
      @media (max-width: 768px) {
        .project-video-item .video-comment-action .video-comment-reply {
          margin-right: 10px; } }
    .project-video-item .video-comment-action .video-comment-edit {
      margin-left: auto; }
    .project-video-item .video-comment-action .video-comment-delete {
      color: #a7a8a9;
      margin-left: 35px; }
      @media (max-width: 768px) {
        .project-video-item .video-comment-action .video-comment-delete {
          margin-left: 10px; } }
  .project-video-item .video-comment-title {
    color: #000; }
  .project-video-item .video-comment-message {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #009ace;
    border-radius: 4px;
    margin-bottom: 10px; }
    .project-video-item .video-comment-message .video-comment-input {
      border: none;
      width: calc(100% - 55px);
      text-align: left;
      display: flex; }
    .project-video-item .video-comment-message .video-comment-input-label {
      margin: 0 0 0 13px;
      -ms-grid-row-align: center;
          align-self: center; }
      .project-video-item .video-comment-message .video-comment-input-label:hover {
        cursor: pointer; }
    .project-video-item .video-comment-message .video-comment-input-attach {
      display: none; }
    .project-video-item .video-comment-message .cs-textarea-wrapper {
      height: 35px;
      width: 100%;
      overflow: hidden; }
    .project-video-item .video-comment-message .video-comment-input-text {
      border: none;
      width: 100%;
      overflow: hidden;
      resize: none;
      background-color: transparent; }
      .project-video-item .video-comment-message .video-comment-input-text:-ms-input-placeholder {
        color: #a7a8a9; }
      .project-video-item .video-comment-message .video-comment-input-text::placeholder {
        color: #a7a8a9; }
      .project-video-item .video-comment-message .video-comment-input-text:focus {
        outline: none; }
    .project-video-item .video-comment-message .video-comment-button-send {
      width: 55px;
      text-align: center; }
      .project-video-item .video-comment-message .video-comment-button-send .button--disabled {
        color: #a7a8a9; }
  .project-video-item.active {
    width: 100%; }
    .project-video-item.active .video-item-list {
      width: calc(50% - 15px); }
      @media (max-width: 576px) {
        .project-video-item.active .video-item-list {
          width: 100%; } }
    .project-video-item.active .video-item-bullets {
      display: none; }
    .project-video-item.active .video-item-thumbnails {
      display: block; }
  .project-video-item.show-comment {
    width: 100%;
    display: flex;
    flex-wrap: wrap; }
    .project-video-item.show-comment .video-item-wrap {
      width: calc(50% - 15px); }
    .project-video-item.show-comment .video-item-component.active {
      display: flex;
      align-items: start; }
      @media (max-width: 576px) {
        .project-video-item.show-comment .video-item-component.active {
          display: block; } }
    .project-video-item.show-comment .video-item-component-content {
      width: 100%; }
      @media (max-width: 576px) {
        .project-video-item.show-comment .video-item-component-content {
          width: 100%; } }
    .project-video-item.show-comment .video-item-comment {
      display: block; }
      @media (max-width: 576px) {
        .project-video-item.show-comment .video-item-comment {
          width: 100%;
          margin-left: 0; } }

.project-delivery-content {
  width: 1140px;
  margin: 0 auto;
  padding: 35px 0;
  max-width: 100%; }

.project-delivery-message {
  color: #53565a;
  font-size: 16px;
  margin-bottom: 5px; }

@media (max-width: 576px) {
  .project-delivery-notice {
    padding: 0 15px; } }

.project-delivery-list {
  display: flex;
  flex-wrap: wrap;
  margin: 15px -7.5px 0 -7.5px; }
  @media (max-width: 576px) {
    .project-delivery-list {
      padding: 0 15px; } }

.project-delivery-item {
  width: 20%;
  padding: 0 7.5px; }
  @media (max-width: 576px) {
    .project-delivery-item {
      width: 50%;
      margin-bottom: 10px; } }

.project-delivery-item-content {
  position: relative; }

.project-delivery-chapter {
  position: absolute;
  top: 7px;
  left: 10px;
  color: #fff;
  background: #333;
  border-radius: 50px;
  padding: 0 10px;
  white-space: nowrap;
  max-width: 90%;
  overflow: hidden;
}

.project-progress-action {
  background-color: rgba(131, 131, 131, 0.8);
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9; }

.project-progress-action-content {
  max-width: 1140px;
  display: flex;
  padding: 20px 25px;
  margin: 0 auto;
  align-items: center; }
  @media (max-width: 576px) {
    .project-progress-action-content {
      display: block; } }

.project-progress-action-message {
  color: #fff;
  font-size: 16px; }

.project-progress-action-btn {
  margin-left: auto;
  display: flex; }
  @media (max-width: 576px) {
    .project-progress-action-btn {
      margin-top: 10px; } }
  .project-progress-action-btn .button {
    min-width: 325px;
    font-size: 20px; }
    .project-progress-action-btn .button:focus {
      color: #fff; }

.project-chapter {
  margin-bottom: 10vh; }
  @media (max-width: 576px) {
    .project-chapter {
      margin-bottom: 20vh; } }

.project-chapter-item {
  margin-top: 40px; }

.project-chapter-title, .project-chapter-title-deleted {
  font-size: 24px;
  margin-bottom: 10px;
  color: #000; }
  @media (max-width: 576px) {
    .project-chapter-title, .project-chapter-title-deleted {
      font-size: 20px; } }

.project-chapter-title:hover {
  cursor: grab;
}

.project-chapter-videos {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px -30px -15px; }
  @media (max-width: 576px) {
    .project-chapter-videos {
      margin: 0 -10px -20px -10px; } }

.project-chapter-video-item {
  width: 25%;
  padding: 0 15px;
  margin-bottom: 30px;
  transition: all .5s;}
  @media (max-width: 576px) {
    .project-chapter-video-item {
      width: 50%;
      padding: 0 10px; } }
  .project-chapter-video-item.selected .project-chapter-video-item-content {
    border: 3px solid #009ace; }

.project-delivery-time {
  color: #009ace; }

.project-chapter-video-scence {
  color: #000;
  white-space: nowrap;
  max-width: calc(100% - 50px);
  overflow: hidden;
}

.project-chapter-video-user {
  position: absolute;
  bottom: 10px;
  right: 10px; }

.project-delivery {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background-color: #a7a8a9; }
  .project-delivery.done-select {
    background-color: rgba(131, 131, 131, 0.8); }
    .project-delivery.done-select .project-delivery-notice {
      display: none; }
    .project-delivery.done-select .project-delivery-list {
      display: none; }
    .project-delivery.done-select .project-delivery-content {
      padding: 0; }
    .project-delivery.done-select .project-progress-action {
      position: relative;
      background-color: transparent; }
      .project-delivery.done-select .project-progress-action.sticky {
        background-color: rgba(131, 131, 131, 0.8);
        position: fixed;
        top: 58px;
        left: 0;
        width: 100%;
        z-index: 9; }

.project-setting {
  display: none; }
  .project-setting__filter {
    display: flex;
    align-items: center;
    height: 50px; }
  .project-setting__filter-item {
    color: #009ace;
    height: 30px;
    line-height: 30px;
    padding: 0 30px;
    border-radius: 20px; }
    @media (max-width: 576px) {
      .project-setting__filter-item {
        padding: 0 20px; } }
    .project-setting__filter-item.active {
      background-color: #009ace;
      color: #fff; }
    .project-setting__filter-item:hover {
      cursor: pointer; }
  .project-setting__container {
    padding: 0 20%; }
    @media (max-width: 576px) {
      .project-setting__container {
        padding: 0; } }
  .project-setting__info {
    margin: 30px 0 45px; }
  .project-setting__title {
    margin-bottom: 25px;
    border-top: 1px solid #a7a8a9;
    padding-top: 10px;
    font-size: 16px;
    color: #000; }
  .project-setting__group {
    padding-bottom: 40px; }
  .project-setting__form-group {
    padding: 0 15px; }
  .project-setting__column-2 {
    display: flex;
    align-items: flex-end; }
    .project-setting__column-2 .form-group {
      /* stylelint-disable-line */
      width: 50%; }
      .project-setting__column-2 .form-group:first-child {
        padding-right: 7.5px; }
      .project-setting__column-2 .form-group:nth-child(2) {
        padding-left: 7.5px; }
    .project-setting__column-2 .button {
      /* stylelint-disable-line */
      margin-bottom: 25px; }
  .project-setting .project-development-period {
    display: flex;
    justify-content: space-between; }
    .project-setting .project-development-period .form-group {
      width: 40%;
      margin-bottom: 12px; }
      @media (max-width: 576px) {
        .project-setting .project-development-period .form-group {
          width: 42%; } }
      .project-setting .project-development-period .form-group label {
        /* stylelint-disable-line */
        display: none; }
    .project-setting .project-development-period .form-sign {
      -ms-grid-row-align: center;
          align-self: center; }
  .project-setting .project-development-btn {
    text-align: right; }
  .project-setting .button--background {
    height: 30px;
    line-height: 30px; }
  .project-setting .combodate {
    display: flex;
    justify-content: space-between; }
    .project-setting .combodate select {
      height: 30px;
      background-color: transparent;
      border-radius: 0;
      -webkit-appearance: none;
      appearance: none;
      border-color: #53565a;
      background-image: url("../images/icon-select-dropdown.svg");
      background-position: calc(100% - 10px) 50%;
      background-repeat: no-repeat;
      padding-left: 10px; }
      .project-setting .combodate select:focus {
        outline: none; }
    .project-setting .combodate .year {
      min-width: calc(50% - 7.5px); }
    .project-setting .combodate .month,
    .project-setting .combodate .day {
      min-width: calc(25% - 10.5px); }
  .project-setting .textarea-large .form-control {
    min-height: 140px; }
  .project-setting .input-checkbox {
    margin-bottom: 30px;
    margin-left: 10px; }
    .project-setting .input-checkbox label {
      /* stylelint-disable-line */
      color: #53565a;
      line-height: 20px; }
  .project-setting .project-field-radio {
    display: flex;
    align-items: center;
    margin-bottom: 15px; }
    .project-setting .project-field-radio .input-radio {
      margin-bottom: 0; }
    .project-setting .project-field-radio .form-group {
      margin: 0 5px 0 45px; }
      .project-setting .project-field-radio .form-group label {
        /* stylelint-disable-line */
        display: none; }
    .project-setting .project-field-radio .form-control {
      width: 120px; }
  .project-setting__add-period, .project-setting__add-milestone, .project-setting__add-plan, .project-setting__add-credit {
    margin-bottom: 30px;
    text-align: center; }
    @media (max-width: 576px) {
      .project-setting__add-period, .project-setting__add-milestone, .project-setting__add-plan, .project-setting__add-credit {
        margin-top: 15px; } }
  .project-setting__add-credit {
    margin-top: 15px; }
  .project-setting__notice {
    color: #53565a;
    margin-bottom: 15px; }
  .project-setting__action {
    margin: 45px 0 20px;
    text-align: center; }
    .project-setting__action .button--gradient {
      /* stylelint-disable-line */
      font-size: 20px;
      min-width: 325px;
      text-transform: uppercase; }
      @media screen and (max-width: 767px) {
        .project-setting__action .button--gradient {
          width: 100%;
          min-width: auto; } }
    .project-setting__action .button--text {
      display: block;
      margin-top: 10px; }
  .project-setting__contract .form-group {
    /* stylelint-disable-line */
    display: flex;
    align-items: center; }
  .project-setting__contract .form-control {
    /* stylelint-disable-line */
    display: inline-block;
    width: 155px;
    margin-left: auto;
    text-align: right;
    font-size: 13px; }
  .project-setting__fee {
    display: flex; }
    .project-setting__fee-label {
     }
    .project-setting__fee-value {
      margin-left: auto;
      padding-right: 13px; }
  .project-setting__plan {
    background-color: #fcfcfc;
    margin-bottom: 15px;
    padding: 8px 5px; }
    @media (max-width: 576px) {
      .project-setting__plan {
        margin-left: -15px;
        margin-right: -15px;
        padding: 8px 20px; } }
    .project-setting__plan-btn {
      padding: 20px 0 12px;
      text-align: center; }
    .project-setting__plan .project-setting-member {
      margin-bottom: 25px; }
  .project-setting .project-member-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -7.5px; }
    @media (max-width: 576px) {
      .project-setting .project-member-list {
        flex-wrap: nowrap;
        overflow-x: auto; } }
  .project-setting .project-member-item {
    width: 20%;
    padding: 0 7.5px;
    margin-bottom: 25px;
    position: relative;
    text-align: center; }
    @media (max-width: 576px) {
      .project-setting .project-member-item {
        width: 105px;
        flex: 0 0 auto; } }
    .project-setting .project-member-item__avatar {
      margin-bottom: 10px; }
      .project-setting .project-member-item__avatar-img {
        width: 100%; }
    /* .project-setting .project-member-item__name {
      color: #000; } */
    .project-setting .project-member-item__mess {
      font-size: 10px;
      color: #53565a; }

.project-setting-tab {
  display: none; }
  .project-setting-tab.active {
    display: block; }

.project-setting-member {
  /* stylelint-disable-line */
  display: flex;
  margin-bottom: 15px; }
  .project-setting-member__avatar {
    position: relative; }
    .project-setting-member__avatar-img {
      width: 60px;
      border-radius: 50%; }
  .project-setting-member__active {
    position: absolute;
    width: 15px;
    height: 15px;
    right: 0;
    bottom: 1px;
    background-color: #009ace;
    border: 1px solid #fff;
    border-radius: 50%; }
  .project-setting-member__info {
    padding-left: 25px; }
  .project-setting-member__name {
    font-size: 16px;
    color: #000; }
  .project-setting-member__mess {
    font-size: 10px;
    color: #53565a;
    padding: 2px 0; }
  .project-setting-member__work {
    color: #009ace; }
  .project-setting-member_field-desc {
    margin-top: 5px; }

.project-setting-plan {
  text-align: center; }

.project-setting-music {
  text-align: center; }
  .project-setting-music__title {
    font-size: 10px;
    color: #53565a;
    margin-bottom: 10px; }
  .project-setting-music__name {
    color: #000; }
  .project-setting-music__info {
    color: #000;
    margin-bottom: 20px; }
  .project-setting-music__insert {
    display: flex;
    margin-bottom: 40px; }
  .project-setting-music__item {
    width: 50%;
    color: #000; }
  .project-setting-music__detail {
    margin-bottom: 40px; }
    .project-setting-music__detail-item {
      display: flex;
      margin-bottom: 5px; }
    .project-setting-music__detail-position {
      width: 50%;
      text-align: right;
      font-size: 10px;
      color: #53565a;
      padding-right: 15px; }
    .project-setting-music__detail-name {
      width: 50%;
      text-align: left;
      color: #000;
      padding-left: 15px; }

.project-member-setting-modal {
  text-align: left; }

.project-member-setting {
  position: absolute;
  width: 50%;
  height: calc(100vh - 140px);
  overflow-y: auto;
  top: 140px;
  left: 50%;
  background-color: #f8f8f8;
  padding-bottom: 20px;
  border-radius: 4px; }
  @media (max-width: 576px) {
    .project-member-setting {
      width: 100% !important;
      /* stylelint-disable-line */
      left: 0;
      top: 65px; } }
  .project-member-setting .project-member-list {
    margin-top: 10px; }
  .project-member-setting .project-setting-member {
    border: 1px solid #a7a8a9;
    background-color: #fff;
    margin-left: 20px;
    margin-right: 20px;
    padding: 8px 10px 8px 15px;
    font-size: 10px; }
    .project-member-setting .project-setting-member__setting {
      margin-left: auto;
      text-align: right; }
    .project-member-setting .project-setting-member__permission {
      color: #fff;
      background-color: #009ace;
      display: inline-block;
      padding: 5px; }
    .project-member-setting .project-setting-member__ip {
      border: 1px solid #009ace;
      padding: 4px 5px;
      color: #009ace;
      margin-top: 10px; }
  .project-member-setting .project-member-invite-list {
    margin: 0 20px; }
  .project-member-setting .project-member-invite-item {
    border: 1px solid #a7a8a9;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 5px 10px; }
    .project-member-setting .project-member-invite-item__email {
      font-size: 10px;
      max-width: 50%;}
    .project-member-setting .project-member-invite-item__resend {
      margin-left: 10px;
      font-size: .9em}
    .project-member-setting .project-member-invite-item__revoke {
      margin-left: 10px;
      font-size: .9em}
    .project-member-setting .project-member-invite-item__edit {
      margin-left: auto; }
    .project-member-setting .project-member-invite-item__delete {
      margin-left: 10px; }
  .project-member-setting .project-member-invite-edit {
    margin: 0 25px; }
    .project-member-setting .project-member-invite-edit .project-member-invite-item {
      border-radius: 4px;
      justify-content: flex-start; }

.project-member-title {
  font-size: 13px;
  color: #fff;
  background-image: linear-gradient(to right, #a7a8a9, #333);
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  margin-top: 25px;
  display: inline-block;
  min-width: 125px;
  padding: 1px 15px;
  margin-bottom: 25px; }

.project-member-order {
  text-align: right;
  margin-right: 20px;
  margin-top: -43px;
  color: #009ace; }
  .project-member-order:hover {
    cursor: pointer; }

.project-member-guide {
  padding: 0 25px;
  margin-bottom: 25px; }

.project-setting-member-form {
  margin: 0 25px;
  background-color: #fff;
  border: 1px solid #a7a8a9;
  border-radius: 4px;
  padding: 25px 50px 5px 50px; }
  @media (max-width: 576px) {
    .project-setting-member-form {
      padding: 25px 20px 5px 20px; } }
  .project-setting-member-form .input-checkbox label {
    /* stylelint-disable-line */
    color: #000;
    line-height: 22px; }

.project-member-action {
  text-align: right;
  margin-right: 20px; }
  .project-member-action .button {
    /* stylelint-disable-line */ }
    .project-member-action .button:last-child {
      margin-left: 60px; }

.modal-no-overlay .modal-backdrop.in {
  opacity: 0; }

.project-item__milestone {
  color: #fff;
  margin-top: 25px;
  position: absolute; }

.form-group-search {
  display: flex;
  position: relative; }
  .form-group-search button {
    position: absolute;
    left: 0;
    top: 0;
    width: 40px;
    height: 100%;
    border: none;
    background: none; }
    .form-group-search button:focus {
      outline: none; }
  .form-group-search .form-control {
    padding-left: 40px; }

.form-group.textarea-large label {
  color: #53565a; }

.form-group.textarea-large textarea {
  min-height: 120px; }

.form-datepicker .form-control,
.form-timepicker .form-control {
  background-image: url("../images/icon-select-dropdown.svg");
  background-position: calc(100% - 10px) 50%;
  background-repeat: no-repeat; }

.timepicker-picker table {
  margin: 0 auto; }
  .timepicker-picker table td {
    text-align: center; }

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #a7a8a9;
  color: #53565a; }

@media (max-width: 576px) {
  .messenger__audio-list {
    margin: 0 -30px; } }

@media (max-width: 576px) {
  .messenger__audio-list .audio-player-data {
    margin: 0 -15px; } }

@media (max-width: 576px) {
  .messenger__audio-list .audio-player-waveform {
    padding: 0 15px; } }

.messenger__order-right {
  margin-left: auto;
  display: flex;
  align-items: center; }
  .messenger__order-right .select-container {
    margin: 0; }
  .messenger__order-right .sumo-select {
    min-width: 120px; }
    .messenger__order-right .sumo-select .SumoSelect > .CaptionCont {
      background-color: transparent;
      border: none;
      text-transform: uppercase; }
      .messenger__order-right .sumo-select .SumoSelect > .CaptionCont > label {
        opacity: 0; }
    .messenger__order-right .sumo-select .SumoSelect > .CaptionCont > span {
      color: #009ace; }

.messenger__order-arrow:hover {
  cursor: pointer; }

.messenger__order-arrow:before {
  content: '▼';
  display: inline-block; }

.messenger__order-arrow.active:before {
  content: '▲'; }

.messenger__list-action {
  text-align: center;
  height: 222px;
  background-color: #53565a;
  display: flex;
  align-items: center; }
  @media (max-width: 576px) {
    .messenger__list-action {
      margin-left: -15px;
      margin-right: -15px; } }

.messenger__list-action-content {
  width: 100%; }

.messenger__list-action-notice {
  color: #fff; }

.messenger__list-action-btn .button--gradient {
  font-size: 20px;
  min-width: 325px;
  text-transform: uppercase;
  margin-top: 10px; }
  @media (max-width: 576px) {
    .messenger__list-action-btn .button--gradient {
      min-width: calc(100% - 30px); } }

.messenger-director {
  font-size: 13px;
  margin-top: 75px;}
  .messenger-director .audio-player-title {
    padding-left: 0; }
  .messenger-director__container {
    padding: 0 20%; }
    @media (max-width: 576px) {
      .messenger-director__container {
        padding: 0; } }
  .messenger-director__info {
    margin: 30px 0 0; }
  .messenger-director__form-group {
    padding: 0 15px; }
  .messenger-director__group {
    padding-bottom: 40px; }
    .messenger-director__group:last-child {
      padding-bottom: 0; }
  .messenger-director__column-2 {
    display: flex;
    align-items: flex-end; }
    .messenger-director__column-2 .form-group {
      width: 50%;
      margin-bottom: 10px; }
      .messenger-director__column-2 .form-group:first-child {
        padding-right: 7.5px; }
      .messenger-director__column-2 .form-group:nth-child(2) {
        padding-left: 7.5px; }
        .messenger-director__column-2 .form-group:nth-child(2) .form-control:-moz-read-only {
          /* stylelint-disable-line */
          text-align: right; }
        .messenger-director__column-2 .form-group:nth-child(2) .form-control:read-only {
          /* stylelint-disable-line */
          text-align: right; }
        .messenger-director__column-2 .form-group:nth-child(2) .form-description {
          padding-right: 12px;
          text-align: right; }
    .messenger-director__column-2 .button {
      width: calc(50% - 7.5px);
      margin-bottom: 25px;
      margin-right: 7.5px; }
  .messenger-director .messenger__slider-item {
    margin-bottom: 30px; }
  .messenger-director .messenger__slider-label {
    display: flex;
    justify-content: space-between;
    margin-top: 15px; }
  .messenger-director .messenger__slider-content {
    display: flex;
    align-items: center; }
  .messenger-director .messenger__slider-min, .messenger-director .messenger__slider-max {
    color: #009ace;
    width: 45px; }
  .messenger-director .messenger__slider-max {
    text-align: right; }
  .messenger-director .messenger__slider-bar {
    width: 100%; }
  .messenger-director .messenger .noUi-target {
    background: #a7a8a9;
    border: none;
    box-shadow: none;
    height: 5px;
    border-radius: 4px; }
  .messenger-director .messenger .noUi-handle {
    border: 3px solid #009ace;
    box-shadow: none;
    border-radius: 50%;
    width: 18px;
    height: 18px; }
    .messenger-director .messenger .noUi-handle:before, .messenger-director .messenger .noUi-handle:after {
      display: none; }
    .messenger-director .messenger .noUi-handle:focus {
      outline: none; }
  .messenger-director .messenger .noUi-connect {
    background: #009ace; }
  .messenger-director .messenger__audio-item {
    margin-bottom: 15px; }
  .messenger-director .messenger__audio-button {
    margin-top: 30px;
    text-align: center; }
  .messenger-director .messenger__avatar {
    align-self: center;
    padding-left: 0; }
    @media (max-width: 576px) {
      .messenger-director .messenger__avatar {
        margin-left: 0; } }
  .messenger-director .messenger__name {
    color: #000; }
  .messenger-director .messenger__user-active, .messenger-director .messenger__user-deactive {
    left: 0; }
  .messenger-director .messenger__like {
    color: #009ace;
    font-size: 13px;
    line-height: 1;
    margin-top: 6px; }
    .messenger-director .messenger__like i {
      margin-right: 6px;
      margin-top: -1px; }
  .messenger-director .messenger__info {
    margin-left: 0; }
  .messenger-director .messenger-director-new .messenger__item,
  .messenger-director .messenger-director-offer .messenger__item {
    font-size: 10px;
    margin-bottom: 20px; }
    @media (max-width: 576px) {
      .messenger-director .messenger-director-new .messenger__item,
      .messenger-director .messenger-director-offer .messenger__item {
        margin: 0 -30px 20px -30px;
        padding-left: 30px; } }
  .messenger-director__action {
    margin: 0 0 20px;
    text-align: center; }
    .messenger-director__action .button--gradient {
      font-size: 20px;
      min-width: 325px;
      text-transform: uppercase; }
      @media (max-width: 576px) {
        .messenger-director__action .button--gradient {
          min-width: 100%; } }
  .messenger-director__list {
    margin-bottom: 10px; }
  @media (max-width: 576px) {
    .messenger-director .messenger-director-offer .messenger-director__list {
      margin-left: -20px;
      margin-right: -20px; } }
  @media (max-width: 576px) {
    .messenger-director .messenger-director-offer .messenger-director__item--right .messenger-director__item-form {
      padding: 0 0 0 40px; } }
  @media (max-width: 576px) {
    .messenger-director .messenger-director-offer .messenger-director__item--left .messenger-director__item-form {
      padding: 0 40px 0 0; } }
  .messenger-director .upload__file {
    padding: 0; }
  .messenger-director .form-text-description {
    text-align: right; }
  .messenger-director__item {
    padding: 10px;
    margin-bottom: 10px; }
    @media (max-width: 576px) {
      .messenger-director__item {
        padding: 10px 5px; } }
    .messenger-director__item-content {
      display: flex; }
    .messenger-director__item-avatar {
      width: 35px; }
      .messenger-director__item-avatar-img {
        width: 35px;
        border-radius: 50%; }
    .messenger-director__item-time {
      font-size: 8px;
      color: #53565a; }
    .messenger-director__item-info {
      width: calc(100% - 35px);
      padding-left: 5px; }
    .messenger-director__item-form {
      /* stylelint-disable-line */
      padding: 0 40px 0 0; }
      @media (max-width: 576px) {
        .messenger-director__item-form {
          padding: 0; } }
    .messenger-director__item-title {
      margin-bottom: 5px;
      font-size: 16px;
      color: #009ace;}
    .messenger-director__item-seen {
      display: inline-block;
      margin-left: 5px;
      position: relative;
      top: -1px; }
    .messenger-director__item-mess {
      line-height: 1.5;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      white-space: pre-line;
      word-break: break-word;}

    .messenger-director__item-mess span {
      white-space: pre-line;
      width: 99%
    }
      .messenger-director__item-mess.align-center {
        justify-content: center;
        align-items: center;}
    .messenger-director__item-file {
      color: #009ace;
      display: inline-block;
      padding-top: 5px;
      cursor: pointer;}
      .messenger-director__item-file:before {
        content: '';
        width: 24px;
        height: 16px;
        display: inline-block;
        margin-bottom: -3.5px;
        margin-right: 5px;
        background-image: url("../images/icon-download-line.svg");
        background-size: cover; }
      .messenger-director__item-file:hover {
        color: #009ace; }
    .messenger-director__item--right .messenger-director__item-content {
      flex-direction: row-reverse; }
    .messenger-director__item--right .messenger-director__item-info {
      padding-right: 5px;
      padding-left: 0; }
    .messenger-director__item--right .messenger-director__item-form {
      /* stylelint-disable-line */
      padding: 0 0 0 40px; }
      @media (max-width: 576px) {
        .messenger-director__item--right .messenger-director__item-form {
          padding: 0; } }
    .messenger-director__item--right .messenger-director__item-seen {
      margin-left: 0;
      margin-right: 5px; }
    .messenger-director__item--right .align-center .button {
      margin-left: 0;}
    .messenger-director__item--right .messenger-form {
      padding-left: 40px;
      padding-right: 0; }
    .messenger-director__item--right .form-text-description {
      text-align: left; }
    .messenger-director__item-action {
      margin: 20px 0 15px;
      text-align: center; }
      .messenger-director__item-action .button--gradient {
        font-size: 20px;
        min-width: 325px;
        text-transform: uppercase; }
        @media (max-width: 576px) {
          .messenger-director__item-action .button--gradient {
            min-width: calc(100% - 20px); } }
    .messenger-director__item-reply {
      border: 1px solid #009ace;
      margin: 0 10px; }
      .messenger-director__item-reply .form-group {
        /* stylelint-disable-line */
        margin-bottom: 0; }
      .messenger-director__item-reply label {
        /* stylelint-disable-line */
        display: none; }
      .messenger-director__item-reply .form-control {
        /* stylelint-disable-line */
        border: 1px solid #009ace; }
      .messenger-director__item-reply .messenger-detail__input {
        width: 100%; }
        .messenger-director__item-reply .messenger-detail__input .cs-textarea-wrapper {
          height: 60px; }
      .messenger-director__item-reply .messenger-detail__input-text {
        padding: 22px 0 0 20px;
        min-height: 35px; }
  .messenger-director__review {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 20px);
    margin: 15px auto; }
    .messenger-director__review .button {
      /* stylelint-disable-line */
      width: 32%; }
      @media (max-width: 576px) {
        .messenger-director__review .button {
          min-width: auto; } }
  .messenger-director .messenger-upload {
    text-align: center; }
  .messenger-director .messenger__list {
    height: calc(100vh - 228px); }
    @media (max-width: 576px) {
      .messenger-director .messenger__list {
        height: auto; } }
    @media (max-width: 576px) {
      .messenger-director .messenger-detail {
        height: auto; } }
  .messenger-director .messenger__tag {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-bottom: 2px; }
  .messenger-director .messenger__count {
    position: absolute;
    width: 15px;
    height: 15px;
    line-height: 15px;
    border-radius: 50%;
    background-color: #53565a;
    bottom: 13px;
    right: 5px;
    text-align: center;
    color: #fff;
    font-size: 10px; }
  .messenger-director .messenger__mess {
    font-size: 13px;
    line-height: 1.8;
    max-height: 17px;
    overflow: hidden;}
  .messenger-director .messenger__seen {
    bottom: 10px; }
  .messenger-director .messenger-director-waiting .messenger-detail .messenger-detail-content,
  .messenger-director .messenger-director-doing .messenger-detail .messenger-detail-content {
    border: 1px solid #009ace;
    padding: 5px; }
    @media (max-width: 576px) {
      .messenger-director .messenger-director-waiting .messenger-detail .messenger-detail-content,
      .messenger-director .messenger-director-doing .messenger-detail .messenger-detail-content {
        height: calc(100% - 5px);
        margin: 0 10px 10px 10px; } }
  .messenger-director .messenger-director-doing .messenger__list {
    height: calc(100vh - 450px); }
  @media (max-width: 576px) {
    .messenger-director .messenger__header {
      display: none; } }
  @media (max-width: 576px) {
    .messenger-director .project-item__filter-item {
      padding: 0 15px; } }
  .messenger-director .button-switch {
    transform: scale(0.5); }

.messenger-accept {
  display: inline-block;
  height: 45px;
  line-height: 45px;
  background-color: #53565a;
  min-width: 325px;
  color: #fff;
  margin-bottom: 20px; }
  @media (max-width: 576px) {
    .messenger-accept {
      min-width: calc(100% - 20px); } }
  .messenger-accept:before {
    content: '';
    width: 22.35px;
    height: 18.14px;
    display: inline-block;
    margin-bottom: -5px;
    margin-right: 8px;
    background-image: url("../images/icon-check-line.svg");
    background-size: cover; }
  .messenger-accept:hover {
    color: #fff; }

.messenger-reject:before {
  width: 0;
  background-image: None;
}

.messenger-done:before {
  width: 25px;
  height: 22px;
  background-image: url("../images/icon-heart-border.svg");
}

.align-center {
  text-align: center; }
  .align-center .button {
    /* stylelint-disable-line */
    margin-top: 10px;}

.messenger-form {
  /* stylelint-disable-line */
  padding-right: 40px; }
  .messenger-form__confirm .input-checkbox {
    display: inline-block; }

.no-border {
  border: none; }

.messenger-artist-page .messenger-director-waiting .messenger__list,
.messenger-artist-page .messenger-director-doing .messenger__list {
  height: calc(100vh - 405px); }

.messenger-artist-page .messenger-detail {
  height: calc(100vh - 183px); }

.autoExpand {
	 display: block;
	 box-sizing: border-box;
	 overflow: hidden;
	 padding: 10px;
	 width: 100%;
	 border: none;
	 min-height: 56px;
}
 .autoExpand:focus {
	 outline: none;
}

.creator-profile-new {
  position: relative;
}

.creator-profile-new:after {
    color: #ffffff;
    padding: 0 5px;
    background: #009ace;
    border-radius: 50px;
    height: auto;
    width: auto;
    position: absolute;
    font-size: 10px;
    top: 20px;
}

@media screen and (max-width: 767px) {
  .creator-profile-new:after {
    font-size: 8px;
  }
}

.owner-creator-profile-new:after {
  content: '承認待ち';
  left:50%;
  transform:translateX(-50%);
  white-space: nowrap;
}

.checker-creator-profile-new:after {
  content: 'NEW';
  left:50%;
  transform:translateX(-50%);
}

.project-item_admin {
  position: absolute;
  right: 10px;
  top: 10%
}

.account-link__toggle.active {
  color: #009ace !important;
}

.video-modal-version-item {
  position: absolute;
  display: none;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%; }
  .video-modal-version-item.version-active {
    display: block; }
  .video-modal-version-item video {
    margin: 0 auto; }

.variation-upload {
  position: absolute;
  top: 4px;
  left: 14px;
  z-index: 9;
  opacity: 0;
  transition: all .3s; }
  @media screen and (max-width: 767px) {
    .variation-upload {
      opacity: 1; } }
  .variation-upload .button {
    visibility: hidden; }
    @media screen and (max-width: 767px) {
      .variation-upload .button {
        visibility: visible; } }
  .variation-upload:hover {
    opacity: 1;
    transition: all .3s; }
    .variation-upload:hover .button {
      visibility: visible; }
  .variation-upload--before {
    top: auto;
    bottom: -40px;
    left: 20px; }
  .variation-upload--after {
    top: 50%;
    left: -10px; }
  .variation-upload__file-input {
    display: none !important;
    /* stylelint-disable-line */ }
  .variation-upload__link {
    border-radius: 50%;
    line-height: 1; }
    .variation-upload__link:before {
      content: '+';
      width: 24px;
      height: 24px;
      line-height: 24px;
      background-color: #53565a;
      color: #fff;
      font-size: 24px;
      box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.06); }

.input-field-error {
  color: #e6002d;
  font-size: 10px;
  position: absolute;
  top: calc(100% + 3px);
  left: 50px; }

.price-user {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 8px 14px;
  position: relative;
  border: 1px solid #a7a8a9;
  border-radius: 10px; }
  .price-user__avatar {
    width: 75px;
    align-self: flex-end;
    position: relative; }
  .price-user__info {
    margin-left: 10px;
    width: calc(100% - 75px);
    -ms-grid-row-align: center;
        align-self: center; }
  .price-user__name {
    color: #000;
    }
  .price-user__set {
    display: flex;
    align-items: center; }
  .price-user__min {
    display: flex;
    align-items: center; }

.project-setting-price__button {
  border: 1px solid #a7a8a9;
  padding: 15px;
  text-align: center; }
  .project-setting-price__button-desc {
    color: #000; }

.project-setting-price__current {
  display: flex;
  justify-content: space-between;
  padding: 10px; }

.select-admin__item {
  flex: 0 0 128px;
  margin: 0 10px; }
  .select-admin__item:first-child {
    margin-left: 0; }
  .select-admin__item:last-child {
    margin-right: 0; }

.select-admin__list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto; }
  .select-admin__list.searching .select-admin__item:not(.search-found) {
    display: none; }

.select-admin__avatar-img {
  width: 100%; }

.project-offer__item {
  display: flex;
  align-items: start;
  margin-bottom: 20px; }
  @media screen and (max-width: 991px) {
    .project-offer__item {
      display: block; } }
  .project-offer__item:last-child {
    margin-bottom: 0; }

.offer-user {
  display: flex;
  align-items: start;
  flex: 0 0 354px;
  border: 1px solid #a7a8a9;
  border-radius: 10px;
  padding: 5px 10px; }
  @media screen and (max-width: 991px) {
    .offer-user {
      flex: 1; } }
  .offer-user__avatar {
    width: 75px;
    align-self: flex-end;
    position: relative; }
  .offer-user__info {
    margin-left: 10px;
    width: calc(100% - 155px); }
  .offer-user__name {
    color: #000;
    font-size: 12px;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /*! autoprefixer: on */
    overflow: hidden; }
  .offer-user__work {
    font-size: 10px; }
  .offer-user__money {
    flex: 0 0 80px;
    text-align: right;
    font-size: 10px;
    -ms-grid-row-align: center;
        align-self: center; }
  .offer-user__price {
    margin-bottom: 5px; }
    .offer-user__price-sub {
      margin-bottom: 5px; }
    .offer-user__price-total {
      color: #009dc4; }

.offer-detail {
  flex: 1;
  margin-left: 28px;
  padding: 8px;
  background-color: rgba(240, 240, 240, 0.5); }
  @media screen and (max-width: 991px) {
    .offer-detail {
      margin-left: 0; } }
  .offer-detail__name {
    width: 90px; }
  .offer-detail__bank {
    width: 110px; }
  .offer-detail__branch {
    width: 110px; }
  .offer-detail__branch-number {
    width: 130px; }
  .offer-detail__account-type {
    width: 130px; }
  .offer-detail__account-number {
    width: 110px; }
  .offer-detail__transaction {
    margin-bottom: 0; }
  .offer-detail__price {
    width: 90px; }
  .offer-detail__close-date {
    width: 120px; }
  .offer-detail__accept-date {
    width: 110px; }
  .offer-detail__detail {
    width: 130px; }

.table-offer {
  font-size: 10px; }
  .table-offer thead tr td,
  .table-offer thead tr th,
  .table-offer tbody tr td,
  .table-offer tbody tr th {
    border: none;
    padding: 5px 8px;
    word-break: break-word; }
  .table-offer thead {
    color: #000; }
  .table-offer tbody {
    color: #53565a; }

.list-search__list {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 500px;
  margin: 50px auto 0 auto; }
  @media screen and (max-width: 1440px) {
    .list-search__list {
      max-width: 400px; } }
  @media screen and (max-width: 767px) {
    .list-search__list {
      padding: 0; } }

.list-search__col-small {
  flex: 0 0 15.8%; }

.list-search__col-medium {
  flex: 0 0 21%; }

.list-search__col-large {
  flex: 0 0 26.33%; }

.list-search__item {
  position: relative;
  padding: 5px; }
  .list-search__item:hover {
    cursor: pointer; }
  @media screen and (max-width: 1440px) {
    .list-search__item {
      padding: 3px; } }
  .list-search__item:after {
    content: ''; }
  .list-search__item-banner {
    padding-bottom: 100%;
    background-size: cover;
    background-position: center center; }

.list-search__reload {
  width: 24px;
  height: 24px;
  background-color: #707070;
  background-repeat: no-repeat;
  -webkit-mask-image: url("../images/icon-reload.svg");
  mask-image: url("../images/icon-reload.svg");
  -webkit-mask-size: cover;
  mask-size: cover;
  margin-left: auto;
  margin-right: 25%; }
  .list-search__reload:hover {
    background-color: #009dc4;
    cursor: pointer; }

#contract-modal .modal-dialog {
  width: 80vw;
  max-width: 1140px; }
  @media screen and (max-width: 767px) {
    #contract-modal .modal-dialog {
      width: auto; } }

#contract-modal .modal-body {
  max-height: 70vh;
  padding: 50px; }
  @media screen and (max-width: 767px) {
    #contract-modal .modal-body {
      padding: 30px 15px; } }

.contract-modal {
  color: #000;
  line-height: 1.6; }
  .contract-modal__logo {
    text-align: right;
    margin-bottom: 15px; }
    .contract-modal__logo img {
      max-height: 70px; }
  .contract-modal__title {
    font-size: 20px;
    margin-bottom: 20px; }
  .contract-modal__description {
    margin-bottom: 15px; }
  .contract-modal__section {
    border-top: 1px solid #d6d6d6;
    display: flex;
    padding-top: 15px;
    padding-bottom: 5px; }
    @media screen and (max-width: 767px) {
      .contract-modal__section {
        display: block; } }
  .contract-modal__heading {
    font-size: 16px;
    flex: 0 0 20%; }
    @media screen and (max-width: 767px) {
      .contract-modal__heading {
        margin-bottom: 15px; } }
  @media screen and (max-width: 767px) {
    .contract-modal__text--date {
      margin-bottom: 10px; } }
  .contract-modal__text--date span {
    margin-right: 15px; }
    .contract-modal__text--date span:last-child {
      margin-right: 0; }
  .contract-modal h6 {
    font-size: 13px; }
  .contract-modal p {
    margin-bottom: 10px; }
  .contract-modal ul {
    padding-left: 10px;
    list-style: none; }
  .contract-modal__columns {
    display: flex; }
  .contract-modal__column {
    flex: 0 0 50%; }
  .contract-modal__mark {
    padding-right: 15px;
    padding-top: 20px;
    text-align: right; }
  .contract-modal__label {
    display: inline-block; }
    .contract-modal__label--spec {
      min-width: 130px; }
    .contract-modal__label--deliver {
      min-width: 95px; }
  .contract-modal__occupation {
    font-size: 16px;
    margin-bottom: 15px; }
  .contract-modal__download {
    text-align: right; }
    .contract-modal__download-btn {
      display: inline-block;
      width: 24px;
      height: 16px;
      background-color: #707070;
      background-repeat: no-repeat;
      -webkit-mask-image: url("../images/icon-download.svg");
      mask-image: url("../images/icon-download.svg");
      -webkit-mask-size: cover;
      mask-size: cover; }
      .contract-modal__download-btn:hover {
        cursor: pointer;
        background-color: #009dc4; }
  .contract-modal__close {
    text-align: center; }
    .contract-modal__close-btn {
      border-radius: 30px;
      font-size: 16px;
      min-width: 200px; }

.gallery__list {
  max-width: 500px;
  margin: 50px auto 0 auto; }
  @media screen and (max-width: 1440px) {
    .gallery__list {
      max-width: 400px; } }
  @media screen and (max-width: 767px) {
    .gallery__list {
      padding: 0; } }

.gallery__row {
  display: flex;
  align-items: center;
  justify-content: center; }

.gallery__item {
  position: relative;
  padding: 5px;
  flex: 0 0 20%; }
  .gallery__item:hover {
    cursor: pointer; }
  @media screen and (max-width: 1440px) {
    .gallery__item {
      padding: 3px; } }
  .gallery__item:after {
    content: ''; }
  .gallery__item-banner {
    padding-bottom: 100%;
    background-size: cover;
    background-position: center center; }

.gallery__reload {
  width: 24px;
  height: 24px;
  background-color: #707070;
  background-repeat: no-repeat;
  -webkit-mask-image: url("../images/icon-reload.svg");
  mask-image: url("../images/icon-reload.svg");
  -webkit-mask-size: cover;
  mask-size: cover;
  margin-left: auto;
  margin-right: 25%; }
  @media screen and (max-width: 767px) {
    .gallery__reload {
      margin-right: 15px; } }
  .gallery__reload:hover {
    background-color: #009dc4;
    cursor: pointer; }

.user-item__avatar {
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 50%; }
  .user-item__avatar-img {
    width: 100%;
    border-radius: 50%; }

.user-item__action {
  /* stylelint-disable-line */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  opacity: 0;
  visibility: hidden; }

.user-item__link {
  margin-right: 10px; }
  @media screen and (max-width: 767px) {
    .user-item__link {
      margin-right: 5px; } }

.user-item__delete {
  margin-left: 10px; }
  @media screen and (max-width: 767px) {
    .user-item__delete {
      margin-left: 5px; } }

.user-item__link, .user-item__delete {
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center; }
  @media screen and (max-width: 767px) {
    .user-item__link, .user-item__delete {
      width: 30px;
      height: 30px; } }
  @media screen and (max-width: 767px) {
    .user-item__link .srm-icon, .user-item__delete .srm-icon {
      transform: scale(0.7); } }
  .user-item__link:hover .srm-icon, .user-item__delete:hover .srm-icon {
    background-color: #009dc4; }

.user-item__label {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  background-color: #009dc4;
  color: #fff;
  border-radius: 23px;
  font-size: 12px;
  padding: 0 8px;
  text-transform: uppercase; }
  @media screen and (max-width: 767px) {
    .user-item__label {
      font-size: 10px; } }

.user-item__info {
  text-align: center;
  margin: 8px 0; }

.user-item__name {
  font-size: 16px;
  color: #000;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word;
  margin-bottom: 8px; }
  @media screen and (max-width: 1440px) {
    .user-item__name {
      font-size: 13px; } }
  .user-item__name:hover {
    color: #009dc4;
    transition: all .3s; }

.user-item__work {
  font-size: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word; }
  @media screen and (max-width: 1440px) {
    .user-item__work {
      font-size: 10px; } }

.list-artist,
.list-composer {
  margin-top: 60px;
  padding-top: 10px;
  border-top: 1px solid #d6d6d6; }
  .list-artist__title,
  .list-composer__title {
    font-size: 20px;
    color: #000;
    margin-bottom: 5px; }
  .list-artist__sub-title,
  .list-composer__sub-title {
    font-size: 13px;
    color: #838383;
    margin-bottom: 25px; }
  .list-artist .user-item,
  .list-composer .user-item {
    flex: 0 0 140px;
    margin: 0 10px; }
    .list-artist .user-item:hover,
    .list-composer .user-item:hover {
      cursor: pointer; }
      .list-artist .user-item:hover .user-item__action,
      .list-composer .user-item:hover .user-item__action {
        opacity: 1;
        visibility: visible;
        transition: all .3s; }
    @media screen and (max-width: 1440px) {
      .list-artist .user-item,
      .list-composer .user-item {
        flex: 0 0 110px; } }
    @media screen and (max-width: 767px) {
      .list-artist .user-item,
      .list-composer .user-item {
        flex: 0 0 90px; } }
    .list-artist .user-item:first-child,
    .list-composer .user-item:first-child {
      margin-left: 0; }
    .list-artist .user-item:last-child,
    .list-composer .user-item:last-child {
      margin-right: 0; }
  .list-artist .custom-scrollbar-horizontal,
  .list-composer .custom-scrollbar-horizontal {
    margin-left: 6%;
    padding-bottom: 10px; }
    .list-artist .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
    .list-composer .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
      margin: 0; }
  .list-artist__list,
  .list-composer__list {
    display: flex;
    flex-wrap: nowrap; }

.list-composer {
  margin-bottom: 45px; }
  .list-composer__add {
    margin-right: 10px; }
    .list-composer__add .srm-icon-wrap {
      background-color: #53565a;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center; }
    .list-composer__add .srm-add {
      background-color: #fff; }
    .list-composer__add-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 140px;
      height: 140px;
      border-radius: 50%;
      background-color: #c4c4c4;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
      @media screen and (max-width: 1440px) {
        .list-composer__add-btn {
          width: 110px;
          height: 110px; } }
      @media screen and (max-width: 767px) {
        .list-composer__add-btn {
          height: 90px;
          width: 90px; } }
      .list-composer__add-btn:hover {
        cursor: pointer; }
    .list-composer__add-desc {
      font-size: 15px;
      color: #000;
      margin-top: 10px;
      text-align: center; }
      @media screen and (max-width: 1440px) {
        .list-composer__add-desc {
          font-size: 13px; } }

#composer-modal .modal-body,
#composer-new-modal .modal-body {
  max-height: 100vh;
  padding: 20px; }

.composer-modal .custom-scrollbar-horizontal {
  padding-bottom: 10px; }
  .composer-modal .custom-scrollbar-horizontal .mCustomScrollBox.mCS-minimal-dark + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
    margin: 0; }

.composer-modal .user-item {
  /* stylelint-disable-line */
  flex: 0 0 128px;
  margin: 0 10px; }
  .composer-modal .user-item:hover {
    cursor: pointer; }
  @media screen and (max-width: 1440px) {
    .composer-modal .user-item {
      flex: 0 0 110px; } }
  @media screen and (max-width: 767px) {
    .composer-modal .user-item {
      flex: 0 0 90px; } }
  .composer-modal .user-item:first-child {
    margin-left: 0; }
  .composer-modal .user-item:last-child {
    margin-right: 0; }

.composer-modal__title {
  font-size: 20px;
  color: #000; }

.composer-modal__form {
  margin-top: 40px;
  padding-left: 30px;
  padding-right: 30px; }

.composer-modal__input {
  border: 1px solid #d6d6d6;
  height: 44px;
  line-height: 42px;
  padding: 10px 15px;
  width: 100%; }
  .composer-modal__input:-ms-input-placeholder {
    color: #a7a8a9; }
  .composer-modal__input::placeholder {
    color: #a7a8a9; }
  .composer-modal__input:focus {
    outline: 1px solid #009dc4; }

.composer-modal__action {
  margin-top: 30px; }

.composer-modal .btn-add-composer {
  display: block;
  max-width: 400px;
  margin: 0 auto;
  font-size: 18px;
}

.composer-modal .btn-cancel-composer {
  display: block;
  margin-top: 20px;
  margin-bottom: 15px;
}

.composer-modal__list {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 30px; }
  .composer-modal__list.searching .user-item {
    display: none; }
    .composer-modal__list.searching .user-item.search-found {
      display: block; }

.composer-modal__not-found {
  display: none;
  padding: 25px 10px;
  text-align: center; }
  .composer-modal__not-found.active {
    display: block; }

.gallery-new {
  margin: 50px 0;
  text-align: center; }
  .gallery-new .srm-icon-wrap {
    background-color: #53565a;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center; }
  .gallery-new .srm-add {
    background-color: #fff; }
  .gallery-new__btn {
    display: inline-block; }
    .gallery-new__btn:hover {
      cursor: pointer; }
  .gallery-new__desc {
    margin-top: 10px; }

.composer-new {
  padding: 10px 20px; }
  .composer-new__title {
    font-size: 20px;
    color: #000; }
  .composer-new__form {
    margin-top: 40px; }
  .composer-new__action {
    margin-top: 30px; }
  .composer-new .form-group label {
    color: #000; }
  .composer-new .form-control {
    height: 36px;
    line-height: 36px; }
  .composer-new .btn-new-composer {
    display: block;
    max-width: 400px;
    margin: 0 auto;
    font-size: 18px;
    }
  .composer-new .btn-cancel-composer {
    display: block;
    margin-top: 20px;
    margin-bottom: 15px;
    }

.sheading {
  font-family: 'A+mfCv-AXISラウンド 50 R StdN',
      'M PLUS 1p',
      sans-serif;
    font-weight: normal;
    font-feature-settings: 'clig' off, 'liga' off;
    color: #000;
  }
  .sheading--13 {
    font-size: 13px; }
  .sheading--16 {
    font-size: 16px; }
  .sheading--18 {
    font-size: 18px; }

.scaption {
  font-size: 11px;
  color: #a7a8a9; }
  .scaption--upper {
    text-transform: uppercase; }

.mcontainer {
/*  max-width: 1140px; */
  margin-left: auto;
  margin-right: auto;
  padding: 0 16px;
}

.smenu-pc .mcontainer {
  padding: 0;
}

.mrow {
  display: flex;
  background-color: #fff;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  margin-bottom: 10px;
  margin-top: 104px;
  position: relative;
  overflow: hidden; }
  @media (max-width: 992px) {
    .mrow {
      display: block;
      border-radius: 0;
      border: none;
      margin-top: 64px;
      margin-bottom: 0;
      height: calc(100vh - 245px);
      height: -webkit-calc(var(--app-height) - 245px);
      height: calc(var(--app-height) - 245px);
    }
    .hide-banner .mrow {
      height: calc(100vh - 64px);
      height: -webkit-calc(var(--app-height) - 64px);
      height: calc(var(--app-height) - 64px);
    }

      .martist .maction {
          position: fixed;
          /* bottom: 150px; */
          width: calc(100% + 28px);
      }
  }

.mcolumn--left {
  flex: 0 0 364px;
  max-width: 100%; }

.mcolumn--main {
  flex: 1;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  position: relative; }
  .mcolumn--main.mshow {
    opacity: 1;
    visibility: visible;
    z-index: 2;
    border-left: 0;
    border-right: 0;
    background-color: #fff;
    transform: translateX(0);
    transition: all .3s;
    display: block;}
  @media (max-width: 992px) {
    .mcolumn--main {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      visibility: hidden;
      transform: translateX(100%); } }

.mcolumn--right {
  flex: 0 0 268px;
  width: 268px;
  max-width: 100%;}
  .mcolumn--right.mshow {
    opacity: 1;
    visibility: visible;
    z-index: 3;
    background-color: #fff;
    transform: translateX(0);
    transition: all .3s;
    display: block;
  }
  @media (max-width: 992px) {
    .mcolumn--right {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      visibility: hidden;
      transform: translateX(100%); } }

.mcolumn-header {
  z-index: 9999;
  position: relative;
  width: fit-content;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;}
  @media (max-width: 992px) {
    .mcolumn-header .sheading {
      font-size: 13px; } }

.mcolumn-back {
  display: none;
  font-size: 24px;
  color: #53565a; 
  /* padding: 8px 0px 0px 8px; */
}
  @media (max-width: 992px) {
    .mcolumn-back {
      display: flex; } }

.mcolumn-next {
  display: none;
  font-size: 22px;
  color: #53565a;
  transform: rotate(90deg); }
  @media (max-width: 992px) {
    .mcolumn-next {
      display: flex; } }

.madd-new {
    display: flex;
    width: 100%;
    align-items: center;
}

.madd-new .icon {
    font-size: 20px;
    color: #009ace;
}

.madd-new .icon.icon--sicon-heart {
    color: #009ace;
}

.madd-new .icon.icon--sicon-heart:hover::before {
    color: #009ace;
    content: "";
}

.madd-new .messenger-help {
    margin-left: auto;
    display: flex;
    cursor: pointer;
}

.madd-new .messenger-help:hover {
    color: #A7A8A9;
}

.madd-new .messenger-help .icon {
  color: #A7A8A9;
}

.madd-new .offer-filter {
    display: flex;
    align-items: center;
    width: 150px;
}

.mlist-wrap {
  height: calc(100vh - 300px);
  overflow: auto;
  padding: 16px 12px 0 16px; }
  @media (max-width: 992px) {
    .mlist-wrap {
      height: calc(100vh - 247px);
      height: calc(var(--app-height) - 247px);
      height: -webkit-calc(var(--app-height) - 247px);}
  }

.msearch {
  margin-bottom: 24px; }

.mlist .mitem {
  cursor: pointer;
  margin-bottom: 8px; }
  .mlist .mitem:last-child {
    margin-bottom: 0; }

.mlist .mactive {
  position: relative; }
  .mlist .mactive:before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    width: 6px;
    height: 40px;
    border-radius: 3px;
    background-color: #009ace;
    transform: translateY(-50%); }

.mfinish {
  display: flex;
  background-color: #000;
  border-radius: 6px;
  padding: 16px;
  margin: 25px 16px 16px 16px; }
  .mfinish-text {
    color: #fff;
    margin-right: 12px;
    text-align: right; }
  .mfinish-btn {
    margin-left: auto; }

.mcontent {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 100%;
      grid-template-columns: 100%;
  -ms-grid-rows: 1fr auto;
      grid-template-rows: 1fr auto;
  /* height: calc(100vh - 180px); */
  height: calc(100vh - 90px);
  position: relative;
  z-index: 0; }
  @media (max-width: 992px) {
    .mcontent {
      /* height: calc(100vh - 128px); */
      height: -webkit-calc(var(--app-height) - 128px);
      height: calc(var(--app-height) - 128px);} }

.pd-section__content:not(active) .mmessage--received:not(.mmessage-near) .message-info-audio{
  margin-left:40px;
}

.pd-section__content.active .mmessage--received:not(.mmessage-near) .message-info-audio{
  margin-left: 0;
}

.mmessage-main {
  display: flex;
  align-items: flex-end;
  flex: 0 0 auto;
  max-width: calc(100% - 120px); }
  .mmessage-main .avatar {
    flex: 0 0 32px; }
  .mmessage-main .mmessenger {
    margin-bottom: 2px; }
    .mmessage-main .mmessenger:last-child {
      margin-bottom: 0; }
  .mmessage-main .mmessage-content {
    padding-left: 4px;
    padding-right: 4px; }

@media (max-width: 992px) {
    .mmessage .mmessage-main {
        max-width: calc(100% - 60px);
        transition: 0.3s
    }
    .pd-section__content .mmessage .mmessenger--audio-wave .s-audio {
        /* min-width: 165px; */
         transition: 0.3s
    }

    .pd-section__content.active .mmessage .mmessenger--audio-wave .s-audio {
      min-width: 240px;
    }

    .mmessage--received:not(.mmessage-near) .message-info-audio {
        margin-left: 0px;
    }

    .pd-section__content:not(active) .mmessage--received:not(.mmessage-near) .message-info-audio {
        margin-left: 0;
    }

    .pd-section__content.active .mmessage--received:not(.mmessage-near) .message-info-audio {
        margin-left: 0;
    }
}

@media (max-width: 576px) {
  .pd-section__content .mmessage .mmessenger--audio-wave .s-audio, .pd-section__content.active .mmessage .mmessenger--audio-wave .s-audio {
    min-width: 145px;
  }
}

.mmessage-info {
  flex: 0 0 auto;
  display: flex !important;
  flex-direction: column;
  justify-content: flex-end;
  /* margin-top: 8px; */
  font-size: 13px;
  height: 100% !important;
  color: #a7a8a9; }

.message-info-container {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  height: auto !important;
  cursor: pointer !important;
}

.show-action-hover {
  display: unset !important;
}

.actions-received {
  display: flex;
  justify-content: flex-start !important;
}

.message-actions-container {
  display: flex;
  height: 50%;
  justify-content: flex-end;
  align-items: flex-end;
  cursor: pointer !important;
}

@media (max-width: 992px) {
  .mmessage-status {
    align-items: center; } }

.mmessage-action {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-left: 4px;
  transition: all .3s; }
  .mmessage-action .mmessage-delete, .mmessage-action .mmessage-resolve {
    margin-right: 6px;
    margin-left: 6px; }
  .mmessage-action .mmessage-reply {
    font-size: 12px; }
  .mmessage-action .mmessage-edit,
  .mmessage-action .mmessage-download,
  .mmessage-action .mmessage-reply,
  .mmessage-action .mmessage-resolve,
  .mmessage-action .mmessage-delete, .message-first__message {
    width: 22px;
    height: 22px;
    background-color: rgba(167, 168, 169, 0.2);
    border-radius: 50%;
    color: #a7a8a9;
    display: inline-flex;
    align-items: center;
    justify-content: center; }

  .mmessage-action .mmessage-edit:hover,
  .mmessage-action .mmessage-download:hover,
  .mmessage-action .mmessage-reply:hover,
  .mmessage-action .mmessage-resolve:hover,
  .mmessage-action .mmessage-delete:hover, .message-first__message:hover {
      background-color: #009ace;
      color: #fff;
 }

 .mmessage-action .mmessage-resolve.active, .mmessage-action .mmessage-resolve.mmessage-resolved, .mmessage-action .mmessage-reply.active,
 .mmessage-action .mmessage-edit.active {
    background-color: #009ace;
    color: #fff; }

.mmessage-time {
  margin-bottom: 5px; }
  @media (max-width: 992px) {
    .mmessage-time {
      margin-bottom: 0;
      margin-right: 8px; } }

.mmessage-user {
  position: relative;
  display: flex;
  align-items: flex-end; }
  .mmessage-user .notification {
    margin-left: 2px;
    font-size: 9px;
    color: #53565a; }

.mmessage-user-seen {
  display: flex; }
  .mmessage-user-seen:not(:first-child) {
    margin-left: -5px; }
  .mmessage-user-seen .avatar {
    background-color: #fff;
    border: 1px solid #fff; }

.mmessage-user .user-status-icon.online {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background-color: #2CC84D;
  border: 1px solid #FFFFFF;
  right: 0;
  bottom: 0;
  box-sizing: content-box; }

  .mmessage-user .user-status-icon.offline {
    display: none; }

.mmessage {
  margin-bottom: 4px;
  word-break: break-word;
  display: flex;
  align-items: flex-end;
  /* cursor: pointer; */
}

  /*.mmessage div {*/
  /*  cursor: default;}*/
  .mmessage:last-child {
    margin-bottom: 0; }
  .mmessage--sent.mmessage {
    flex-direction: row-reverse; }
  .mmessage--sent .mmessage-main {
    flex-direction: row-reverse;
    text-align: left; }
  .mmessage--sent .mmessage-content {
    padding-right: 0;
    padding-left: 4px; }
  .mmessage--sent .mmessage-time {
    text-align: right; }
    @media (max-width: 992px) {
      .mmessage--sent .mmessage-time {
        margin-right: 0; } }
  .mmessage--sent .mmessage-user {
    flex-direction: row-reverse; }
  .mmessage--sent .notification {
    margin-left: 5px;
    /*margin-right: 7px; */
  }
  .mmessage--sent .mmessage-user-seen:not(:first-child) {
    /*margin-right: -5px;*/
  }
  .mmessage--sent .mmessage-info {
    flex-direction: row-reverse; }
  .mmessage--sent .mmessage-status {
    flex-direction: row-reverse; }
  .mmessage--sent .mmessage-action {
    flex-direction: row-reverse;}
  .mmessage--sent .mmessage-delete {
    margin-right: 6px; }
  .mmessage:hover .mmessage-action {
    opacity: 1;
    visibility: visible;
    transition: all .3s; }

.mscrollbar {
  overflow: auto; }
  .mscrollbar {
    scrollbar-face-color: #d3d3d3;
    scrollbar-track-color: transparent; }
  .mscrollbar::-webkit-scrollbar {
    width: 4px;
    height: 4px; }
  .mscrollbar::-webkit-scrollbar-thumb {
    background: #f0f0f0;
    border-radius: 2px; }
  .mscrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px; }
  .mscrollbar--dark {
    scrollbar-face-color: #53565a;
    scrollbar-track-color: transparent; }
  .mscrollbar--dark::-webkit-scrollbar {
    width: 4px;
    height: 4px; }
  .mscrollbar--dark::-webkit-scrollbar-thumb {
    background: #53565a;
    border-radius: 2px; }
  .mscrollbar--dark::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px; }

.mmessage-list {
  height: 100%;
  overflow: hidden auto;
  position: relative;
  box-sizing: border-box;
  padding: 16px 0px 0px 8px;
  flex: 1;}
  @media (max-width: 992px) {
    .mmessage-list {
      height: calc(100vh - 203px);
      height: -webkit-calc(var(--app-height) - 203px);
      height: calc(var(--app-height) - 203px);}}
.maction {
  background-color: #fcfcfc;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;}


.minfo-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px; }
  .minfo-header .minfo-label {
    margin-left: auto; }

.minfo-wrap {
  max-height: calc(100vh - 196px);
  overflow: auto; }
  @media (max-width: 992px) {
    .minfo-wrap {
      max-height: calc(100vh - 144px);
      height: -webkit-calc(var(--app-height) - 144px);
      height: calc(var(--app-height) - 144px);} }

.minfo-list {
  padding: 6px 0 10px; }

.minfo-contract {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 32px 20px 24px;
  margin-top: 24px;
  text-align: center; }

.minfo-confirm {
    text-align: left;
}

.minfo-contract-desc {
  margin-top: 24px; }

.minfo-contract-btn {
  margin-top: 16px; }
  .minfo-contract-btn .btn {
    width: 100%; }

.minfo-contract-icon {
  font-size: 32px;
  color: #53565A;
  background-color: #f0f0f0;
  width: 68px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto; }

.minfo-contract-icon:hover {
  color: #FFFFFF;
  background-color: #009ACE;
  cursor: pointer;
}

.minfo-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0; }
  .minfo-section:last-child {
    border-bottom: none;
    padding-bottom: 0; }

.minfo-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px; }
  .minfo-item:last-child {
    margin-bottom: 0; }
  .minfo-item .minfo-label {
    color: #a7a8a9; }
  .minfo-item .minfo-value {
    margin-left: auto;
    max-width: 200px;
    color: #000000;}

@media (max-width: 992px) {
    .minfo-item .minfo-value {
        max-width: 300px
    }
}

.minfo-audio {
  margin-bottom: 16px; }

.minfo-file .minfo-file-preview {
  margin-bottom: 16px; }

.minfo-file .minfo-file-name .messenger-content {
  display: flex; }

.minfo-video {
  margin-bottom: 16px; }

.minfo-folder {
  margin-bottom: 16px;
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 16px; }
  .minfo-folder ul {
    padding-left: 40px;
    margin: 0;
    list-style: none; }
  .minfo-folder li {
    margin-bottom: 12px;
    white-space: nowrap; }
    .minfo-folder li:first-child {
      margin-top: 16px; }
    .minfo-folder li:last-child {
      margin-bottom: 0; }
  .minfo-folder .icon {
    font-size: 20px;
    margin-right: 12px;
    position: relative;
    bottom: -2px; }
  .minfo-folder .mfolder {
    padding: 0; }
    .minfo-folder .mfolder > li:first-child {
      margin-top: 0; }
    .minfo-folder .mfolder > li > .icon {
      font-size: 24px;
      margin-right: 16px;
      position: relative;
      bottom: -3px; }

.edit-thread__form {
  margin-top: 48px;
  margin-bottom: 74px; }

.edit-thread__action {
  margin-bottom: 8px;
  text-align: center; }
  .edit-thread__action .btn {
    min-width: 125px; }

.create-thread__policy {
  margin-top: 40px;
  padding: 0 40px; }

.create-thread__link {
  color: #009ace; }
  .create-thread__link:hover {
    color: #009ace; }

.create-thread__content {
  text-align: center; }

.create-thread__accept {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24px; }
  .create-thread__accept .form-check-label {
    position: relative;
    top: -1px; }

.create-thread__action {
  margin-top: 40px; }

.create-thread__mobile {
  margin-top: 40px; }

.create-thread__close {
  margin: 16px 0; }
  .create-thread__close .btn {
    margin: 0 auto; }

.create-thread__example {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  color: #d3d3d3;
  padding: 12px 16px;
  margin-top: 40px;
  text-align: left; }

.create-thread__upload {
  margin-top: 24px; }
  .create-thread__upload .mupload-file {
    border-radius: 6px; }

.create-thread .sform-row {
  margin-top: 24px;
  text-align: left; }
  .create-thread .sform-row input[type='number'] {
    -moz-appearance: textfield;
    appearance: textfield; }
    .create-thread .sform-row input[type='number']::-webkit-outer-spin-button, .create-thread .sform-row input[type='number']::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0; }

.view-thread__files {
  padding-right: 30px; }

.view-thread .s-file {
  display: inline-flex;
  margin-right: 4px;
  margin-bottom: 8px; }

.view-thread__users {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -4px;
  margin-top: 24px;
  margin-bottom: 16px; }

.view-thread__user {
  flex: 0 0 32px;
  margin: 0 4px; }

.view-thread__watched {
  margin-top: 30px; }

.view-thread__downloaded {
  margin-top: 35px; }

#modal-create-thread-2 .modal-dialog {
  width: 752px; }

#modal-create-thread-2 .modal-header {
  border-bottom: none;
  padding-bottom: 16px; }

#modal-create-thread-2 .smodal-close {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  color: #53565a;
  text-align: center; }
  #modal-create-thread-2 .smodal-close .icon {
    margin-left: -3px; }

.psearch {
  margin-top: 104px; }
  @media (max-width: 992px) {
    .psearch {
      margin-top: 64px;
      padding: 0 15px;
      background-color: #fff; } }

.psearch-top {
  background-color: #fff;
  border-radius: 12px;
  /*overflow: hidden;*/
  margin-bottom: 24px; }
  @media (max-width: 992px) {
    .psearch-top {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px; } }

.psearch-banner {
  height: 240px;
  background-size: cover;
  overflow: hidden; }
  @media (max-width: 992px) {
    .psearch-banner {
      height: 120px; } }

.psearch-progress .progress {
  height: 3px;
  background-color: #f0f0f0;
  border-radius: 0; }

.psearch-progress .pprogress {
  margin-bottom: 0 }

.psearch-progress .progress-bar {
  background-color: #009ace;
  border-radius: 0; }

.psearch-progress .bg-warning {
   background-color: #53565a; }

.psearch-tabs {
  display: flex;
  border: 1px solid #f0f0f0;
  border-top: none;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px; }

.psearch-tab {
  padding: 22px 40px;
  border-right: 1px solid #f0f0f0;
  color: #a7a8a9;
  display: flex;
  align-items: center; }
  @media (max-width: 992px) {
    .psearch-tab {
      padding: 15px 23px; } }
  @media (max-width: 992px) {
    .psearch-tab:last-child {
      border-right: none; } }
  .psearch-tab .icon {
    margin-right: 8px;
    font-size: 24px; }
  .psearch-tab.active {
    color: #009ace; }
  .psearch-tab:hover {
      color: #009ace;
    cursor: pointer; }
.psearch-tab a{
    color: #a7a8a9;
}

.psearch-tab a:hover{
    color: #009ace;
}
/* .psearch-section { */
  /* border: 1px solid #f0f0f0; */
  /* border-radius: 12px; */
  /* background-color: #fff; */
  /* margin-bottom: 24px; } */
  /* @media (max-width: 992px) { */
    /* .psearch-section { */
      /* border: none; } } */

.psearch-title {
  padding: 18px 16px;
  border-bottom: 1px solid #f0f0f0; }
  @media (max-width: 992px) {
    .psearch-title {
      padding: 18px 0;
      border-bottom: none; } }

/* .psearch-content { */
  /* padding: 24px; } */
  /* @media (max-width: 992px) { */
    /* .psearch-content { */
      /* padding: 0; } } */

/* .psearch-form .sform-row:not(.psearch-tag) { */
  /* margin-bottom: 24px; } */
  /* @media (max-width: 992px) { */
    /* .psearch-form .sform-row:not(.psearch-tag) { */
      /* margin-bottom: 16px; } } */

@media (max-width: 992px) {
  .psearch-form .sform-row--3-columns {
    display: flex;
    flex-wrap: wrap; } }

@media (max-width: 992px) {
  .psearch-form .sform-row--3-columns .sform-group:nth-child(1) {
    flex: 0 0 50%; } }

@media (max-width: 992px) {
  .psearch-form .sform-row--3-columns .sform-group:nth-child(2) {
    flex: 0 0 50%;
    padding-right: 0; } }

.psearch-tag {
  margin-bottom: 40px; }
  .psearch-tag .psearch-tag-title {
    margin-bottom: 12px; }
    @media (max-width: 992px) {
      .psearch-tag .psearch-tag-title {
        display: none; } }
  .psearch-tag .slabel {
    margin-right: 4px;
    margin-bottom: 8px; }

.psearch-submit {
  text-align: center; }
  .psearch-submit .btn {
    min-width: 160px; }
    @media (max-width: 992px) {
      .psearch-submit .btn {
        width: 100%; } }

.psearch-artist-list {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;}
  .psearch-artist-list .mcreator {
    flex: 0 0 320px;
    margin: 0 12px; }
    .psearch-artist-list .mcreator:first-child {
      margin-left: 0; }
    .psearch-artist-list .mcreator:last-child {
      margin-right: 0; }


.psearch-gallery {
  margin-bottom: 24px; }

.psearch-artist-block {
  display: flex;
  margin-top: 24px; }
  @media (max-width: 992px) {
    .psearch-artist-block {
      display: block; } }
  @media (max-width: 992px) {
    .psearch-artist-block .mcalendar {
      margin-left: -16px;
      margin-right: -16px; } }
  @media (max-width: 992px) {
    .psearch-artist-block .mcalendar .table-condensed {
      width: 100%; } }

.psearch-album-list {
  display: flex;
  flex-wrap: wrap; }
  @media (max-width: 992px) {
    .psearch-album-list {
      display: block; } }
  .psearch-album-list .s-audio {
    padding: 2px 16px; }
  .psearch-album-list .psearch-album-item {
    flex: 0 0 50%;
    margin-bottom: 24px; }
    .psearch-album-list .psearch-album-item.active .s-audio {
      background-color: #000; }
    @media (max-width: 992px) {
      .psearch-album-list .psearch-album-item {
        margin-bottom: 16px; } }
    .psearch-album-list .psearch-album-item:nth-child(2n + 1) {
      padding-right: 12px; }
      @media (max-width: 992px) {
        .psearch-album-list .psearch-album-item:nth-child(2n + 1) {
          padding-right: 0; } }
    .psearch-album-list .psearch-album-item:nth-child(2n) {
      padding-left: 12px; }
      @media (max-width: 992px) {
        .psearch-album-list .psearch-album-item:nth-child(2n) {
          padding-left: 0; } }
    .psearch-album-list .psearch-album-item:nth-last-of-type(-n+2) {
      margin-bottom: 0; }
      @media (max-width: 992px) {
        .psearch-album-list .psearch-album-item:nth-last-of-type(-n+2) {
          margin-bottom: 16px; } }

.psearch-artist {
  background-color: #000;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px; }
  @media (max-width: 992px) {
    .psearch-artist {
      overflow-x: hidden;
      padding: 16px; } }

.psearch-artist-top {
  display: flex;
  justify-content: space-between; }
  @media (max-width: 992px) {
    .psearch-artist-top {
      display: block; } }

.psearch-artist-info {
  display: flex;
  align-items: center; }
  @media (max-width: 992px) {
    .psearch-artist-info {
      display: block;
      text-align: center;
      margin-top: 8px; } }
  .psearch-artist-info .avatar {
    border: 2px solid #fff;
    border-radius: 50%; }

.psearch-artist-meta {
  padding-left: 16px; }
  @media (max-width: 992px) {
    .psearch-artist-meta {
      padding-left: 0;
      margin-top: 16px; } }

@media (max-width: 992px) {
  .psearch-artist-btn {
    text-align: center;
    margin-top: 24px; } }

.psearch-artist-name {
  color: #fff; }

.psearch-artist-role {
  color: #fff; }

.psearch-artist-contract {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16px;
  padding-right: 0;
  border-radius: 6px; }
  @media (max-width: 992px) {
    .psearch-artist-contract {
      border-radius: 0;
      padding-right: 16px;
      margin: 0 -16px; } }

.psearch-artist-title {
  color: #fff;
  margin-top: 8px;
  margin-bottom: 16px; }

.psearch-artist-policy {
  white-space: pre-line;
  max-height: 84px;
  color: #d3d3d3; }

@media (max-width: 992px) {
    .psearch-artist-policy {
        max-height: none;
    }
}

.psearch-artist-offer {
  margin-top: 28px; }

.artist-offer__wrap {
  padding-top: 5px;
  margin-bottom: 18px; }

.artist-offer__slider {
  display: flex;
  margin: 0 calc(-10% - 20px); }
  @media (max-width: 992px) {
    .artist-offer__slider {
      margin: 0 -10%; } }

.artist-offer__item {
  width: 20%;
  position: relative; }
  .artist-offer__item:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.3);
    top: 50%;
    left: 50%;
    transform: translateY(-50%); }
  .artist-offer__item:after {
    content: '';
    position: absolute;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 2px solid #fff;
    background-color: #000;
    transform: translate(-50%, -50%); }
  .artist-offer__item:first-child:after {
    left: calc(50% - 2px); }
  .artist-offer__item:last-child:after {
    left: calc(50% + 2px); }
  .artist-offer__item:last-child:before {
    display: none; }
  .artist-offer__item.active:after {
    width: 16px;
    height: 16px;
    background-color: #009ace;
    border: 2px solid #fff; }
  .artist-offer__item.active:first-child:after {
    left: calc(50% + 2px); }
  .artist-offer__item.active:last-child:after {
    left: calc(50% - 2px); }

.artist-offer__label {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
  color: #fff; }


.psearch-artist-block-list {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 16px;
  margin-left: 24px;
  max-height: 327px; }
  @media (max-width: 992px) {
    .psearch-artist-block-list {
      margin-left: -16px;
      margin-right: -16px;
      margin-top: 16px;
      max-height: none; } }

.block-company {
  display: flex;
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2); }
  .block-company:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none; }
  .block-company__avatar .avatar {
    border-radius: 6px; }
  .block-company__name {
    color: #fff; }
  .block-company__business {
    color: #fff; }
  .block-company__info {
    padding-left: 12px; }
  .block-company__action {
    margin-left: auto;
    -ms-grid-row-align: center;
        align-self: center; }
  .block-company__delete {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: inline-block;
    text-align: center;
    line-height: 34px;
    font-size: 20px;
    color: #fff; }
    .block-company__delete:hover {
      color: #fff; }


@media (max-width: 992px) {
  .p-martist .psearch-top {
    padding: 0;
    margin-bottom: 0; } }



.muser-tooltip {
  padding: 6px 0; }
  .muser-tooltip .muser-name {
    font-size: 16px;
    }

.martist .mrow {
  margin-top: 24px; }
  @media (max-width: 992px) {
    .martist .mrow {
      margin-top: 0; } }

.martist.role_.hide-banner {
  margin-top: -280px; }
.martist.role_creator.hide-banner {
  margin-top: -120px; }
  .martist.hide-banner .mrow {
    overflow: visible; }

.martist .mcolumn--main {
  z-index: 2; }

@media (max-width: 992px) {
  .martist .mcolumn--main, .martist .mcolumn--right {
    height: calc(100% + 178px); } }

.martist .mcolumn--main .mcolumn-header, .martist .mcolumn--right .mcolumn-header {
  display: none; }
  @media (max-width: 992px) {
    .martist .mcolumn--main .mcolumn-header, .martist .mcolumn--right .mcolumn-header {
      display: flex; } }

.martist .mlist-wrap {
  /* height: calc(100vh - 244px); */
  height: calc(100% - 20px);
  padding-bottom: 16px; }
  @media (max-width: 992px) {
    .martist .mlist-wrap {
      height: calc(100vh - 306px);
      height: -webkit-calc(var(--app-height) - 306px);
      height: calc(var(--app-height) - 306px); }}

.martist .minfo-wrap {
  max-height: calc(100vh - 180px); }
  @media (max-width: 992px) {
    .martist .minfo-wrap {
      max-height: calc(100vh - 128px); } }

.martist .accordion-header {
  border-bottom: none; }

.martist .accordion-heading.collapsed {
  border-bottom: 1px solid #f0f0f0; }

.martist .accordion .minfo-section {
  padding-top: 0; }

.martist .minfo-item {
  align-items: flex-start; }

.martist .minfo-value {
  text-align: right; }
  .martist .minfo-value .scaption {
    margin-top: 6px; }

.martist .minfo-contract {
  margin-top: 16px; }

.minfo-rating {
  display: flex;
  margin-top: 6px;
  margin-left: -4px; }
  .minfo-rating .btn {
    flex: 0 0 calc(50% - 7px);
    padding: 7.5px 3px;
    min-width: auto;
    margin: 0 4px; }
    .minfo-rating .btn .icon {
      margin: 0; }

.minfo-accept__policy {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px; }
  .minfo-accept__policy .form-check-label {
    position: relative;
    top: -1px; }




.create-offer__artist {
  display: flex;
  padding: 16px;
  margin-top: 36px;
  background-color: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 6px; }

.create-offer__avatar .avatar {
  display: flex; }

.create-offer__info {
  padding-left: 8px; }

.create-offer__policy {
  padding: 16px;
  margin-top: 24px;
  background-color: #fcfcfc;
  border: 1px solid #f0f0f0;
  border-radius: 6px; }
  .create-offer__policy .sheading {
    margin-bottom: 12px; }
  .create-offer__policy-content {
    white-space: pre-line;
    max-height: 80px; }

.create-offer__form {
  margin-top: 24px; }
  .create-offer__form .sform-row {
    /* stylelint-disable-line */
    margin-bottom: 24px; }

.create-offer__price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px; }

.create-offer__price-number {
  padding-right: 11px; }

/* .create-offer__action { */
  /* text-align: center; */
  /* margin-bottom: 16px;} */
  /* @media (max-width: 992px) { */
    /* .create-offer__action { */
      /* display: flex; */
      /* flex-wrap: wrap; */
      /* flex-direction: column-reverse; */
      /* margin-bottom: 0; } } */
  /* .create-offer__action .btn { */
    /* min-width: 176px; } */
    /* @media (max-width: 992px) { */
      /* .create-offer__action .btn { */
        /* min-width: auto; */
        /* width: 100%; } } */
    /* .create-offer__action .btn:first-child { */
      /* margin-right: 8px; } */
      /* @media (max-width: 992px) { */
        /* .create-offer__action .btn:first-child { */
          /* margin-right: 0; } } */
    /* .create-offer__action .btn:last-child { */
      /* margin-left: 8px; } */
      /* @media (max-width: 992px) {
        .create-offer__action .btn:last-child {
          margin-left: 0;
          margin-bottom: 8px; }  */
        /* } */

.invite-member__intro {
  margin-bottom: 40px; }

.invite-member__form .sform-row {
  /* stylelint-disable-line */
  margin-bottom: 24px; }

.invite-member__form .explaination-text {
    margin-left: 32px;
    /* width: 80%; */
}

/* .invite-member__action {
  margin-bottom: 8px;
  margin-top: 32px;
  text-align: right; } */

.scene-info__content {
  margin-top: 16px; }

.scene-info__heading {
  padding-bottom: 24px;
  position: relative; }
  .scene-info__heading:before {
    content: '';
    position: absolute;
    height: 1px;
    background-color: #f0f0f0;
    bottom: 0;
    left: -24px;
    right: -24px;
    width: calc(100% + 48px); }

.scene-row {
  display: flex;
  flex-wrap: wrap;
  margin-top: 24px;
  padding-bottom: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid #f0f0f0; }
  @media (max-width: 992px) {
    .scene-row {
      display: block; } }
  .scene-row--document {
    align-items: flex-start;
    padding-bottom: 0;
    border-bottom: none; }

.scene-col-reward {
  flex: 0 0 170px;
  padding-right: 8px; }
  @media (max-width: 992px) {
    .scene-col-reward {
      padding-right: 0; } }

.scene-col-time {
  flex: 0 0 267px; }
  @media (max-width: 992px) {
    .scene-col-time {
      margin-top: 24px; } }

.scene-col-deadline {
  flex: 0 0 auto;
  padding-left: 24px; }
  @media (max-width: 992px) {
    .scene-col-deadline {
      padding-left: 0;
      margin-top: 24px; } }
  .scene-col-deadline .mcalendar {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    margin-top: 24px; }
    .scene-col-deadline .mcalendar .datepicker-inline .dow {
      padding: 11px; }

.scene-tasks {
  margin-top: 8px; }
  .scene-tasks .scene-task {
    background-color: #f0f0f0;
    border-radius: 6px;
    padding: 8px 16px;
    margin-bottom: 8px; }

.scene-caption {
  padding-top: 8px;
  text-align: right; }

.scene-info-item {
  display: flex; }
  .scene-info-item .scene-label {
    color: #a7a8a9;
    margin-right: 8px; }

.scene-col-upload .mupload {
  display: flex;
  align-items: start; }
  @media (max-width: 992px) {
    .scene-col-upload .mupload {
      display: block; } }

.scene-col-upload .mupload-file {
  flex: 0 0 243px;
  margin-right: 24px; }
  @media (max-width: 992px) {
    .scene-col-upload .mupload-file {
      margin-right: 0; } }

.scene-col-upload .mupload-preview-container {
  flex: 1;
  display: flex;
  align-items: start; }

.scene-col-upload .mupload-previews {
  width: 243px;
  float: left;
  margin-top: 0;
  margin-right: 24px;
  margin-bottom: 16px; }
  .scene-col-upload .mupload-previews:empty {
    margin-bottom: 0; }
  @media (max-width: 992px) {
    .scene-col-upload .mupload-previews {
      margin-top: 24px;
      margin-right: 0;
      width: 100%; } }

.scene-col-upload .mupload-file-list {
  margin-top: 0; }
  .scene-col-upload .mupload-file-list .s-file {
    float: left;
    font-size: 12px;
    margin-bottom: 16px;
    margin-right: 24px;
    width: 243px; }


/*# sourceMappingURL=main.css.map */
.error {
    border: 1px solid #2cc84d;
}

.audio-component {
    margin-top: 100px;
    margin-bottom: 20px;
}

.pdf-component {
    margin-top: 50px;
}

.bootbox-accept {
    background-color: #009ace !important;
    color: #FFFFFF !important;
    border: none;
}

.bootbox-cancel {
    background-color: #FFFFFF !important;
    color: #000000 !important;
    border: none;
}

.btn-delete-message {
    background-color: #FFFFFF !important;
    color: #000000 !important;
    border: none;
}

.btn-cancel-message {
    background-color: #009ace !important;
    color: #FFFFFF !important;
    border: none;
}

.sselect-wrapper .SelectClass, .sselect-wrapper .SumoUnder {
    visibility: inherit;
}
.loading-process {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100vh;
  /* background-color: rgba(0, 0, 0, 0.1); */
  z-index: 101;
}

.load-more-loading {
    background-image: url(../images/icon-loading-b.svg);
    background-size: 48px;
    background-repeat: no-repeat;
    background-position: 16px center;
    width: 64px;
    height: 64px;
    margin: 0 auto;
}
.loading-z-index {
  z-index: 100;
  position: relative;
}
#toast-container .toast-info{
    background-size: 0 0;
    padding-left: 20px !important;
}

/* Msg error */
.errorlist {
  color: #2CC84D !important;
  list-style: none;
}

.errorlist {
  list-style: none;
  padding: 8px 0;
  font-size: 8px;
}

.error-border, .form-group input.form-control.error-border:focus {
  border: 1px solid #2CC84D !important;
}
/* End msg error */

/* Color, font-size */
/* :root{
  --soremo-black: #000000;
  --black2-color: #53565A;
  --grey1-color: #A7A8A9;
  --grey2-color: #D3D3D3;
  --grey3-color: #F0F0F0;
  --white-color: #FFFFFF;
  --blue-color: #009ACE;
  --blue-color-hover: #0076A5;
  --background-color: #FCFCFC;
  --error-color: #2CC84D;
} */
/* End color, font-size  */

/* Footer */
footer .footer-logo {
    position: relative;
    z-index: 3;
    margin: 35px 0 0;
    padding: 102px 20px 60px;
}

.skeleton {
    opacity: 0.7;
    animation: skeleton-loading 1s linear infinite alternate;
}

@keyframes skeleton-loading {
    0% {
        background-color: hsl(200, 20%, 70%);
    }
    100% {
        background-color: hsl(200, 20%, 95%)
    }
}

.footer-logo p {
    color: #fff;
    font-size: 11px;
    line-height: 17px;
    margin-top: 17px;
}
/* End footer */

/* Toast */
.toast.toast-error {
    background-color: var(--error-color) !important;
    color: #fff !important;
    border-radius: 6px;
}

.toast {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'Noto Sans Japanese', 'sans-serif' !important;
    font-size: 13px !important;
    background-color: #fff !important;
    color: #000 !important;
    border-radius: 6px;
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.1));
    opacity: 1 !important;
}

.toast .toast-message, .toast .toast-title  {
  text-align: left !important;
}

#toast-container .toast {
    background-image: none !important;
    padding: 15px;
}
/* End toast */

.cannot-check {
    cursor: default !important;
}

.break-line {
  white-space: pre-line;
  line-break: anywhere;
}

.icon-bookmark-navbar {
  position: relative;
}

.icon-bookmark-navbar:after {
  opacity: 0;
  content: '';
  width: 2px;
  height: 2px;
  background: var(--bg-album);
  border-radius: 4px;
  bottom: -80px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  transition: 0s;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.icon-bookmark-navbar.showing:after {
  opacity: 1;
  content: '';
  width: 48px;
  height: 48px;
  border-radius: 4px;
  bottom: -70px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  transition: 0.4s ease-in-out;
}

.icon-bookmark-navbar.add:after {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  bottom: 6px;
  position: absolute;
  z-index: 9999;
  transition: all 0.4s ease-in-out, transform 0.2s, left 0.2s;
}

.DM-box-container .maction {
  background: transparent;
  border: none;
}

@media (min-width: 992px) {
  .DM-box-container .mcommment{
    position: fixed !important;
    z-index: 999;
    width: 100%;
    bottom: 32px;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
  
  .DM-box-container .button-add-DM, 
  .DM-box-container .button-accept-DM {
    bottom: calc(100% + 19px);
  }
}

/*
- edited at: 10/07/2023
- remove margin left and right all screen
- set width 100%
- set background size to 100%
*/
@media (min-width: 768px) {
  .container {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  /* .pbanner__image {
    background-size: 100% !important;
    background-position: center !important;
  } */

}

.d-none-header {
    display: none;
}

.d-block-header {
    display: block;
}

.mt-0-container {
    margin-top: 0 !important;
}

.sticky {
  position: fixed;
  top: 0;
  width: calc(100% - 30px);
  z-index: 1000
}

.pd-main-message {
  padding-bottom: 246px !important;
}

.project-item__content .project-tab.active {
  width: 100%;
}