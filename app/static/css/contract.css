@charset "UTF-8";
/* CSS Document */


/*
------- contract -------*/
.contract h2 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    /* font-size: 1.5rem; */
    font-style: normal;
    font-weight: normal;
    font-size: 24px;
    line-height: 150%;
    letter-spacing: -0.327273px;
    color: #000000;
    margin-top: 0px !important;
    padding-top: 0px !important;
}

.contract h3 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    line-height: 150%;
    padding-top: 40px !important;
    margin-top: 0px !important;
    letter-spacing: -0.245455px;
    color: #000000;

}

.contract h4 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 1rem;
    font-weight: normal;
}

.contract h5 {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 0.8125rem;
}

.contract p {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    padding-bottom: 40px;
}

.contract li, .contract a {
    list-style-type: none;
    list-style-position: outside;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
}

/* table */

.contract table {
    border-collapse: collapse;
    width: 100%;
}

.contract table tr td{
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}

.contract th, .contract td {
    padding: 1rem 1rem;
    border-bottom: 1px solid #ddd;
}

th {
    color: #53565A;
    background: #fcfcfc;
}


.contract {
    width: clamp(325px, 100%, 1000px);
    padding: 0px 5px 0px;
    text-align: justify;

}

.contract h2 {
    margin: 5rem auto 0.125rem;
    padding: 1.875rem 0 0.125rem;
    text-align: justify;
}

.contract h3 {
    margin: 1.5rem auto 0.125rem;
    padding: 1rem 0 0.125rem;
    text-align: justify;
}

.contract p + h3, .contract ol + h3 {
    border-top: thin solid #A7A8A9;
}

.contract ol li {
    list-style-type: none;
    list-style-position: inside;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}

.contract ol p{
    padding-bottom: 16px;
}

.contract ol li ol {
    padding-bottom: 0px;
}

.contract ol {
    padding: 16px 0px 40px 0px

}

.contract a:link, .blog a:visited, .blog a:hover, .blog a:active {
    color: #333333;
}


li > ol {
    list-style-position: inside;
}

.imgTermOfService img{
    background: #009ACE;

}

.banner {
    height: 235px;
    margin-bottom: 0px !important;
}

.bannerTerm {
    background: #ffffff;
    width: clamp(325px, 100%, 1000px);
    height: 235px;
    padding: 0px 5px 0px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    box-shadow: none;
}

.bannerTerm h2 {
    font-style: normal;
    font-weight: normal;
    font-size: 40px;
    line-height: 150%;
    color: #000;
}

/* Mobile */
.table-mobile {
    border-bottom: thin solid #A7A8A9;
    display: none;
}

.table-mobile ol li p {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-style: normal;
    font-weight: bold;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
    padding: 16px 0px 8px 0px !important;
}

.table-mobile ol li {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-style: normal;
    font-weight: 300;
    font-size: 13px;
    line-height: 150%;
    letter-spacing: 0.168824px;
    color: #000000;
}

.table-mobile ol {
    padding: 16px 0px 40px 0px !important;
    border-bottom: 1px solid #ddd;
}

.table-mobile ol:last-child {
    border-bottom: none;
}


@media (max-width: 992px) {
    .contract table {
        display: none !important;
    }

    .table-mobile {
        display: block !important;
    }
}

/* PDF印刷向け */


@media print {
    @page {
        A4: landscape;
        margin: 0;
    }

    * {
        -webkit-print-color-adjust: exact;
    }

    header, footer {
        display: none;
    }


    body {
        margin: 0;
        padding: 0;
    }

    section {
        margin: 0;
        box-shadow: none;
        page-break-before: always;
    }
}
