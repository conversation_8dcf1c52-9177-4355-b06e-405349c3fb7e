/* .chapter__modal-container .popup__text {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0;
    padding-left: 20px;
    padding-top: 20px;
    border-radius: 10px;
} */

/* .chapter__modal-container .popup__text .modal-header{
    display: flex;
    border-bottom: none;
    padding-top: 0;
    padding-left: 0;
} */

/* .chapter__modal-container .button-container{
    margin-right: 24px;
    margin-left: auto;
} */

.chapter__modal-container .button-container .btn-link{
    color: #009ACE;
    border: 1px solid;
    margin-right: 10px;
}

.chapter__modal-container .button-container .btn-link:hover{
    text-decoration: none;
}

.chapter__modal-container .button-container .btn-primary{
   background: #009ACE;
}

/* .chapter__modal-container .popup__text .modal-header .smodal-close i{
    font-size: 18px;
    color: black;
    margin-right: 5px;
} */

/* .chapter__modal-container form .form-group{
    margin-right: 20px;
} */

/* .chapter__modal-container .popup__text h1 {
    font-size: 2rem;
    font-weight: 600;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 2rem;
    text-transform: uppercase;
    color: #0a0a0a;
    color: #000;
    margin-top: 0;
    flex-grow: 1;
} */

/* .chapter__modal-container .popup__text h1 {
    font-size: 16px;
    font-weight: 400;
    color: #000;
    margin-top: 0;
    flex-grow: 1;
} */

.chapter__modal-container .popup-inner {
    width: clamp(320px, 80vw, 756px);
    height: auto;
    border-radius: 12px;
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    background: rgba(255, 255, 255, 1.0);
    border: 1px solid var(--soremo-border);
    padding: 32px 16px;

    /* width: 550px; */
    /* height: 250px; */
    /* border-radius: 10px; */
    /* overflow-y: hidden; */

}

/* .chapter__modal-container .popup__text p {
    border-radius: 10px;
} */

/* .chapter__modal-container  input {
    font-size: 1.5rem;
    outline: #D3D3D3;
    border: 1px solid #D3D3D3;
    border-radius: 5px;
} */

/* .chapter__modal-container input {
    width: 95%;
} */

/* @media (max-width: 992px) {
    .chapter__modal-container  .popup:target .popup-inner {
        width: 90%;
        height: 240px;
        max-width: 400px;
    }

    .chapter__modal-container  .button-container {
        margin-right: 16px;
        text-align: right;
        margin-left: auto;
        min-width: 300px;
    }
} */
