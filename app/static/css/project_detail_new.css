@charset "utf-8";

/* ローカルのフォントを読み込む */
/* @font-face {
    font-family: "A+mfCv-AXISラウンド 50 L StdN";
    src: url('../../fonts/AxisRound50StdN-L.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 R StdN";
    src: url('../../fonts/AxisRound50StdN-R.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 M StdN";
    src: url('../../fonts/AxisRound50StdN-M.otf') format('opentype');
}

@font-face {
    font-family: "A+mfCv-AXISラウンド 50 B StdN";
    src: url('../../fonts/AxisRound50StdN-B.otf') format('opentype');
} */

/* body {
    font-family: "A+mfCv-AXISラウンド 50 L StdN", "M PLUS 1p", sans-serif;
    background-color: #FFFFFF !important;
} */

html {
    font-size: 16px !important;
}

body .dropdown-backdrop {
    position: unset;
}

#mColumnWrap {
    position: inherit;
    height: 100%;
}

.column-list-offer {
    position: relative;
    margin-top: 0;
}

.mcolumn--right {
    margin-top: 0;
    border-left: 1px solid #f0f0f0;
}

.column-list-offer .resize-handle {
    width: 4px;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    background: #f0f0f0;
}

.column-list-offer .resize-handle:hover {
    cursor: col-resize;
}

.prdt .container {
    padding: 0;
}

.project-item__content {
    /* padding-left: 15px; */
    /* padding-right: 15px; */
    position: absolute;
    top: 179px;
    width: 100%;
}

.project-item__content.refactor{
    position: relative;
    top:0;
    width: 100%;
}

.project-item__content:has(.project-tab.project-tab-messenger.active) {
    padding-left: 0;
    padding-right: 0;
}

.new-banner-project {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #53565A;
    padding: 0 16px;
    height: 75px;
    position: fixed;
    z-index: 600;
    top: 64px;
    margin-top: 0;
    width: 100%;
}

/*change new banner project so it will no longer need to have position fixed*/
.new-banner-project-refactor {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #53565A;
    padding: 0 16px;
    height: 75px;
    position: relative;
    /*position: fixed;*/
    z-index: 600;
    /*top: 64px;*/
    margin-top: 0;
    width: 100%;
}

.bdropdown-menu.dropdown-menu {
    z-index: 200;
}

.block-project-left {
    flex: 1;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width:0;
}


.block-project-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    flex:0 0 auto;
    
}

.code-name-block {
    width: 100%;
}

.code-name-block .code-name {
    color: white;
    font-family: "corporate-logo-ver2", sans-serif;
    font-weight: 700;
    margin: 0;
    line-height: inherit;
    width: 100%;
    font-style: normal;
    font-size: clamp(30px, 22px + 2.5vw, 38px);
    height: auto;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width:0;
}

.back-button-block .calendar-schedule {
    color: #FFFFFF;
}

.back-button-block .calendar-schedule:hover {
    cursor: pointer;
    color: #009ace;
}

.back-list-project {
    display: flex;
}

.back-list-project .icon-back-list-project {
    color: #fff;
}

.block-user-avatar {
    display: flex;
    border-radius: 11px;
    border: 1px solid #f0f0f0;
    padding: 4px calc(4px + 4px) 4px 4px;
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13px);
    -webkit-backdrop-filter: blur(13.0px);
    align-items: center;
    justify-content: flex-start;
}

.block-user-avatar:hover {
    cursor: pointer;
    border: 1px solid #009ace;
    outline: 1px solid #009ace;
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10);
}

.block-user-avatar .c-vertical-line {
    width: 1px;
    height: 16px;
    background-color: #f0f0f0;
    margin: 0 4px 0 8px;
}
.block-user-avatar .avatar-user-project {
    background-color: #fff;
    width: 20px;
    height: 20px;
    /* box-shadow: 2px 4px 10px 0 #E5E5E5; */
    border: 1px solid #fff;
    margin-right: -4px;
    border-radius: 8px;
    max-width: initial;
}

/* .material-symbols-rounded {
    font-variation-settings: 'FILL' 1,
    'wght' 600,
    'GRAD' 0,
    'opsz' 24;
} */

.back-button-block {
    display: flex;
}

.block-navigation-bar {
    display: flex;
    height: 80px;
    padding: 16px 16px 0px;
    position: fixed;
    bottom: 0;
    transition: bottom 0.2s ease;
    z-index: 500;
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13px);
    -webkit-backdrop-filter: blur(13px);
    margin-bottom: 0;
    width: 100%;
}

.navigation-bar {
    margin-inline: auto;
    display: flex;
    justify-content: space-between;
    /* justify-content: space-around; */
    width: clamp(320px, 100%, 640px);
    list-style-type: none;
    padding: 0;
}
.navigation-bar-custom {
    justify-content: space-around !important;
}
.navigation-bar-custom a{
    display: flex;
    align-items: start;
}

.nav-bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 96px;
    gap: 2px;
    color: #a7a8a9;
    transition: 0.2s;
}

.nav-bar-item:hover {

    .material-symbols-rounded {
        color: var(--soremo-blue);
    }

    color: var(--soremo-blue);
    scale: 1.2;
}



/* .material-symbols-rounded, .label-nav-bar-item {
    color: #a7a8a9;
    line-height: 100%;
} */


.link-nav-item:hover span {
    cursor: pointer;
    color: #009ace;
}

.link-nav-item.active {

    .label8 {
        color: #009ace;
    }

    .material-symbols-rounded {
        color: #009ace;
    }

}


.block-navbar-switch {
    display: flex;
    justify-content: center;
    width: 100%;
}

.navigation-top-app-bar {
    width: 100%;
    height: 45px;
    position: absolute;
    top: 0px;
    padding: 8px 16px;
    z-index: 300;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    pointer-events: none;
    transition: top 0.2s;
}

.navigation-top-app-bar > * {
    pointer-events: auto;
}

.navbar-active {
    border: 1px solid #F0F0F0;
    cursor: pointer;
    background-color: #a7a8a9;
    color: #fff;
}

 #left-sidebar-open,
 #left-sidebar-close {
    visibility: visible;
    transition: 0.2s;
}

.border-siderbar {
    border-right: 1px solid #f0f0f0; 
}

#left-sidebar {
    overflow-y: auto;
    position: absolute;
    left: clamp(-375px * 0.5 + 16px, -20% , -640px);
    transition: 0.2s ease-out;
    height: calc(100dvh - 75px);
    padding: 12px 0px 80px 0px;
    flex-direction: column;
    align-items: flex-start;
    display: flex;

    border-right: 1px solid var(--soremo-border);
    z-index: 400;
}

.content-left-sidebar {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.content-left-sidebar ul {
    padding: 0;
}

.content-left-sidebar ul li span {
    padding-right: 4px;
}

#left-sidebar.toggled {
    width: clamp(375px * 0.5, 20%, 640px) ;

    left: 0;
    transition: 0.2s ease-out;
}

#left-sidebar.closing {
    transition: 0.2s ease-out; /* アニメーションの設定 */
    left: clamp(-375px * 0.5 + 16px, -20% , -640px); /* サイドバーが隠れる位置 */
}



.c-segment-control {
    width: clamp(320px - 24px, 100% - 80px, 640px);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fcfcfc;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    color: #a7a8a9;
    padding: 0;
    margin: 0;
}

.c-segment-control li {
    list-style: none;
    width: 50%;
    text-align: center;
    border-radius: 6px;
    padding: 4px 0;
    margin: 1px 1px;
    line-height: 100%;
    transition: 0.2s;
    font-size: 16px;
}

.c-segment-control li:hover {
    cursor: pointer;
    background-color: #a7a8a9;
    color: #fff;
}

#left-sidebar ul {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    padding: 4px 0 4px 0;
    list-style: none;
    margin: 0;
}

/* #left-sidebar .chapter-list { */
    /* border-top: 1px solid #F0F0F0; */
    /* padding-bottom: 16px; */
/* } */

/* #left-sidebar .block-label-1 {
    padding-bottom: 16px;
} */

#left-sidebar ul li {
    color: #000;
    font-size: 13px;
    line-break: anywhere;
    padding-left: 16px;

}

#left-sidebar ul .sidebar-label {
    font-size: 11px;
    color: #a7a8a9;
    padding-left: 16px;
}

.block-label-1 li, .chapter-list li {
    width: 100%;
    padding: 4px 0px 4px 0px;
    display: flex;
    align-items: center;
    list-style: none;
    font-size: clamp(13px, 10px + 0.94vw, 16px);
}

.block-label-1 li:hover, .chapter-list li:hover {
    cursor: pointer;
    background-color: #f0f0f0;
}

.block-setting {
    border-top: 1px solid #f0f0f0;
    width: 100%;
    /* padding-top: 8px; */
}

.block-setting .list-setting {
    list-style: none;
}

.block-setting .list-setting li {
    display: flex;
    align-items: center;
    padding: 4px 0px 4px 0px;
    width: 100%;
}

.block-setting .list-setting li:hover {
    cursor: pointer;
    background-color: #f0f0f0;
}
#left-sidebar:not(.toggled) .content-left-sidebar {
    display: none;
}

.wallet-pl .show-file-icon {
    border: none;
}

@media (max-width: 767px) {
    .block-label-1, .chapter-list {
        font-size: 16px;
    }

    .new-banner-project {
        padding-left: 8px;
        padding-right: 8px;
    }
}

.wallet-pl {
    display: flex;
    /* padding-right: 8px; */
    cursor: pointer;
}

.wallet-pl .show-file-icon {
    font-size: 24px !important;
    margin: 0;
}

.wallet-pl .balance-wallet-icon, .wallet-pl .show-file-icon {
    display: none;
}

.wallet-pl .balance-wallet-icon:hover, .wallet-pl .show-file-icon:hover {
    color: #009ace;
}

.footer-comment-block {
    padding: 0 0 0;
    position: fixed;
    bottom: 80px;
    transition: all 0.3s ease;
    margin-inline: auto;
    z-index: 100;
    left: 50%;
    width: clamp(320px, 100%, 740px);
    max-width: clamp(320px, 100%, 740px);
}

.action-panel-head {
    display: flex;
    margin: 0;
    gap: 16px;
    justify-content: center;
    align-items: flex-end;
    /*border-radius: 6px 6px 6px 6px;*/
    /*background: rgba(255, 255, 255, 0.16);*/
    /*box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);*/
    /*backdrop-filter: blur(13px);*/
    /*margin-bottom: 8px;*/
    /*display: flex;*/
    /*flex-direction: column;*/
    /*align-items: center;*/
    /*justify-content: center;*/
    /*width: clamp(320px, 100%, 740px);*/
    /*margin-inline: auto;*/
    transition: width 0.3s ease-in-out;
    width: 100%;
}
.action-panel-head-custom {
    margin-left: 16px;
}

.action-panel-head-custom .offer-block-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    position: relative;
}

.action-panel-head-custom .offer-block-left .offer-btn {
    width: 192px;
    padding: 8px 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    color: #a7a8a9;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
    box-shadow: 2px 4px 10px 0px rgba(167, 168, 169, 0.15);
    transition: 0.2s;
    gap: 8px;
    margin-bottom: 48px;
}

.action-panel-head-custom .offer-block-left .offer-btn:hover {
    cursor: pointer;
    transform: scale(1.02);
    transform-origin: center;
    background-color: #009ace;
    border: 1px solid #009ace;
    color: #fff;
    box-shadow: 0px 0px 32px 0px #009ACE;
}

.action-panel-head-custom .offer-block-left .offer-btn:hover .mail-offer-btn, .action-panel-head-custom .offer-block-left .offer-btn:hover .txt-offer-btn {
    color: #fff;
}

/*.action-panel-head .offer-block-left .offer-btn:hover .txt-offer-btn {*/
/*    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;*/
/*}*/

.action-panel-head-custom .offer-block-left .offer-btn .mail-offer-btn {
    font-size: 48px;
    color: #a7a8a9;
}

.action-panel-head-custom .offer-block-left .offer-btn .txt-offer-btn {
    color: #a7a8a9;
    font-size: 13px;
    /*font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;*/
}

.action-panel-head .offer-block-right {
    border-radius: 6px 6px 6px 6px;
    background: rgba(255, 255, 255, 0.16);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(13px);
    margin: 0 0 4px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-inline: auto;
    border-top: none;
    padding: 8px;
}

.action-panel-head .offer-block-right .block-right-content {
    width: clamp(320px, 100%, 740px);
    padding: 8px 8px 0px 8px;
}

.action-panel-head .offer-block-right .block-right-content .comment-block {
    width: 100%;
    padding: 8px 0;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    position: relative;
    margin: 0;
    height: auto;
    border-top: none;
    background-color: rgba(255, 255, 255, 0.16);
}

.action-panel-head .offer-block-right .block-right-content .comment-block .c-icon-attach {
    position: absolute;
    left: 12px;
    bottom: 16px;
    transition: 0.2s;
    color: #a7a8a9;
}

.action-panel-head .offer-block-right .block-right-content .text-offer {
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-weight: normal;
    font-size: clamp(13px, 10px + 0.94vw, 16px);
    line-height: 200%;
    color: initial;
}

.action-panel-head .offer-block-right .block-right-content hr {
    border: none;
    height: 1px;
    background-color: #f0f0f0;
    margin: 8px 0;
}

.action-panel-head .offer-block-right .block-right-content .block-offer-btn {
    display: none;
}

/*.action-panel-head .offer-block-right .block-right-content .btn-accept-offer {*/
/*    opacity: 1;*/
/*    gap: 16px;*/
/*    display: flex;*/
/*    align-items: center;*/
/*    justify-content: center;*/
/*    width: 100%;*/
/*    padding: 24px 16px;*/
/*    color: white;*/
/*    background: #009ace;*/
/*    border: 1px solid #009ace;*/
/*    border-radius: 6px;*/
/*    transition: 0.2s;*/
/*    font-family: 'A+mfCv-AXISラウンド 50 R StdN',*/
/*    'M PLUS 1p',*/
/*    sans-serif;*/
/*    font-style: normal;*/
/*    font-feature-settings: 'clig' off, 'liga' off;*/
/*    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);*/

/*}*/

/*.action-panel-head .offer-block-right .block-right-content .btn-accept-offer:hover {*/
/*    color: #fff;*/
/*    cursor: pointer;*/
/*    background-color: #0076A5;*/
/*    outline: #0076A5;*/
/*    border: 1px solid #0076A5;*/
/*    scale: 1.005;*/
/*}*/

.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .info-offer, .action-panel-head .offer-block-right .block-right-content .btn-accept-offer .description-offer {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    list-style: none;
    margin: 0;
    padding: 0;
}

.action-panel-head .btn-accept-offer .icon-offer, .action-panel-head .btn-accept-offer .text-content-left {
    align-self: center;
    color: #fff !important;
}

.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .info-offer .name-offer {
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
}

.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .description-offer {

}

.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .li-offer {
    display: flex;
    color: #fff;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    list-style: none;
}

.li-gap-4 {
    gap: 4px;
}

.li-gap-8 {
    gap: 8px;
}


.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .description-offer .text-des-offer {
    /*font-family: 'A+mfCv-AXISãƒ©ã‚¦ãƒ³ãƒ‰ 50 R StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-weight: normal;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.action-panel-head .offer-block-right .block-right-content .btn-accept-offer .description-offer .time-des-offer {
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
}

.block-search-offer {
    padding: 8px 8px 8px;

    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: clamp(13px, 10px + 0.94vw, 16px);
    line-height: 200%;
    position: relative;
}

/* .block-search-offer .c-icon-search {
    color: #a7a8a9;
    position: absolute;
    left: 8px;
} */

.block-search-offer .input-search-offer {
    /*
    padding-left: 36px;
    */
    width: 100%;
    padding: 12px 40px 12px 36px;
    margin: 0;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    font-size: 16px;
    /*font-family: 'A+mfCv-AXISãƒ©ã‚¦ãƒ³ãƒ‰ 50 L StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-weight: normal;
    line-height: 200%;
    background-color: unset;
}

.block-search-offer .input-search-offer::placeholder {
    color: #f0f0f0;
}

.block-search-offer .input-search-offer:focus {
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10) !important;
    outline: none;
    border-color: #009ace;
}

.block-search-offer .c-icon-close-small {
    display: block;
    color: #a7a8a9;
    position: absolute;
    right: 12px;
    top: 18px;
    transition: 0.2s;
    transform: unset;
    cursor: pointer;
}

.mcolumn-content .custom-list-wrap {
    padding: 0;
    height: 100%;
}

.martist .mrow-custom {
    border: unset;
    margin-top: 0;
    border-radius: unset;
}

/*#projectItemDetail {*/
/*    position: relative;*/
/*    width: 100%;*/
/*    top: 179px;*/
/*}*/

/*list offer style    */

.project_offers {

}

ul, li {
    list-style: none;
}

.list-offer {
    margin: 0;
    padding: 0;

}

.list-offer .item-offer {
    display: flex;
    border-top: 1px solid #f0f0f0;
    background-color: #FFFFFF;
    height: inherit;
    border-radius: initial;
    padding: initial;
    flex-direction: initial;
    margin-bottom: 0;
}

.list-offer .item-offer.mactive {
    background-color: #F0F0F0;
}

.list-offer .item-offer:not(.mactive):hover {
    background-color: #FCFCFC;
    cursor: pointer;
}

.list-offer .item-offer:last-child {
    border-bottom: 1px solid #f0f0f0;
}

.list-offer .item-offer .offer-main {
    width: 100%;
    position: relative;
    padding: 8px 0px 8px 8px;
    display: flex;
    /*border: 1px solid #f0f0f0;*/
    gap: 4px;
}

.list-offer .item-offer .offer-main-item-2 {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.list-offer .item-offer .offer-main-item-2 .offer-main-content {
   width: 100%;
}

.list-offer .item-offer .offer-main-item-2 .offer-content-bottom {
    display: flex;
    gap: 4px;
    align-items: center;
}

.list-offer .item-offer .offer-main-item-2 .block-offer-top {
    width: 100%;
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0px 0px 0px 26px;
}

.list-offer .item-offer .offer-main-item-2 .block-offer-top .offer-top-avatar {
    display: flex;
}

.list-offer .item-offer .offer-main-item-2 .block-offer-top .text-bottom-offer {
    padding-top: 8px;
    padding-bottom: 8px;
    width: 100%;
    /*font-family: "A+mfCv-AXISラウンド 50 L StdN", "M PLUS 1p", sans-serif;*/
    font-weight: normal;
    font-size: 11px;
    color: #a7a8a9;
    display: flex;
    height: 27px;
    align-items: center;
}


.list-offer .item-offer .offer-main-item-2 .block-offer-top .offer-top-avatar .top-avatar {
    min-width: 24px;
    min-height: 24px;
    width: 24px;
    height: 24px;
    background-size: cover;
    border-radius: 10px;
    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

.list-offer .item-offer .offer-main .main-avatar {
    min-width: 48px;
    min-height: 48px;
    width: 48px;
    height: 48px;
    border-radius: 18px;
    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
    background-size: cover;
}

.list-offer .item-offer .progress-offer {
    background-color: black;
    width: 4px;
    height: auto;
}

.list-offer .item-offer .content-offer {
    /*padding: 8px 0 8px 12px;*/
    width: 100%;
    align-items: center;
    display: flex;
    gap: 8px;
}

.item-offer .grade-icon {
    visibility: visible;
    color: #FFF000;
    text-shadow: 0px 0px 4px rgba(249, 248, 113, 0.50);
    display: flex;
    justify-content: center;
    align-items: center;
}

.item-offer .content-offer .block-offer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 8px;
    flex: 1;
}

.item-offer .content-offer .block-offer .block-offer-1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.item-offer .content-offer .block-offer .block-offer-1 .title-offer {
    flex: 1;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;
    font-style: normal;
    font-size: 13px;
    line-height: 100%;
    letter-spacing: 2.5px;
    color: black;
    word-wrap: break-word;
}

.title-blue {
    color: #009ace !important;
}

.item-offer .content-offer .block-offer .block-offer-1 .time-offer {
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;*/
    font-weight: normal;
    font-size: 8px;
    line-height: 100%;
    color: #a7a8a9;
}

.item-offer .content-offer .block-offer .block-offer-2 {
    width: 100%;
    gap: 6px;
    flex-wrap: wrap;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.offer-main-item-2 .block-offer-2 .block-text-description {
    line-height: 100%;
    /*font-family: 'A+mfCv-AXISãƒ©ã‚¦ãƒ³ãƒ‰ 50 L StdN', 'M PLUS 1p', sans-serif;*/
    font-weight: normal;
    font-size: 11px;
}

.offer-main-item-2 .block-offer-2 .block-text-description .text-des-1 {
    color: black !important;
}

.offer-main-item-2 .block-offer-2 .block-text-description .text-des-2 {
    color: #000000;
}

.block-offer-2 .description-content-offer {
    line-height: 100%;
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;*/
    font-weight: normal;
    font-size: 11px;
    color: #a7a8a9;
}

.block-offer-2 .block-avatar-user {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    /*font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;*/
    font-weight: normal;
    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 200%;
}

.block-offer-2 .block-avatar-user .avatar-user-offer {
    width: 24px;
    height: 24px;
    border-radius: 10px;
    border: 1px solid #fff;
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

.right-offer {
    display: flex;
    align-items: center;
    min-width: 40px;
    width: 40px;
}

.right-offer .number-offer {
    width: 16px;
    height: 16px;
    background-color: #009ace;
    color: #FFF;
    border-radius: 50%;
    /*font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;*/
    font-style: normal;
    font-size: 8px;
    line-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: visible;
    margin-left: 0;
    padding: 0;
}

/*.right-offer .number-offer.hide-number-offer {*/
/*    visibility: hidden;*/
/*}*/

.right-offer .next-offer-icon {
    color: #a7a8a9;
    visibility: hidden;
}

.item-offer.mactive .right-offer .next-offer-icon {
    visibility: visible;
}

.item-offer:not(.mactive):hover .right-offer .next-offer-icon {
    color: #f0f0f0;
    visibility: visible;
}

.avatar-ml-8 {
    margin-left: -8px;
}

.ml-8 {
    margin-left: 8px;
}

.dot-more-user {
    padding-left: 5px;
    height: 16px;
    align-items: center;
    display: flex;
    color: #fff;
}

.hidden-el {
    visibility: hidden !important;
}

#modalInfoProject {
    z-index: 9999;
    display: none;
}

.content-info-project {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    width: clamp(320px, 80vw, 672px);
    padding: 32px 16px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
    background: #FFF;
    /* Card drop shadow hover */
    box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);

    max-height: calc(100dvh - 32px);
    overflow-y: auto;

}

.content-info-project p {
    margin: 0;
}

.content-info-project .title-info-project {
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: "A+mfCv-AXISラウンド 50 R StdL";
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    letter-spacing: 2.5px;
}

.content-info-project .content-info {
    padding: 16px 0;
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: "A+mfCv-AXISラウンド 50 L StdL";
    font-size: 13px;
    line-height: 200%;
    text-align: justify;
}

.content-info-project .block-info-project {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-info-project .block-info-project {
    display: flex;
    align-items: center;
    place-content: space-between;
    margin-top: 8px;
    width: 100%;
}

.content-info-project .block-info-project .txt-info-project {
    color: #000;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: "A+mfCv-AXISラウンド 50 L StdL";
    font-size: 13px;
    line-height: 200%;
}

#modalInfoProject .hr-info-project {
    margin: 8px 0;
    border-top: #F0F0F0;
    width: 100%;
}

.block-btn-info-project {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    align-self: stretch;
}

.block-btn-info-project .btn-info-project {
    display: flex;
    width: 128px;
    padding: 8px 24px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    /* Heading 13 */
    /*font-family: "A+mfCv-AXISラウンド 50 B StdL", serif;*/
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 200%; /* 26px */
    border-radius: 4px;
    background: #009ACE;
}

.content-info-project .block-info-project-first {
    margin-top: 0 !important;
}

@media (width > 640px) {
    .action-panel-head .offer-block-left {
        width: clamp(320px, 38.2vw, 1140px * 0.382);
        flex: 0 0 auto;
    }

    .prdt .mcolumn.mcolumn--left.column-list-offer {
        width: clamp(320px, 38.2vw, 1140px * 0.382);
        flex: 0 0 auto;
    }

}

    .pd-section__video.mscrollbar {
        gap: 16px 12px;

        /* grid-template-columns: repeat(auto-fill, minmax(364px, 1fr)) !important;
        grid-column-gap: 12px !important;
        row-gap: 16px; */
    }

/*@media (min-width: 1200px) {*/
/*    .pd-section__video.mscrollbar {*/
/*        grid-template-columns: repeat(auto-fill, minmax(364px, 1fr)) !important;*/
/*        grid-column-gap: 12px !important;*/
/*    }*/
/*}*/

/*@media (max-width: 768px) {*/
/*    .pd-section__video.mscrollbar {*/
/*        grid-template-columns: repeat(auto-fill, minmax(364px, 1fr)) !important;*/
/*        grid-column-gap: 8px !important;*/
/*    }*/
/*}*/

/* @media (max-width: 500px) {
    .pd-section__video.mscrollbar {
        grid-column-gap: 0 !important;
    } */

    /* .pd-chapter-list .pd-chapter__list.mscrollbar {
        grid-column-gap: 0 !important;
    } */
/* } */

/*.pd-section__video.mscrollbar {*/
/*    display: grid;*/
/*    grid-template-columns: repeat(auto-fill, minmax(364px, 1fr)) !important;*/
/*    grid-column-gap: 22px;*/
/*}*/

.pd-chapter-list {
    margin-bottom: 80px;
}


.pd-chapter-list .pd-chapter__list.mscrollbar {
    display: flex;
    flex-wrap: wrap;
    gap: 12px 16px;

    /* display: grid; */
    /* grid-template-columns: repeat(auto-fill, minmax(248px, 1fr)) !important; */
    /* grid-column-gap: 12px !important;
    row-gap: 16px; */
}

/* .pd-section--delivery-video .pd-section__video.mscrollbar { */
    /* display: grid; */
    /* grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important; */
    /* display: flex; */
    /* flex-wrap: wrap; */
/* } */

.pd-section--delivery-video .pd-section__video .cvideo.cvideo__thumb-list-delivery {
    padding: 0;
    width: 100%;
    /*max-width: initial !important;*/
}

.pd-section--delivery-video .pd-section__video .cvideo.cvideo__thumb-list-delivery .project-delivery-item-content .cvideo__thumb {
    height: auto;
}

.project-delivery-item-content.item-delivery-video {
    width: 100%;
    padding: 16px 12px 16px;
    margin-bottom: 32px;

    border-radius: 6px;
    border: 1px solid var(--soremo-border);
    background-color: #fff;

    /* Card drop shadow */
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);

    /* ！！兄弟要素の数に応じた上限設定が必要 */

    @media (width > 960px) {
        width: calc(50% - 16px / 2);
    }

    @media (width > 960px) {
        width: calc(100% / 2 - 16px / 2);
    }

    @media (width > 1440px) {
        width: calc(100% / 3 - 32px / 3);
    }
}

.item-disabled {
    pointer-events: none !important;
}

body .prdt {
    margin-top: 0;
}

/*them class nay de hien thi nut offer*/
.show-on-offer {
    display: block !important;
}

.offer-block-right .mcomment-new {
    background-color: rgba(255, 255, 255, 0.16);
    width: 100%;
}

.offer-block-right .mcomment-new:not(.border-editing) {
    border-top: none;
}

.offer-block-right .mcomment-new .mcomment-message .comment-top-area {
    display: inline-block;
    width: 100%;
    border-bottom: none;
}

/* .offer-block-right .mcomment-new .mcomment-message {
    position: relative;
} */

.mcomment-message .comment-top-area .mcomment-attached {
    padding: 0;
}
.offer-block-right .mcomment-new .mcomment-message .comment-top-area .mcomment-attached:has(.mattach-template.collection-item.item-template) {
    padding: 0 14px 4px 14px;
}

.comment-top-area .mattach-previews.collection {
    margin-top: 0;
}
.comment-top-area .mattach-previews.collection .mattach-template.collection-item {
    margin-top: 0;
}

.comment-top-area .mattach-previews.collection .mattach-template.collection-item.item-template .mattach-info {
    display: flex;
}
/*.offer-block-right .mcomment-new.border-editing .mcomment-message .comment-top-area .mcomment-attached {*/
/*    padding: 12px 14px;*/
/*}*/

.offer-block-right .mcomment-new.border-editing .mcomment-top.comment-top-area .mcomment-bottom-new #text-input-comment {
    border: unset;
}

.offer-block-right .mcomment-new .mcomment-message .comment-top-area .mcomment-input {
    padding: 0;
    justify-content: space-between;
}

.offer-block-right .mcomment-new .mcomment-message .comment-top-area .mcomment-input .mcomment-input-close {
    min-width: 21px;
    min-height: 21px;
}

.offer-block-right .mcomment-new .mcommment {
    width: 100%;
}

/*.offer-block-right .mcomment-new .mcomment-message .comment-top-area .mcomment-attached:not(:empty) {*/
/*    padding: 0;*/
/*}*/
.mcomment-new .comment-top-area .mcomment-bottom-new {
    padding: 0;
}

.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-send {
     color: #f0f0f0;
    font-size: 32px;
    /* position: absolute; */
    /* right: 12px; */
    transition: 0.2s;
    margin: 0;
    /* bottom: 12px; */
    transform: unset;
}


.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-send span {
    color: #f0f0f0;
    font-size: 32px;
    transition: 0.2s;
}


.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-send {
    /*transform: unset;*/
    margin: 0;
}

.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-send.active span {
    color: #009ace;
}

.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-action .mattach-label-new {
    display: flex;
}

.mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-action .mattach-label-new span:hover {
    cursor: pointer;
    color: #009ace;
    scale: 1.3;
}

.mcomment-top.comment-top-area .mcomment-bottom-new #text-input-comment {
    resize: none;
    width: 100%;
    padding: 12px 40px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin: 0;
    font-size: 16px;
    /* font-weight: normal; */
    /*font-family: 'A+mfCv-AXISãƒ©ã‚¦ãƒ³ãƒ‰ 50 L StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    /* height: 58px; */
    /* overflow: hidden; */
    line-height: 200%;
    color: black;
}

.offer-block-right .mcomment-new:not(.border-editing) .mcomment-top.comment-top-area .mcomment-bottom-new #text-input-comment:focus {
    box-shadow: 2px 4px 8px 3px rgba(0, 154, 206, 0.10) !important;
    outline: none;
    border-color: #009ace;
}


.mcomment-top.comment-top-area .mcomment-bottom-new #text-input-comment::placeholder {
    color: #f0f0f0;
}

.mcomment-top.comment-top-area .mcomment-bottom.mcomment-bottom-new .mcomment-action-new {
    /* position: absolute;
    left: 12px;
    bottom: 16px; */
    transition: 0.2s;
    color: #a7a8a9;
    padding: 0;
}

.offer-block-right .mcomment-new.border-editing .mcomment-top.comment-top-area .mcomment-bottom-new .mcomment-send {
    right: 4px;
    bottom: 14px;
}

.offer-block-right .mcomment-new.border-editing .mcomment-top.comment-top-area .mcomment-bottom.mcomment-bottom-new .mcomment-action-new {
    left: 12px;
    bottom: 16px;
    padding: 0;
}

body .action-panel-head .mcommment.mcomment-new {
    position: initial !important;
}

.project-chapter-video-undone:not(.cannot-check), 
.project-chapter-video-done:not(.cannot-check) {
    cursor: pointer;
}

.project-chapter-video-done span,
.project-chapter-video-undone span:not(.u-text-blue) {
    color: #f0f0f0 !important;
}

.mcomment-top.comment-top-area .mcomment-input-new {
    padding: 0 !important;
}

.messenger-file-component-container-new.btn-accept-offer {
    margin: 0;
    opacity: 1;
    gap: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    /*padding: 24px 16px;*/
    color: white;
    background: #009ace;
    border: 1px solid #009ace;
    border-radius: 6px;
    transition: 0.2s;
    /*font-family: 'A+mfCv-AXISラウンド 50 R StdN',*/
    /*'M PLUS 1p',*/
    /*sans-serif;*/
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    margin-bottom: 8px;
}

.messenger-file-component-container-new.btn-accept-offer:not(:has(.messenger-file-component-content)) {
    display: none;
}


.messenger-file-component-container-new.btn-accept-offer:hover {
    color: #fff;
    cursor: pointer;
    background-color: #0076A5;
    outline: #0076A5;
    border: 1px solid #0076A5;
    scale: 1.005;
}

.maction-new .messenger-file-component-container-new.btn-accept-offer:hover .messenger-file-component-content {
        background-color: #0076A5 !important;
        transition: 0.2s;
}

.messenger-file-component-container-new.btn-accept-offer .messenger-file-component-content {
    margin-bottom: 0;
    padding: 0;
    background-color: #009ace !important;
    transition: 0.2s;
}

.messenger-file-component-container-new.btn-accept-offer .messenger-file-component-content .content-middle {
    color: white;
}

.item-chapter-active, .item-chapter-active span {
    color: #009ace !important;
}

.prdt .pd-section-file.file-hide.active {
    display: none;
}


.prdt .pd-section--detail.pd-product-comment .pd-section__content {
    width: 100%;
    justify-content: center;
}

.prdt .pd-product-comment .pd-comment {
    max-width: clamp(320px, 100%, 756px);
    height: 100%;
}

.prdt .pd-section--detail.pd-product-comment .pd-section__content.active {
    width: calc(100% - 280px) !important;
    justify-content: center;
}

.prdt .mcolumn.DM-box-container.dm-block-message {
    display: flex;
    justify-content: center;
    border-right: none;
    border-left: none;
}

.prdt .mcolumn.DM-box-container.dm-block-message .offer-content-message {
    width: 100%;
    max-width: clamp(320px, 100%, 756px);
}

.prdt .pd-add-chapter-new {
    width: 100%;
}

/* .prdt .pd-add-chapter-new .add-chapter-icon {
    font-size: 64px;
} */

.prdt .pd-add-chapter-new .pd-add-chapter__text:hover {
    color: var(--soremo-blue);

    span {
        color: var(--soremo-blue);
    }
}

/* .prdt .pd-add-chapter-new .pd-add-chapter__icon {
    text-align: left;
} */

.prdt .pd-chapter__add.pd-chapter-new {
    z-index: 599;
    position: fixed;
    bottom: 72px;
    right: 16px;
    margin: 0;
}

.prdt .pd-chapter__add.pd-chapter-new .add-scene-chapter {
    font-size: 64px;
    color: #A7A8A9;
}

.prdt .pd-chapter__add.pd-chapter-new .pd-chapter__add-text {
    color: #A7A8A9;
    font-feature-settings: 'palt' on, 'clig' off, 'liga' off;
    font-size: 8px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%; /* 8px */
}

.prdt .btn-project-setting {
    color: #000;
    font-size: clamp(13px, 10px + 0.94vw, 16px);
    width: 100%;
    display: flex;
    align-items: center;
}

/* .prdt .btn-project-setting li {
    padding: 0;
} */

.prdt .btn-project-setting:hover {
        cursor: pointer;
    background-color: #f0f0f0;
}
@keyframes slideDown {
    0% {
        top: -100%;
        opacity: 0;
    }
    100% {
        top: 0;
        opacity: 1;
    }
}

.block-users-in-project {
    /* max-height: 592px; */
    /* max-height: calc(100dvh - 16px); */
    /* width: 359px; */
    width: min(100vw - 16px - 46px, 1140px * 0.382);
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    top: 2px;
    right: 46px;
    position: absolute;
    z-index: 1;
    margin-top: 8px;
    padding: 40px 12px 24px;

    border-radius: 12px 12px 12px 12px;

    /* From https://css.glass */
    background: rgba(255, 255, 255, 0.84);
    backdrop-filter: blur(13.0px);
    -webkit-backdrop-filter: blur(13.0px);

    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);


}

.modal-users-in-project.d-none-el .block-users-in-project {
    transform: translateY(-150%);
    transition: transform 0.5s ease;
    animation: slideDown 0.5s forwards;

}

.modal-users-in-project:not(.d-none-el) .block-users-in-project {
    transform: translateY(0);
    transition: transform 0.5s ease;
    animation: slideDown 0.5s forwards;
}

.d-none-el {
    /*display: none !important;*/
}

.content-users-in-project {
    margin: 8px 0 0 0;
    padding: 16px 8px 24px 8px;

    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    gap: 8px;
    overflow-y: auto;
    overflow-x: hidden;

}

.modal-users-in-project .block-owners {
    display: flex;
    padding: 12px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 4px;
    background: #009ACE;
}

.modal-users-in-project .block-center-users {
    display: flex;
    padding-left: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
}

.modal-users-in-project .block-center-users:hover{
    cursor: pointer;
}

.modal-users-in-project .block-center-users .txt-block-center {
    color: #A7A8A9;
    text-align: right;
    font-feature-settings: 'clig' off, 'liga' off;
    /* BodyText 13 comment */
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 150%; /* 19.5px */
    flex: 1 0 0;
}

.modal-users-in-project .block-center-users .icon-next-block-center {
    display: flex;
    font-size: 32px;
    justify-content: center;
    align-items: center;
}


.modal-users-in-project .block-producers {
    display: flex;
    padding: 12px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 4px;
    background: #A7A8A9;
}

.block-center-users {
    display: flex;
    padding-left: 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
}

.content-users-in-project .users-title-block {
    color: #FFF;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 200%; /* 26px */
}

.content-users-in-project .user-project-template-1 {
    display: flex;
    padding: 8px;
    align-items: center;
    align-self: stretch;
    border-radius: 4px;
    background: #FFF;
    /* min-height: 76px; */
}

.content-users-in-project .user-project-template-1 .block-user-project-avatar {
    display: flex;
    min-width: 50px;
    min-height: 50px;
    width: 50px;
    height: 50px;
    padding: 1px;
    justify-content: center;
    align-items: center;
    border-radius: 18px;
    position: relative;
}

.content-users-in-project .user-project-template-1 .block-user-project-avatar .user-project-avatar {
    min-width: 48px;
    min-height: 48px;
    width: 48px;
    height: 48px;
    flex-shrink: 0;
    border-radius: 18px;
    border: 1px solid #FFF;
    /* image shadow */
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

.content-users-in-project .user-project-template-1 .block-user-project-avatar .user-project-status {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 1px;
    bottom: 1px;
    color: #2CC84D;
    font-size: 8px;
}

.content-users-in-project .user-project-template-1 .item-project-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
    margin-left: 8px;
    max-width: calc(100% - 82px);
}

.content-users-in-project .user-project-template-1 .item-project-content .txt-1 {
    color: #000;
    text-align: left;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 13px */
    letter-spacing: 2.5px;
    margin-bottom: 0px;
}

.content-users-in-project .user-project-template-1 .item-project-content .block-txt-2 {
    display: flex;
    align-items: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
}

.content-users-in-project .user-project-template-1 .item-project-content .block-txt-2 .txt-2_1 {
    color: #000;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%; /* 11px */
    /*word-break: break-word;*/
    /*flex: 3;*/
    /*display: -webkit-box;*/
    /*-webkit-box-orient: vertical;*/
    /*-webkit-line-clamp: 2;*/
    /*overflow: hidden;*/
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-users-in-project .user-project-template-1 .item-project-content .block-txt-2 .txt-2_2 {
    color: #A7A8A9;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    /* BodyText 11 spacing */
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%; /* 11px */
    /*word-break: break-word;*/
    /*flex: 3;*/
    /*display: -webkit-box;*/
    /*-webkit-box-orient: vertical;*/
    /*-webkit-line-clamp: 2;*/
    /*overflow: hidden;*/
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-users-in-project .user-project-template-1 .item-project-content .txt-3 {
    color: #A7A8A9;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%; /* 11px */
    margin-bottom: 0;
    text-align: left;
    white-space: normal;
    word-wrap: break-word;
    width: 100%;
}

.content-users-in-project .user-project-template-1 .block-user-nav {
    display: flex;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
}

.content-users-in-project .user-project-template-1 .block-user-nav .users-project-next-icon {
    font-size: 24px;
    color: #A7A8A9;
}

.content-users-in-project .user-project-template-2 {
    display: flex;
    padding: 8px 8px 8px 24px;
    align-items: center;
    align-self: stretch;
    border-radius: 4px;
    background: #FFF;
}

.content-users-in-project .user-project-template-2 .user-project-avatar {
    display: flex;
    width: 32px;
    height: 32px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 13px;
    border: 1px solid #FFF;
    /* image shadow */
    box-shadow: 2px 4px 10px 0px #E5E5E5;
}

.content-users-in-project .user-project-template-2 .block-user-project-avatar {
    position: relative;
    min-width: 32px;
    min-height: 32px;
    width: 32px;
    height: 32px;
}

.content-users-in-project .user-project-template-2 .item-project-content {
   display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
    margin-left: 8px;
    max-width: calc(100% - 64px);
    text-align-last: left;
}

.content-users-in-project .user-project-template-2 .item-project-content .template-2-txt-1 {
    color: #000;
    text-align: left;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 100%; /* 13px */
    letter-spacing: 2.5px;
    margin-bottom: 0;
    white-space: normal;
    word-wrap: break-word;
    width: 100%;
}

.content-users-in-project .user-project-template-2 .item-project-content .template-2-txt-2 {
    color: #000;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    /* BodyText 11 spacing */
    font-size: 11px;
    font-style: normal;
    font-weight: 300;
    line-height: 100%; /* 11px */
    margin-bottom: 0;
    width: 100%;
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.content-users-in-project .user-project-template-2 .block-user-project-avatar .user-project-status {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 1px;
    bottom: 1px;
    color: #2CC84D;
    font-size: 8px;
}

.content-users-in-project .user-project-template-2 .block-user-nav {
    display: flex;
    width: 24px;
    height: 24px;
    justify-content: center;
    align-items: center;
}

.content-users-in-project .user-project-template-2 .block-user-nav .users-project-next-icon {
    font-size: 24px;
    color: #A7A8A9;
}

.block-invite-bottom {
    display: flex;
    padding-left: 8px;
    justify-content: end;
    align-items: center;
    align-self: stretch;
}

.block-invite-producer {

}

.block-invite-bottom .txt-block-center {
    color: #A7A8A9;
    text-align: right;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 13px;
    font-weight: 400;
    line-height: 150%; /* 19.5px */
}

.block-invite-bottom .icon-next-block-center {
    display: flex;
    font-size: 32px;
    justify-content: center;
    align-items: center;
}

.block-invite-bottom:hover {
    cursor: pointer;
    background-color: #A7A8A9;
    border-radius: 4px;
}

.block-invite-bottom:hover .icon-next-block-center, .block-invite-bottom:hover .txt-block-center {
    color: #FFFFFF;
}

.block-invite-director {

}

body {
    height: 100vh;
}

.prdt {
    height: 100%;
}

.prdt .container {
    height: 100%;
}

.new-video-menu {
    height: 100%;
}

.project-list {
    height: 100%;
}

.project-list .project-item {
    height: 100%;
    background-color: #FFFFFF;
}





.toast-bottom-center>div {
  width: clamp(320px, 100% - 32px, 1140px * 0.618) !important;
}

#toast-container {
    bottom: 80px;
}

#toast-container .u-row-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}


#toast-container label,
#toast-container button {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 16px;
}

#toast-container .heading {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 16px;
}

#toast-container p {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN', 'M PLUS 1p', sans-serif;
    font-style: normal;
    font-feature-settings: 'clig' off, 'liga' off;
    font-size: 16px;
}

#toast-container progress {
    /* プログレスバーの幅 */
    width: 100%;
    /* プログレスバーの高さ */
    height: 6px;

    /*-webkit-appearance: none;*/
    appearance: none;
    border-radius: 6px;
}

#toast-container progress::-webkit-progress-bar {
    background-color: #f0f0f0;
    border-radius: 6px;
}

#toast-container progress::-webkit-progress-value {
    background-color: #009ace;
    border-radius: 6px;
}

#toast-container .u-gap16 {
    gap: 16px;
}

#toast-container .u-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

#toast-container a,
#toast-container li,
#toast-container dt,
#toast-container dd,
#toast-container .bodytext {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'M PLUS 1p', sans-serif;
    font-weight: normal;
    font-size: clamp(0.813rem, 0.625rem + 0.94vw, 1rem);
    line-height: 200%;
}
#toast-container .u-justify-end {
    justify-content: flex-end;
}

#toast-container .c-btn-tertiary {
    min-width: 128px;
    padding: 8px 24px;
    color: #a7a8a9;
    background: #f0f0f0;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    transition: 0.2s;
}

#toast-container .c-btn-primary {
    min-width: 192px;
    padding: 8px 24px;
    color: #fff;
    background: #009ace;
    border: 1px solid #009ace;
    border-radius: 4px;
    transition: 0.2s;
}

#toast-container>div {
  position: relative;
  pointer-events: auto;
  overflow: hidden;
  margin: 0 0 8px;
  padding: 16px;
  /* width: 300px; */
  -moz-border-radius: 12px 12px 12px 12px;
  -webkit-border-radius: 12px 12px 12px 12px !important;
  /* border-radius: 12px 12px 12px 12px; */
  background-position: 16px center;
  background-repeat: no-repeat;
  -moz-box-shadow: 0 0 12px #999999;
  -webkit-box-shadow: 0 0 12px #999999;
  box-shadow: 2px 4px 8px 3px rgba(0, 0, 0, 0.10);
  /* box-shadow: 0 0 12px #999999; */
  color: #FFFFFF;
  opacity: 1.0;
  -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
  filter: alpha(opacity=1.0);
}

/* .sheader-links .sheader-link.text-link {
    color: #A7A8A9;
    font-size: 16px;
} */

.block-users-in-project .member-item-layout {
    padding: 8px 8px 8px 24px;
    border-radius: 4px;
    background: #fcfcfc;
    width:100%;
}

.block-users-in-project .member-item__name {
    text-align: left;
}
.block-users-in-project .member-item__action {
    /* margin: 8px auto;
    align-items: center; */
}

.prdt .tab--video-progress.tab--video-all {
    width: 100%;
}

.prdt .pd-section__video.pd-section--update.mscrollbar {
    display: flex;
}

.invite-the-btn.disable {
    pointer-events: none;
}

.d-none-chapter {
    display: none !important;
}

.pd-chapter__list.mscrollbar {
    overflow: unset;
}

.visibility-hide {
    visibility: hidden !important;
}

.block-message-count {
    width: 16px;
    height: 16px;
}

.prdt .mrow.mrow-custom {
    margin-bottom: 0;
}

.prdt .messenger-detail  {
    height: 100%;
    width: 100%;
}

.prdt .refactor .messenger-detail {
    /* position: absolute; */
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

.prdt .messenger-detail:has(.mmessage-list #footerCommentBlockOffer .offer-block-right .messenger-file-component-container-new.btn-accept-offer) {
    height: calc(100vh - 300px) !important;
}

.prdt .messenger-detail .mcontent {
    height: 100%;
}

.prdt .custom-switch-new {
    width: clamp(320px - 24px, 100% - 80px, 640px);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fcfcfc;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    color: #a7a8a9;
    padding: 0;
    margin: 0 auto;
    height: 30px;
}

.prdt .custom-switch-new .label-new {
    width: 100%;
    margin-bottom: 0;
    height: 28px;
}

.prdt .custom-switch-new .label-new .form-group-new {
    width: 100%;
    display: flex;
    margin-bottom: 0;
    height: 100%;
    align-items: center;
}

.prdt .custom-switch-new .label-new .form-group-new .text-item {
    flex: 1 ;
    text-align: center;
    border-radius: 6px;
    line-height: 100%;
    transition: 0.2s;
    padding: 4px 0;
    margin: 1px 1px;
    /* font-size: 16px; */
    /* font-weight: 500; */
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.prdt .custom-switch-new .label-new .form-group-new .text-item:hover {
    cursor: pointer;
    background-color: #a7a8a9;
    color: #fff;
}

.hide-top-bar {
    display: none !important;
}

.tab-disabled {
    pointer-events: none;
}

.text-offer {
    width: 100%;
    font-weight: normal;
    font-size: clamp(13px, 10px + 0.94vw, 16px);
    line-height: 200%;
    color: initial;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.tab--messenger-artist > .psearch-main {
    padding: 0 15px;
}

.prdt .messenger-file-component-content .content-middle .text-top, .prdt .messenger-file-component-content .text-top {
    font-size: 18px;
}

.prdt .messenger-file-component-content {
    height: 86px;
}

.prdt .messenger-file-component-container {
    margin-bottom: 8px;
}

.prdt .messenger-file-component-content {
    margin-bottom: 0;
}

.pd-section .mmessage-component {
    height: 100%;
    min-height: 60vh;
    max-height: 60vh;
}

/*.container-down .pd-section .mmessage-component {*/
/*    height: 83vh;*/
/*}*/

.prdt .pd-section-file .pd-file-content {
    max-height: 100%;
}

.prdt .pd-section-file {
    /* margin-top: 40px; */
    max-height: calc(100vh - 260px);
    background: #fcfcfc;
}

.hidden-btn-offer {
    visibility: hidden;
}

.action-panel-head-custom {
    width: auto;
    position: fixed;
    bottom: 0;
}

.action-panel-head-custom .offer-block-left2 {
    width: auto;
}

.project-tab.project-tab-messenger.active {
    position: fixed;
    top: 139px;
}

.project-tab.project-tab-messenger.refactor.active {
    position: relative;
    top: 0px!important;
}

.modal-open .sheader {
    /*top: -64px !important;*/
    z-index: 1 !important;
}

.modal-open:not(.modal-no-overlay) .navigation-top-app-bar {
    z-index: 0 !important;
}

.modal-open .block-navigation-bar {
       /*bottom: -80px !important;*/
       z-index: 1 !important;
}

.load-more-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.project-tab.project-tab-messenger.active:has(.tab--messenger-artist .psearch-main) {
    position: unset !important;
}

.modal-open .bootbox-confirm.in .modal-dialog {
    top: 50%;
    transform: translate(0, -50%);
}

.modal-open .pbanner.new-banner-project {
    z-index: 1;
}

.modal-open .prdt .pbanner.new-banner-project:has(>.modal-users-in-project:not(.d-none-el)) {
    z-index: 1;
}

.prdt:has(.project-item__content.hide) .navigation-top-app-bar {
    display: none;
}

.prdt:has(.project-item__content.hide) .schedule-project-detail {
    visibility: hidden;
}

@media (min-width: 695px) and (max-width: 992px) {
   .prdt .mcolumn--main.DM-box-container.dm-block-message {
        opacity: 1;
        position: relative;
    }
    .prdt .mcolumn--main .mmessage-list {
        height: 100%;
    }
    .prdt .messenger-detail {
        margin-top: 0 !important;
    }
    .prdt .martist .mcolumn--main, .martist .mcolumn--right {
        height: 100%;
    }
    .prdt .mcolumn--main {
        transform: unset;
        visibility: unset;
    }
    .prdt .mcolumn--wrap {
        width: calc(100% - 55px);
    }
    .prdt .martist .mcolumn--main .mcolumn-header, .martist .mcolumn--right .mcolumn-header {
        /*display: none;*/
    }
    .prdt .offer-content-message .messenger-detail .mcolumn-header {
        display: none;
    }
    .prdt .mcolumn-header .mcolumn-back {
        display: none;
    }
    .prdt .mcolumn-header .mcolumn-toggle {
        display: flex;
    }
    .prdt .mcolumn--right {
        visibility: unset;
        display: block;
        transform: unset;
        opacity: 1;
        position: fixed;
        right: 0;
        left: unset;
        top: 115px;
        z-index: 2;
        max-width: 268px;
        background-color: #FFFFFF;
    }
    .prdt .mrow {
        display: flex;
    }

    .prdt .mrow.mrow-custom {
        height: 100%;
    }

    .prdt .martist .maction {
        position: unset;
        margin: 0;
    }

    /* .prdt .pd-file-heading {
        padding: 0;
    }
     */
    .prdt .pd-product-comment .pd-section-file.file-hide {
        display: block;
        width: 267px;
    }

    .prdt .pd-section--detail.pd-product-comment {
        flex-wrap: unset;
    }

    /*.prdt .pd-section-file.file-hide {*/
    /*    display: block;*/
    /*}*/

    /*.prdt .pd-product-comment .pd-section-file.file-hide {*/
    /*    !*display: block;*!*/
    /*    position: fixed;*/
    /*    right: 0;*/
    /*    width: 267px;*/
    /*    z-index: 999;*/
    /*    background-color: #FFFFFF;*/
    /*}*/

    /*.prdt .pd-section--detail.pd-product-comment .pd-section__content.active {*/
    /*    width: 100% !important;*/
    /*}*/

}

/* @media (min-width: 695px) {
    .prdt .open-close-wallet-sp {
        display: none !important;
    }

    .prdt .hide-file-tab {
        display: none;
    }
} */


#schedulaCalendarModal .modal-dialog.sc-block {
    pointer-events: visible;
}

/* #schedulaCalendarModal .calendar-body {
    overflow: auto;
    height: 100%;
} */

.prdt #modal-confirm-done-offer.account__popup-container.modal-confirm.in .modal-dialog.popup-dialog {
    width: auto;
    height: auto;
}


.height-schedule-header {
    height: calc(100dvh - 8px); /* 8px margin */
}

.cvideo__thumb-item-delivery .cvideo__thumb {
    cursor: pointer;
}
