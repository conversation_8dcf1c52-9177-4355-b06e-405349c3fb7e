/* 01EG89H6SS2141VNGDDEHBMV4Q */
.header {
    box-shadow: 4px 4px 12px 0 rgba(166, 166, 166, 0.21);
}

.account-link {
    margin-left: 20px;
}

.link-header:hover .dropdown-menu {
    display: block;
}

.link-header .dropdown-menu {
    transition: all .4s ease;
    position: absolute;
    top: 100%;
    right: 0;
    left: auto;
    z-index: 1000;
    display: none;
    float: right;
    min-width: 160px;
    padding: 5px 0;
    margin: 0;
    font-size: 14px;
    text-align: right;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    border: none;
    background-clip: padding-box;
    border-radius: 4px;
    box-shadow: 4px 4px 12px 0 rgba(166, 166, 166, 0.21);
}

.header {
    box-shadow: 4px 4px 12px 0 rgba(166, 166, 166, 0.21) !important;
}

@media (max-width: 992px) {
    .header-top-sp {
        background: none !important;
    }
}

#toast-container > div {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

#toast-container > div:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.17), 0 2px 4px -1px rgba(0, 0, 0, 0.13);
}

.toast-top-right {
  top: 70px;
  right: 12px;
}

.button--background,
.account .button--background {
    color: white;
    background: #53565a;
    height: auto;
    line-height: inherit;
    padding: 6px 15px;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.button--background:hover {
    background: rgba(0, 157, 196, 0.73);
}

.button--background-secondary {
    color: #53565a;
    background: white;
    height: auto;
    line-height: inherit;
    padding: 6px 15px;
    cursor: pointer;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.button--background-secondary:hover {
    color: white;
    background: #53565a;
}

.button--gradient {
    border: none;
    height: auto !important;
    line-height: inherit;
    padding: 10px 15px;
    transition: .1s;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

.button--round {
    border-radius: 50px;
}

.button--gradient:hover {
    background: #0076a5 !important;
    color: #fcfcfc !important;
    transition: .2s;
}

.upload__file .button {
    height: auto;
    line-height: inherit;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
}

button {
    border: none;
}

.messenger-accept, .messenger-director__item-action {
    margin: 10px 0;
}

.col-md-5.col-sm-5.messenger__column-left .messenger__list {
    padding: 0 10px;
}

.project-item-right-action {
    margin: 0 30px 0 auto;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN', 'Noto Sans Japanese', 'sans-serif';
    font-weight: normal;
}

.master_admin {
    filter: invert(61%) sepia(53%) saturate(690%) hue-rotate(81deg) brightness(95%) contrast(85%);
}

.creator {
    filter: invert(68%) sepia(1%) saturate(272%) hue-rotate(169deg) brightness(98%) contrast(97%);
}

.admin {
    filter: invert(34%) sepia(6%) saturate(372%) hue-rotate(174deg) brightness(91%) contrast(87%);
}

.client, .master_client{
    filter: invert(48%) sepia(36%) saturate(7500%) hue-rotate(168deg) brightness(98%) contrast(103%);
}

.owner-project {
    filter: invert(28%) sepia(98%) saturate(1535%) hue-rotate(174deg) brightness(88%) contrast(102%);
}

.background-avt {
    background-color: white;
    border-radius: 50%;
}


.button--black {
    border: none;
    height: auto !important;
    line-height: inherit;
    padding: 5px 60px;
    transition: .1s;
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-weight: normal;
    background: #54565a;
    color: white;
}

.button--black:hover {
    background: #333333;
    color: white;
}

.footer-logo {
    background: #53565A;
    text-align: center;
    margin: 90px auto 0px;
    padding: 20px;
}

.pointer {
    cursor: pointer;
}
