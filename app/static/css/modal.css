/* .smodal .modal-dialog {
    width: auto;
    max-width: 100%;
    margin: 0;
} */

.smodal .modal-header {
    padding: 24px;
}

.smodal .modal-content {
    border: none;
    border-radius: 0;
    box-shadow: none;
    background-color: transparent;
}

.smodal .modal-body {
    padding: 16px;
    max-height: calc(100vh - 50px);
}

/* .smodal--large .modal-dialog {
    width: 752px;
} */

.smodal--large .smodal-close {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    color: #53565a;
    text-align: center;
}

.smodal--large .smodal-close .icon {
    margin-left: -3px;
}

.smodal .smodal-close {
    position: absolute;
    top: 20px;
    right: 24px;
    color: #a7a8a9;
    z-index: 9;
    font-size: 20px;
}

.smodal .smodal-close:hover {
    cursor: pointer;
    color: #009ace;
}

.smodal .smodal-close--prev {
    width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    color: #53565a;
    text-align: center;
}

.smodal .smodal-close--prev .icon {
    margin-left: -3px;
}

/*.smodal--video .modal-content {*/
/*  background: transparent;*/
/*  box-shadow: none; }*/

.smodal--video .modal-dialog {
    background-color: transparent;
}

/* @media (max-width: 992px) {
    .smodal {
        margin: 0 10px;
    }
} */

/* .smodal--video .smodal-close {
    /* stylelint-disable-line
    color: #fff;
    top: 5px;
    right: 10px;
} */

/* @media (max-width: 992px) {
    .smodal--video .smodal-close {
        right: 5px;
    }
} */

.smodal--video .smodal-close:hover {
    color: #009ace;
}

/*.smodal--video .modal-body {*/
/*  padding: 0;*/
/*  overflow: hidden; }*/

.smodal--image .modal-content {
    background: transparent;
    box-shadow: none;
}

.smodal--image .smodal-close {
    /* stylelint-disable-line */
    color: #fff;
    top: 5px;
    right: 0;
}

@media (max-width: 992px) {
    .smodal--image .smodal-close {
        right: 16px;
    }
}

.smodal--image .smodal-close:hover {
    color: #009ace;
}

.smodal--image .modal-body {
    padding: 0;
    overflow: hidden;
}

@media (max-width: 992px) {
    .smodal--document .modal-dialog {
        padding: 0 15px;
    }
}

.smodal--document .modal-content {
    background: transparent;
    overflow: hidden;
}

.smodal--document .smodal-download, .smodal--image .smodal-download, .smodal--video .smodal-download {
    position: absolute;
    right: calc(50% - 20px);
    top: 30px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 992px) {
    .smodal--document .smodal-download, .smodal--image .smodal-download, .smodal--video .smodal-download {
        left: 5px;
        right: auto;
        top: 5px;
        width: 30px;
        height: 30px;
        line-height: 30px;
    }
}

.smodal--document .smodal-close {
    /* stylelint-disable-line */
    color: #fff;
    top: 5px;
    right: 10px;
}

@media (max-width: 992px) {
    .smodal--document .smodal-close {
        right: 5px;
    }
}

.smodal--document .smodal-close:hover {
    color: #009ace;
}

.smodal--document .modal-body {
    padding: 0;
}

.smodal--document embed {
    min-height: 66vh;
}

.smodal--document iframe {
    min-height: 80vh;
    width: 100%;
}

@media (max-width: 992px) {
    .smodal--document iframe {
        min-height: 70vh;
    }
}


.video-popup {
    text-align: center;
}

.video-popup__title {
    background-color: #000;
    border-radius: 6px;
    color: #fff;
    display: inline-block;
    padding: 8px 16px;
    margin-bottom: 24px;
}

.video-popup video {
    max-height: 80vh;
}

@media (max-width: 992px) {
    .video-popup video {
        max-height: 60vh;
    }
}

.image-popup {
    text-align: center;
}

.image-popup__title {
    background-color: #000;
    border-radius: 6px;
    color: #fff;
    display: inline-block;
    padding: 8px 16px;
    margin-bottom: 24px;
}

.document-popup__content {
    background-color: #fff;
    border-radius: 12px;
    padding: 8px 8px 4px;
}

.document-popup__top {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 28px;
}

@media (max-width: 992px) {
    .document-popup__top {
        justify-content: start;
    }
}

.document-popup__btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    text-align: center;
    line-height: 34px;
    color: #fff;
    font-size: 24px;
}

.document-popup__btn:hover {
    cursor: pointer;
}

.document-popup__title {
    background-color: #000;
    color: #fff;
    border-radius: 6px;
    width: fit-content;
    height: 34px;
    line-height: 34px;
    padding: 0 16px;
    margin: 0 auto 5px;
    -webkit-line-clamp: 1;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /*! autoprefixer: on */
    overflow: hidden;
    word-break: break-word;
}

.document-popup__title .icon {
    /* stylelint-disable-line */
    font-size: 16px;
    margin-right: 8px;
    position: relative;
    bottom: -2px;
}

.document-popup__action {
    text-align: center;
    margin-top: 12px;
    margin-bottom: -8px;
}

/* .document-popup__action .btn {
    stylelint-disable-line
} */

.document-popup__action .btn .icon {
    /* stylelint-disable-line */
    margin-right: 12px;
}

.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.05);
}




.modal-backdrop.show {
    opacity: 0.05;
}

/* .modal {
    text-align: center;
    padding-right: 0 !important;
} */

/* .modal:before {
    content: '';
    display: inline-block;
    height: 75px;
    vertical-align: middle;
    margin-right: -4px;
} */

.modal .modal-body {
    overflow-x: auto;
}

.modal .modal-dialog {
    display: inline-block;
    margin: 0 auto;
    /* text-align: left; */
    /* vertical-align: middle; */
    /* width: auto; */

}

@media (max-width: 576px) {
    /* .modal {
        top: 80px;
    } */

    .modal:before {
        display: none;
    }

    /* .modal .modal-dialog {
        display: block;
    } */
}

#modal-create-offer, #modal-edit-offer {
    margin-top: 0;
}

#modal-create-offer .modal-dialog, #modal-edit-offer .modal-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.modal-create-edit-offer .modal-dialog {
    width: 100% !important;
    height: 100% !important;
    border-radius: 0px !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-create-edit-offer .modal-dialog .modal-content {
    width: 100%;
}

.modal-create-edit-offer .modal-body {
    max-height: 100% !important;
    height: 100%;
    overflow: hidden;
    max-width: 756px;
    width: 100%;
}

.modal-create-edit-offer .create-offer__action {
    position: fixed;
    bottom: 0;
    left: 0;
    margin-bottom: 0px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 16px;
    width: 100%;
    background-color: #FCFCFC;
    border: 1px solid #f0f0f0;
    z-index: 9999;
}

.modal-create-edit-offer .create-offer__action__container {
    width: 100%;
    max-width: 1140px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.modal-create-edit-offer .modal-dialog__header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 152px;
    width: 100%;
    padding: 0 24px;
}

.modal-create-edit-offer .modal-dialog__header__container {
    width: 100%;
    max-width: 1140px;
    padding: 32px 0px 8px;
    height: 100%;
}

.modal-create-edit-offer .modal-dialog__header__container > hr {
    margin: 24px 0;
    width: 100%;
    border-bottom: none;
}

.modal-create-edit-offer .modal-dialog__header__container .modal-dialog__header__text {
    width: 100%;
    font-weight: 400;
    font-size: 40px;
    line-height: 100%;
    color: #000000;
    letter-spacing: 2.5px;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    text-align: left;
}


.modal-create-edit-offer .form-block-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background-color: transparent;
    margin-bottom: 16px;
}

.modal-create-edit-offer .form-block-container .form-block-title {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 200%;
    color: #000000;
    text-align: left;
}

.modal-create-edit-offer .form-block-container .form-block-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 8px 16px;
    background: #FCFCFC;
    border: 1px solid #F0F0F0;
    border-radius: 4px;
}

.modal-create-edit-offer .form-block-container .form-block-content .sform-row {
    width: 100%;
}

.modal-create-edit-offer .form-block-container .form-block-content .sform-row:last-child {
    margin-bottom: 0px;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-bottom {
    max-width: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-top {
    max-width: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: flex-start;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-top {
    margin-bottom: 8px;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-top .form-group {
    margin-bottom: 0px;
    position: relative;
}

.modal-create-edit-offer .form-block-container .form-block-content .input-block-bottom .create-offer__price-item {
    margin-bottom: 0px;
}

.modal-create-edit-offer .form-block-content .input-block-top-left,
.modal-create-edit-offer .form-block-content .input-block-bottom-left {
    width: calc(100% - 76px);
    max-width: calc(100% - 76px);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.modal-create-edit-offer .form-block-content .input-unit-block {
    width: 76px;
    margin-left: 4px;
    min-width: 76px;
}

.modal-create-edit-offer .form-block-content .input-block-bottom-left {
   display: flex;
   justify-content: flex-end;
   width: calc(100% - 76px);
}

.modal-create-edit-offer .form-block-content .input-block-bottom-left .create-offer__price {
    margin-right: 15px;
}

.modal-create-edit-offer .form-block-content .input-block-bottom-left .create-offer__price-number {
   padding-right: 0px;
}

.modal-create-edit-offer .form-block-content label {
   margin-bottom: 0;
}

.modal-create-edit-offer .form-block-content .sform-row--2-columns .sform-group:first-child {
   padding-right: 4px;
}

.modal-create-edit-offer .form-block-content .sform-row--2-columns .sform-group:last-child {
   padding-left: 4px;
}

@media (max-width: 576px) {
    #modal-create-offer, #modal-edit-offer {
        top: 0;
        width: 100%;
        margin: 0;
    }

    .modal-create-edit-offer .modal-dialog__header {
        height: 132px !important;
    }

    .modal-create-edit-offer .modal-dialog__header__container .modal-dialog__header__text h1 {
        font-style: normal;
        font-weight: 400;
        font-size: 24px;
        line-height: 100%;
        letter-spacing: 2.5px;
    }

    .modal-create-edit-offer .create-offer {
        max-height: calc(100vh - 132px - 104px) !important;
    }

    .modal-create-edit-offer .create-offer__action__container {
        padding-left: 24px;
        padding-right: 24px;
    }

    .modal-create-edit-offer .form-block-content .sform-row--2-columns .sform-group:last-child {
        padding-left: 4px;
    }

    .modal-create-edit-offer .form-block-content .sform-row--2-columns .sform-group:first-child{
        padding-right: 4px;
    }

    .modal-create-edit-offer .form-block-content .sform-row--2-columns .sform-group{
        flex: 0 0 50% !important;
    }
}

.messenger-popup, .messenger-popup-plan {
    transition: opacity 0.4s linear;
}

.messenger-popup__content, .messenger-popup__content_plan {
    background-color: white;
    width: 35vw;
    margin: auto;
    padding: 28px;
    box-shadow: -2px -2px 6px 0 rgba(0, 0, 0, .1);
    border-radius: 6px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 400px;
}

.messenger-popup__content input, .messenger-popup__content textarea, .messenger-popup__content_plan input, .messenger-popup__content_plan textarea {
    /* stylelint-disable-line */
    border: 1px solid #d6d6d6;
    margin-bottom: 15px;
}

.messenger-popup__content input::placeholder, .messenger-popup__content textarea::placeholder, .messenger-popup__content_plan input::placeholder, .messenger-popup__content_plan textarea::placeholder {
    color: #d0d1d4;
}

.messenger-popup__form-text {
    margin-bottom: 15px;
    line-height: 1.5;
}

.messenger-popup__form-confirm {
    margin-bottom: 15px;
}

.messenger-popup__form .input-checkbox {
    display: inline-block;
}

.messenger-popup__action {
    margin-top: 15px;
    text-align: right;
}

.messenger-director__item-info .messenger-popup__action {
    margin-top: 0;
}

.messenger-popup__action .button--gradient {
    font-size: 20px;
    text-transform: uppercase;
    margin-bottom: 20px;
    text-align: center;
}

.messenger-popup__file-input {
    display: none !important;
}

.messenger-popup__file-title {
    color: #009dc4;
    margin-bottom: 5px;
}

.messenger-popup .file-list, .messenger-popup-plan .file-list {
    padding: 0;
    margin: 0 0 15px;
    list-style: none;
}

.messenger-popup .file-delete, .messenger-popup-plan .file-delete {
    display: inline-block;
    margin-left: 10px;
    font-size: 20px;
    transform: rotate(-45deg);
    color: #d6d6d6;
    position: relative;
    bottom: -5px;
}

.messenger-popup .file-delete:hover, .messenger-popup-plan .file-delete:hover {
    cursor: pointer;
}

.messenger-popup__action .button--gradient.button--disabled {
    background-image: linear-gradient(45deg, rgba(182, 182, 182, 1), grey);
    color: #f0f0f0;
}

/* Modal guide */
.guide-popup__content {
    background-color: #FFFFFF;
    width: 35vw;
    margin: auto;
    padding: 24px;
    box-shadow: -2px -2px 6px 0 rgb(0 0 0 / 10%);
    border-radius: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 400px;
    text-align: left;
}

.guide-popup__wrap {
    display: flex;
    padding: 12px 0;
}

.guide-popup__wrap-content {
    width: calc(100% - 200px);
    border-radius: 6px;
    padding: 12px 14px;
}

.mscene__user .icon {
    color: #A7A8A9;
    font-size: 12px;
    margin: 0 8px;
}

.mscene__bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
}

.mscene__bottom .mscene__name .thread-name {
    margin-bottom: 0;
}

.mchecked .mscene__bottom .mscene__name .thread-name {
    color: #FFFFFF;
}
#modal-guide .mscene__name .thread-name {
    max-width: 210px;
}

.notification {
    color: #FFFFFF;
    font-weight: 300;
    font-size: 10px;
    line-height: 15px;
    padding: 0 6px;
    border-radius: 10px;
    background-color: #009ACE;
    width: fit-content;
    height: fit-content;
    margin-left: 8px;
}

.mchecked .mscene__date {
    color: #FFFFFF;
}

.mscene__date {
    color: #A7A8A9;
    font-weight: 300;
    font-size: 8px;
    line-height: 12px;
}

.mscene__wrap-msg {
    background-color: #53565A;
    padding: 12px 16px;
    color: #FFFFFF;
    font-weight: 300;
    font-size: 13px;
    line-height: 20px;
    border-radius: 8px;
    height: 44px;
    width: 145px;
    margin: auto;
    position: relative;
}

.mscene__wrap-msg::before {
    content: "";
    border-width: 12px;
    border-style: solid;
    position: absolute;
    left: -20px;
    top: 10px;
    border-color: transparent #53565A transparent transparent;
}

@media (max-width: 992px) {
    .guide-popup__wrap-content {
        width: calc(100% - 130px);
    }

    .mscene__wrap-msg {
        width: 100px;
    }

    #modal-guide .mscene__name .thread-name {
        max-width: 100px;
    }

    .guide-popup__content {
        min-width: auto;
        width: auto;
    }
}

@media (max-width: 739px) {
    .guide-popup__wrap-content {
        width: 100%;
        margin-right: 20px;
    }

    .mscene__bottom {
        flex-wrap: wrap;
    }
}
/* End modal guide */

/* Modal delete offer */
#delete-offer.modal:before, #closed-offer.modal:before {
    height: 0;
}

#delete-offer .modal-dialog, #closed-offer .modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    height: 100%;
}

.account__popup-container {
    top: 0;
    background: rgba(0, 0, 0, 0.8);
}

#delete-offer .popup-title, #closed-offer .popup-title {
    font-size: 16px;
    line-height: 24px;
    color: #000;
    margin: 0;
    text-align: center;
}

.popup-text {
    font-size: 13px;
    line-height: 20px;
    color: #000;
    padding: 16px 0;
    padding-top: 0;
    margin: 0;
    text-align: center;
}

#delete-offer .popup-content, #closed-offer .popup-content {
    width: 370px;
    padding: 40px 24px;
}

#delete-offer .popup-content .popup-footer, #closed-offer .popup-content .popup-footer {
    text-align: center;
    font-size: 13px;
}

.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus {
    outline: 0;
}

/* .btn {
    line-height: 19px;
    border-radius: 4px;
} */

@media (max-width: 992px) {
    #delete-offer .modal-dialog, #closed-offer .modal-dialog {
        width: auto;
    }
}
/* End modal delete offer */


.modal-confirm.modal:before {
    height: 0;
}

.modal-confirm .modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    height: 100%;
}

.modal-confirm .popup-title {
    font-size: 16px;
    line-height: 24px;
    color: #000;
    margin: 0;
    text-align: center;
}

.modal-confirm .popup-content {
    width: 370px;
    padding: 40px 24px;
}

@media (max-width: 992px) {
    .modal-confirm .modal-dialog {
        width: auto;
    }
}


.popup-text_form {
    font-family: AXIS Round 50 StdN;
    font-style: normal;
    font-weight: 300;
    font-size: 13px;
    color: #000000;
    align-items: flex-end;
    text-align: right;
}

.popup-form-budget {
    display: flex;
    align-items: center;
    margin-top: 20px;
}
