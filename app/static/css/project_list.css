.sproject {
    background-size: cover;
    background-position: center center;
    border-radius: 12px;
    position: relative;
}

.sproject:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 12px;
}

.sproject__main {
    display: flex;
    flex-direction: column;
    height: 120px;
    padding: 16px;
    position: relative;
    z-index: 2;
}

.sproject__top {
    display: flex;
}

.sproject .bdropdown {
    right: auto;
    margin-top: -5px;
}

.sproject .bdropdown .icon {
    color: #fff;
    font-size: 24px;
}

.bdropdown-menu {
    margin-left: -20vh;
    right: 0 !important; /* stylelint-disable-line */
    left: auto !important; /* stylelint-disable-line */
}

.sproject__user-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.sproject__user {
    display: inline-flex;
}

.sproject__user:not(:first-child) {
    margin-left: -8px;
}

.sproject__user .avatar {
    border: 2px solid #fff;
}

.sproject__bottom {
    display: flex;
    margin-top: auto;
    align-items: flex-end;
}

.sproject__user-btn {
    font-size: 26px;
    color: #fff;
    height: 26px;
    line-height: 1;
    margin-top: 2px;
}

.sproject__user-btn:hover {
    color: #fff;
}

.sproject__progressbar {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 1;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.sproject__progressbar .sprogress {
    height: 3px;
    background-color: #d3d3d3;
    border-radius: 0;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

.sproject__progressbar .progress-bar {
    background-color: #009ace;
    border-radius: 0;
}

.sproject__number {
    color: #fff;
    margin-left: auto;
    line-height: 1;
}


.sprojects {
    margin-top: 8px;
    width: min(100%, 1140px);
    margin-inline: auto;
}

.sprojects-search-tag {
    margin-top: 16px;
}

.sprojects-search-tag .tag {
    margin-right: 4px;
    margin-bottom: 8px;
}

.sprojects-list {
    display: flex;
    flex-wrap: wrap;
    margin: 20px -12px 40px -12px;
}

.sprojects-list .sprojects-item {
    flex: 0 0 33.333333%;
    padding: 12px;
    margin-bottom: 16px;
}

@media (max-width: 992px) {
    .sprojects-list .sprojects-item {
        flex: 0 0 100%;
    }
}

.sprojects-list .sproject-time {
    margin-top: 16px;
    display: flex;
    align-items: center;
    line-height: 1;
}

.sprojects-list .sproject-time .icon {
    color: #a7a8a9;
    font-size: 16px;
    margin-right: 8px;
}

.member-manage__heading {
    margin-bottom: 24px;
}

/* .member-manage__group {
    width: min(100vw - 16px - 46px, 1140px * 0.382);
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 24px;
    padding-bottom: 40px;
} */

.member-manage__head {
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 16px;
    line-height: 150%;
    color: #000000;
}

.member-manage__head-hint {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 11px;
    line-height: 150%;
    color: #000000;
    margin-bottom: 16px;
}

.member-manage__head:not(:first-child) {
    margin-top: 48px;
}

.member-item-right {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    margin-right: 0px !important;
    padding-top: 5px;
}

.member-item-identification {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    font-size: 8px;
    line-height: 150%;
    color: #000000;
    border: 1px solid #A7A8A9;
    border-radius: 4px;
    padding: 1px 4px;
    margin-left: 5px;
}

.member-manage__group__director{
    width: min(100vw - 16px - 46px, 1140px * 0.3770);
}


.member-manage__list .group-member-item {
    display: flex;
    flex-direction: row;
}

.member-item__info {
    width: min(100vw - 16px - 46px, 1140px * 0.29);
}

.member-item__info .text-container{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.member-manage__list-producer .member-item-container:first-child .sub-member-item:first-child {
    border: 1px solid #009ACE;
}

.member-item__info-bottom {
    display: flex;
}

/* .member-manage__list {
    padding: 0px 0px 0px 10px;
    width: min(100vw - 16px - 46px, 1140px * 0.382);
} */

.member-manage__list-producer, .member-manage__list-director, .list-project-member, .list-project-owner {
    min-height: 50px;
}

.member-manage__list .member-item-left .member-item__info-bottom .member-item__role, .member-manage__list .member-item-left .member-item__info-bottom .member-item__company {
    max-width: 50%;
    -webkit-line-clamp: 1 !important;
    line-height: unset !important;
}

/* .member-item-container {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 8px;
} */

.member-item-action {
    padding-left: 7px;
    /* margin-bottom: 20px; */
    display: flex;
    align-items: center;
    width: 50px;
}

.member-item-action__button-container .member-item__btn-delete .icon--sicon-trash{
    color: #A7A8A9;
    font-size: 20px !important;
}

.member-item-action__button-container .member-item__btn-move {
    color: #A7A8A9;
    font-size: 22px !important;
    /* margin-bottom: 10px !important; */
    line-height: 22px;
}

.member-item-action__button-container .member-item__btn-delete .icon--sicon-trash:hover, .member-item-action__button-container .member-item__btn-move:hover {
    color: #0076A5;
    cursor: pointer;
}

.member-manage-action{
    display: flex;
    justify-content: flex-end;
}

.member-item-action__button-container {
    opacity: 0;
    pointer-events: none;
    display: block;
    padding: 0px auto;
    text-align: center;
}

.member-item__avatar a {
    display: flex;
    align-items: center;
}

.member-item__avatar {
    display: flex;
    align-items: center;
    /* padding: 4px 0px; */
}

/* #modal-member-manage .modal-dialog {
    margin: 30px 0px;
} */

.modal .modal-body {
    max-height: calc(100dvh - 108px);
}

.show-button-action {
    opacity: 1;
    pointer-events: all;
}

.member-manage__list-producer .member-item-container:first-child .member-item-action__button-container.show-button-action .member-item__btn-delete {
    display: none !important;
}

.modal-backdrop {
    z-index: 999 !important;
}

/*@media (max-width: 576px){
    .modal {
        top: 0px;
    }
} */

@media (max-width: 992px) {
    .member-item-right {
        display: block;
    }

    .member-item-container .member-item__info {
        max-width: 200px;
        width: unset !important;
    }

    .member-manage__list .member-item-left {
        max-width: 200px;
        width: unset !important;
    }

    .member-manage__list .member-item-right {
        max-width: 150px;
        width: unset !important;
    }

    .member-item-action {
        padding-left: 2px;
    }
}

.member-manage__group:last-child {
    border-bottom: none;
    margin-bottom: 0px;
}

.member-manage__invite {
    margin-bottom: 8px;
    text-align: center;
}



.member-item:last-child {
    margin-bottom: 0;
}

.member-item__info {
    margin-left: 8px;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.member-item__name {
    margin: 4px 0;
    width: 180px;
    font-size: 13px;
}

.member-item__action {
    margin-left: auto;
    word-wrap: break-word;
}

.member-item__role .sselect-wrapper .SumoSelect > .CaptionCont {
    text-align: right;
    padding-top: 0;
    border: none;
}

.member-item__role .sselect-wrapper .SumoSelect > .CaptionCont > label {
    align-items: flex-start;
    justify-content: flex-end;
}

.member-item__role .sselect-wrapper .SumoSelect > .CaptionCont > span {
    padding-right: 8px;
    font-size: 13px;
}

.member-item__role .sselect-wrapper .SumoSelect.open > .optWrapper {
    top: 24px;
}

#modal-member-ip .member-item {
    margin-bottom: 0;
}

#modal-member-ip .modal-header {
    padding: 14px 24px 6px 24px;
}

.member-ip__list {
    padding-left: 48px;
    padding-right: 8px;
}

.member-ip__item {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.member-ip__input {
    flex: 1;
}

.member-ip__input .sform-control {
    width: 100%;
}

.member-ip__delete {
    font-size: 24px;
    color: #a7a8a9;
    margin-left: 16px;
}

.member-ip__delete:hover {
    color: #009ace;
}

.member-ip__new {
    padding: 0 48px;
}

.member-ip__new .sform-control {
    width: 100%;
}

.member-ip__new-ip {
    margin-bottom: 24px;
}

.member-ip__new-btn {
    color: #d3d3d3;
    font-size: 24px;
    border: 1px dashed #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 9px;
    margin-bottom: 40px;
}

.member-ip__new-btn:hover {
    color: #009ace;
}

.sprojects-filter {
    display: flex;
    align-items: center;
    margin: 32px 0 8px;
}

.sprojects-filter .video-filter {
    display: flex;
    align-items: center;
    line-height: 1;
    color: #009ace;
}

.sprojects-filter .video-filter:hover {
    cursor: pointer;
}

.sprojects-filter .video-filter .icon {
    /* stylelint-disable-line */
    font-size: 18px;
    margin-right: 8px;
    color: #a7a8a9;
}

.sprojects-filter .video-filter.video-heart .icon {
    /* stylelint-disable-line */
    color: #009ace;
}

.sprojects-filter .video-filter.video-all .icon-image {
    width: 18px;
    height: 18px;
}

.sprojects-filter .video-filter.video-all .icon-image:before {
    background-size: cover;
}

.sprojects-filter .video-order-by {
    /*margin-left: auto;*/
}

.sprojects-filter .video-order-by .sselect-wrapper .SumoSelect > .CaptionCont {
    /* stylelint-disable-line */
    font-size: 13px;
    padding: 12px 16px;
}

.sprojects-add-project__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap:0;

    height: 64px;
    padding: 8px 0px;
    border-radius: 4px;
        
    background-color: #ffffff;
    border: 1px solid #f0f0f0;
    box-shadow: 2px 4px 8px 0px rgba(0, 0, 0, 0.05);
    
    transition: 0.2s;
}

.sprojects-add-project__content:hover {
    cursor: pointer;
    background-color: #009ace;

    .sprojects-add-project__text {
        color: #fff;    
    }
    .material-symbols-rounded {
        color: #fff;
    }
}


.sprojects-add-project__text {
    color: #000000;
    font-size: 11px;
}



.sprojects-search-tag {
    margin-top: 16px;
}

.sprojects-search-tag .tag {
    margin-right: 4px;
    margin-bottom: 8px;
}

.video-order-type {
    color: #f0f0f0;
}

.video-order-type:hover {
    cursor: pointer;
}

.video-order-type.active {
    color: #009ace;
}

.video-order__asc {
    margin-right: 2px;
}

.video-order {
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-left: auto;
    padding-right: 8px;
}

.batch-number-project {
    line-height: 1;
    align-items: center;
}

.batch-number-project .slabel--blue{
    padding: 0 7px;
    height: 20px;
    line-height: 20px;
    font-size: 8px;
}

.bootbox-body {
    font-size: 13px;
}


/* .material-symbols-rounded {
  font-variation-settings:
  'FILL' 1,
  'wght' 600,
  'GRAD' 0,
  'opsz' 24;

  color:#a7a8a9;
} */

.banner-default {
    background: #53565A;
    color: white;
    font-family: "corporate-logo-ver2", sans-serif;
    font-weight: 700;
    font-style: normal;
    font-size: clamp(1.875rem, 1.375rem + 2.5vw, 2.375rem);
    white-space: nowrap;
}
