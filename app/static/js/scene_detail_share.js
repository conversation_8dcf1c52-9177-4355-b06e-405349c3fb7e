const maxWidthSPDevice = 767;
const itemPaddingRight = 'item-padding-right';
const itemPaddingLeft = 'item-padding-left';

function applyStyleParentSlide() {
    let listVariation = $('.list-variation');
    let blockSceneVideo = $('.pd-scene.block-scene-video');
    let sceneCurrent = blockSceneVideo.find('.cscene__variation.slick-slide.slick-current.slick-center .cscene__version.slick-slide.slick-current.slick-active');
    let childrenSceneCurrent = sceneCurrent.children()
    let dataMaxHeightVariations = childrenSceneCurrent.attr('data-max-height-variations')
    let dataPreviewHeight = childrenSceneCurrent.attr('data-preview-height')
    let dataPreviewWidth = childrenSceneCurrent.attr('data-preview-width')
    let maxWidthViewport = $(document).width()
    let sceneActive = $('.cscene__version.slick-slide.slick-current.slick-active')
    let sceneNew = blockSceneVideo.find('.cscene__variation.slick-slide.slick-current.slick-center');
    if ($(document).width() > maxWidthSPDevice) {
        let paddingHozScreen = childrenSceneCurrent.attr('data-padding-hoz-screen')
        let widthCommentArea = childrenSceneCurrent.attr('data-width-comment')
        let maxWidthViewportPC = $(document).width() * percentMaxWidthPreviewPC;
        setStyleParentScenePC(sceneNew, sceneActive, maxWidthViewportPC, dataPreviewHeight, dataPreviewWidth, paddingHozScreen, widthCommentArea)
        listVariation.css({
            'max-height': `${dataMaxHeightVariations}px`,
        })
    } else {
        setStyleParentScene(sceneNew, sceneActive, maxWidthViewport, dataPreviewHeight, dataPreviewWidth);
        listVariation.css({
            'max-height': `${dataMaxHeightVariations}px`,
        })
    }
}

function setStyleParentScenePC(sceneNew, sceneActive, maxWidthViewport, dataPreviewHeight, dataPreviewWidth, paddingHozScreen, widthCommentArea) {
    let pdComment = $('.pd-comment');
    let blockSceneVideo = $('.block-scene-video');
    let pdSectionContent = $('.pd-section__content');
    let widthBlockSceneVideo = $(document).width() * percentMaxWidthPreviewPC;
    const btnSceneHeart = $('.scene-detail-heart');
    const blockRating = $('.block-rating');
    const blockTxtName = $('.block-txt-name');
    if (paddingHozScreen > 0) {
        let divPadding = paddingHozScreen;
        pdComment.css({
            'min-width': `${widthCommentArea}px`,
            'width': `${widthCommentArea}px`,
            'width': '100%',
        })
        blockSceneVideo.css({
            'max-width': `${dataPreviewWidth}px`,
            'width': `${dataPreviewWidth}px`,
            'width': '100%',
        })
        pdSectionContent.css({
            'padding-left': `${divPadding}px`,
            'padding-right': `${divPadding}px`
        })
        btnSceneHeart.addClass(itemPaddingLeft)
        blockRating.addClass(itemPaddingRight)
        blockTxtName.addClass(itemPaddingLeft)
    } else {
         pdComment.css({
            'min-width': `${widthCommentArea}px`,
            'width': `${widthCommentArea}px`,
            'width': '100%',
        })
        blockSceneVideo.css({
            'max-width': `${widthBlockSceneVideo}px`,
            'width': `${dataPreviewWidth}px`,
            'width': '100%',
            'flex': 'initial'
        });
        pdSectionContent.css({
            'padding-left': '',
            'padding-right': ''
        });
        btnSceneHeart.removeClass(itemPaddingLeft)
        blockRating.removeClass(itemPaddingRight)
        blockTxtName.removeClass(itemPaddingLeft)
    }

    pdComment.addClass('w-100');
    sceneNew.parent().parent().css({
        'height': dataPreviewHeight + 'px',
        'width': `${maxWidthViewport}px`,
    })
    sceneActive.css({
        'width': '100%',
        'height': dataPreviewHeight + 'px',
    })
    sceneActive.children().children().css({
        'width': dataPreviewWidth + 'px',
        'height': dataPreviewHeight + 'px',
    })
    let listSlickSlide = sceneActive.parent().find('.cscene__version.slick-slide');
    listSlickSlide.css({
        'margin-bottom': '1px'
    })
    let listSlickSlideHz = sceneNew.parent().find('>div');
    let currentDataSlickIndex = sceneActive.data('slick-index');
    let currentDataSlickIndexHz = sceneNew.data('slick-index');
    let totalHeightSlickSlide = 0;
    let topActiveSlickSlide = 0;
    let totalLeftSlickSlide = 0;
    let leftActiveSlickSlide = 0;
    if (listSlickSlide.length > 0) {
        listSlickSlide.each(function (i, item) {
            totalHeightSlickSlide += $(this).outerHeight()
            if (i < currentDataSlickIndex) {
                topActiveSlickSlide += $(this).outerHeight();
            }
        })
    }
    if (listSlickSlideHz.length > 0) {
        listSlickSlideHz.each(function (i, item) {
            totalLeftSlickSlide += $(this).outerWidth();
            if (i < currentDataSlickIndexHz) {
                leftActiveSlickSlide += $(this).outerWidth();
            }

        })
    }
    sceneNew.parent().css({
        'height': dataPreviewHeight + 'px',
        'left': `-${leftActiveSlickSlide}px`,
    })
    sceneActive.parent().css({
        'width': 'auto',
        'height': `${totalHeightSlickSlide}px`,
        'top': `-${topActiveSlickSlide + currentDataSlickIndex}px`
    })
    sceneActive.parent().parent().css({
        'height': dataPreviewHeight + 'px',
        'width': 'auto'
    })
    let dataMaxHeightVariations = sceneActive.children().attr('data-max-height-variations')
    $('.list-variation').css({
        'max-height': `${dataMaxHeightVariations}`,
    })
}

function setStyleParentScene(sceneNew, sceneActive, maxWidthViewport, dataPreviewHeight, dataPreviewWidth) {
    sceneNew.parent().parent().css({
        'height': dataPreviewHeight + 'px',
        'width': `${maxWidthViewport}px`,
    })
    sceneActive.css({
        'width': '100%',
        'height': dataPreviewHeight + 'px',
    })
    sceneActive.children().children().css({
        'width': dataPreviewWidth + 'px',
        'height': dataPreviewHeight + 'px',
    })
    let listSlickSlide = sceneActive.parent().find('.cscene__version.slick-slide');
    listSlickSlide.css({
        'margin-bottom': '1px',
    })
    let listSlickSlideHz = sceneNew.parent().find('>div');
    let currentDataSlickIndex = sceneActive.data('slick-index');
    let currentDataSlickIndexHz = sceneNew.data('slick-index');
    let totalHeightSlickSlide = 0;
    let topActiveSlickSlide = 0;
    let totalLeftSlickSlide = 0;
    let leftActiveSlickSlide = 0;
    if (listSlickSlide.length > 0) {
        listSlickSlide.each(function (i, item) {
            totalHeightSlickSlide += $(this).height()
            if (i < currentDataSlickIndex) {
                topActiveSlickSlide += $(this).height();
            }
        })
    }
    if (listSlickSlideHz.length > 0) {
        listSlickSlideHz.each(function (i, item) {
            $(this).css({
                'width': `${maxWidthViewport}px`,
            })
            totalLeftSlickSlide += $(this).outerWidth();
            if (i < currentDataSlickIndexHz) {
                leftActiveSlickSlide += $(this).outerWidth();
            }

        })
    }
    sceneNew.parent().css({
        'height': dataPreviewHeight + 'px',
        'left': `-${leftActiveSlickSlide}px`,
    })
    sceneActive.parent().css({
        'width': 'auto',
        'height': `${totalHeightSlickSlide}px`,
        'top': `-${topActiveSlickSlide + currentDataSlickIndex}px`
    })
    sceneActive.parent().parent().css({
        'height': dataPreviewHeight + 'px',
        'width': 'auto'
    })
}

function removePaddingTakePC() {
    if ($(document).width() > maxWidthSPDevice) {
        let sceneVideo = $('.cscene--video--new')
        if ($('.list-variation').prop('scrollHeight') > 350) {
            sceneVideo.addClass('pdb-take');
        } else {
            sceneVideo.removeClass('pdb-take')
        }
    }
}

function setPositionTooltipVariationScrollPC() {
    let listVariation = $('.list-variation');
    listVariation.on('scroll', function () {
        $(this).find('.variation-button-name-tooltip-container').css({
            'display': 'none'
        })
    });
}

function checkPositionTooltipVariation(listToolTip, listVariation) {
    let isIpadDevice = false;
    if ( $(document).width() < maxWidthIpadDevice && $(document).width() > maxWidthSPDevice){
        isIpadDevice = true;
    }
    let variationActive = $('.variation-button-container.active');
    let pdComment = $('.pd-comment');
    let topVariationList = getOffset(listVariation[0]).top;
    let topVariationActive = getOffset(variationActive[0]).top;
    let topPdComment = getOffset(pdComment[0]).top;
    let bottomVariationList = getOffset(listVariation[0]).top + listVariation.outerHeight();
    listToolTip.find('.variation-button-name-tooltip-container').css({
        'display': 'none'
    })
    if (isIpadDevice) {
        if (topVariationActive > topVariationList && topVariationActive < bottomVariationList) {
            variationActive.find('.variation-button-name-tooltip-container').css({
                'display': 'flex',
            })
        }
    } else {
        if (topVariationActive > topVariationList && topVariationActive < topPdComment) {
            variationActive.find('.variation-button-name-tooltip-container').css({
                'display': 'flex',
            })
        }
    }
}

function setPositionTooltipVariationScroll() {
    let listVariation = $('.list-variation');
    listVariation.on('scroll', function () {
        let listToolTip = $('.variation-button-container');
        listToolTip.each(function (i, el) {
            calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
            $(this).find('.variation-button-name-tooltip-container').css({
                'transform': ''
            })
        })
        checkPositionTooltipVariation(listToolTip, listVariation);
    });
}

// function setPositionTooltipVariationTouchmove() {
//     let listVariation = $('.list-variation');
//     let listToolTip = $('.variation-button-container');
//     listToolTip.each(function (i, el) {
//         calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
//         $(this).find('.variation-button-name-tooltip-container').css({
//             'transform': ''
//         })
//     })
//     checkPositionTooltipVariation(listToolTip, listVariation);
// }

function calculatePositionTooltipVariation(variation, tooltipDom){
    const mainDiv = $('main')[0];
    const posLeft = getOffset(variation[0]).left;
    const posTop = getOffset(variation[0]).top - window.scrollY;
    const buttonWidth = variation[0].offsetWidth;
    if(!tooltipDom) {
        return;
    }
    const tooltipWidth = tooltipDom.offsetWidth;
    const tooltipHeight = tooltipDom.offsetHeight;

    if (tooltipWidth <= buttonWidth) {
        tooltipDom.style.top = posTop - tooltipHeight - 2 + 'px';
        tooltipDom.style.left = posLeft + (buttonWidth - tooltipWidth) / 2 + 'px';
    } else {
        if(tooltipWidth + 4 < mainDiv.offsetWidth) {
            if(posLeft + (buttonWidth - tooltipWidth) / 2 > 0) {
                if(mainDiv.offsetWidth - (posLeft + (buttonWidth - tooltipWidth) / 2) < tooltipWidth) {
                    tooltipDom.style.top = posTop - tooltipHeight - 2 + 'px';
                    const countLeft = mainDiv.offsetWidth - (posLeft + (buttonWidth - tooltipWidth) / 2);
                    tooltipDom.style.left = posLeft + (buttonWidth - tooltipWidth) / 2 - (tooltipWidth - countLeft) - 2 + 'px';
                    tooltipDom.style.width = 'max-content';
                } else {
                    tooltipDom.style.width = 'max-content';
                    tooltipDom.style.top = posTop - tooltipHeight - 2 + 'px';
                    tooltipDom.style.left = posLeft + (buttonWidth - tooltipWidth) / 2 + 'px';
                }
            } else {
                tooltipDom.style.width = 'max-content';
                tooltipDom.style.top = posTop - tooltipHeight - 2 + 'px';
                tooltipDom.style.left = 2 + 'px';
            }
        } else {
            tooltipDom.style.width = mainDiv.offsetWidth - 4 + 'px';
            tooltipDom.style.top = posTop - tooltipHeight - 2 + 'px';
            tooltipDom.style.left = 2 + 'px';
        }
    }
}

function actionHoverVariation() {
    if ($(document).width() > maxWidthIpadDevice) {
        $(document).on('mouseenter', '.variation-button-container', function (e) {
            $(this).find('.variation-button-name-tooltip-container').css({
                'display': 'flex',
                'transform': ''
            })
            calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
            $(this).find('.variation-button-name-tooltip-container').css({
                'display': 'flex',
            })
        });
        $(document).on('mouseleave', '.variation-button-container', function (e) {
            $(this).find('.variation-button-name-tooltip-container').css({
                'display': 'none'
            })
        })
    }
}

function changeTab(tabIndex) {
    let tabs = document.getElementsByClassName("tab-comment");

    for (let i = 0; i < tabs.length; i++) {
        if (i + 1 === tabIndex) {
            if (tabs[i].classList.contains('active')) {
                event.preventDefault();
                return;
            }
            tabs[i].classList.add("active");
        } else {
            tabs[i].classList.remove("active");
        }
    }
}

$(document).ready(function () {
    let btnReplyMsg = $('.scene-style .li-reply-message');
    let btnEditMsg = $('.scene-style .li-edit-message');
    let btnResolveMsg = $('.scene-style .li-resolve-message');

    btnReplyMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-reply .txt-reply-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-reply .img-reply-comment')[0].style.background = "url('/static/images/scene-detail/icon-reply-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-reply .txt-reply-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-reply .img-reply-comment')[0].style.background = "url('/static/images/scene-detail/icon-reply.svg')";
    });

     btnEditMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-edit .txt-edit-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-edit .img-edit-comment')[0].style.background = "url('/static/images/scene-detail/icon-edit-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-edit .txt-edit-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-edit .img-edit-comment')[0].style.background = "url('/static/images/scene-detail/icon-edit.svg')";
    });

      btnResolveMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-resolve .txt-item-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-resolve .img-resolve-comment')[0].style.background = "url('/static/images/scene-detail/icon-resolve-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-resolve .txt-item-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-resolve .img-resolve-comment')[0].style.background = "url('/static/images/scene-detail/icon-resolve.svg')";
    });

    hoverBtnActionMessage()
    clickBtnActionMessage()
})