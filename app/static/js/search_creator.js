// function searchCreator() {
//     let isSearching = false;
//     $(document).on('keyup', '#pm-search-creator', function (e) {
//         if (e.keyCode == 13 && !isSearching) {
//             isSearching = true;
//             $('.psearch-section.psearch-artist-wrap').remove();
//             $('.psearch-gallery').remove();
//             $('.psearch-artist').remove();
//             let stage_name = $('#pm-search-creator').val();
//             let project_id = $('.project-item.active').attr('data-project-id');
//             if (stage_name.trim() === '' && !$('.skills-item.selected').length || !project_id) {
//                 isSearching = false;
//                 return
//             }
//             let skill_ids = [];
//             $('.skills-item.selected').map(function () {skill_ids.push($(this).attr('data-id'))});
//             $.ajax({
//                 type: 'GET',
//                 datatype: 'json',
//                 url: '/messenger/search_creator' ,
//                 data: {
//                     'stage_name': stage_name,
//                     'project_id': project_id,
//                     'skill_ids': skill_ids
//                 },
//                 beforeSend: function (xhr, settings) {
                        //     xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        // },
                        // success: function (response) {
//                     $(response.creators_html).insertAfter($('.psearch-form'));
//                     isSearching = false;
//                 },
//                 error: function () {
//                     isSearching = false;
//                 }
//             })
//         }
//     });
// }

function searchCreator() {
    let isSearching = false;
    function executeSearch() {
        if (!isSearching) {
            isSearching = true;
            $('.psearch-section.psearch-artist-wrap').remove();
            $('.psearch-gallery').remove();
            $('.psearch-artist').remove();
            let stage_name = $('#pm-search-creator').val();
            let project_id = $('.project-item.active').attr('data-project-id');
            if (stage_name.trim() === '' && !$('.skills-item.selected').length || !project_id) {
                isSearching = false;
                return;
            }
            let skill_ids = [];
            $('.skills-item.selected').map(function () { skill_ids.push($(this).attr('data-id')) });
            $.ajax({
                type: 'GET',
                datatype: 'json',
                url: '/messenger/search_creator',
                data: {
                    'stage_name': stage_name,
                    'project_id': project_id,
                    'skill_ids': skill_ids
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    $(response.creators_html).insertAfter($('.psearch-form'));
                    isSearching = false;
                },
                error: function () {
                    isSearching = false;
                }
            });
        }
    }

    $(document).on('keyup', '#pm-search-creator', function (e) {
        if (e.keyCode == 13) {
            executeSearch();
        }
    });

    $(document).on('click', '.c-btn-primary', function () {
        executeSearch();
        this.blur(); // フォーカスを解除
    });
}


function playSaleContent() {
    $('.psearch-main').on('click', '.gallery__item:not(.playing, .loading)', function () {
        const thumbnail = $(this);
        const audio = thumbnail.find('audio');
        $('.playing').removeClass('playing')
        $('audio').each(function (i, e) {
            e.pause();
        });

        audio.on('canplay', function () {
            audio.off('canplay');
            audio[0].play();
            console.log('loaded');
            thumbnail.removeClass('loading');
            thumbnail.addClass('playing');

        });

        toastr.info(thumbnail.attr('data-artist'), audio.attr('data-name'), {
            newestOnTop: true,
            timeOut: 5000,
            extendedTimeout: 10000,
        });

        audio[0].play();
        audio[0].muted = false;
        audio[0].loop = true;

        if (audio[0].readyState > 1) {
            thumbnail.removeClass('loading');
            thumbnail.addClass('playing');


        } else {
            thumbnail.removeClass('playing');
            thumbnail.addClass('loading')
        }
    });

    $('.psearch-main').on('click', '.playing', function () {
        const audio = $(this).parent().find('audio');
        audio.off('canplay');
        audio.each(function (i, e) {
            e.pause()
        });
        $(this).removeClass('playing loading');
    })
}

function showModalContract(id_form) {

    $(document).on('click', '.btn-create-contact', function () {
        let modal_create = $('#modal-create-offer');
        $('.mcreator.active').removeClass('active');
        $(this).parents('.mcreator').addClass('active');
        actionForm(modal_create);
        resetForm(modal_create)
        is_editing = false;
        intitActionForm();
    });
}

function actionCreateOfferCreator() {
    $(document).on('click', '#modal-create-offer .create-offer-submit:not(.disabled)', function () {
        let modal_create = $('#modal-create-offer');
        $('.errorlist').remove();
        $('.error-border').removeClass('error-border');
        let buttom_dom = $(this);
        let data_form = new FormData();
        let file = myDropzone.files[0];
        let deadline = $('#deadline-date').val();
        let time_deadline = $('.select-deadline_time').val();
        let contract = $('#id_contract').val();
        let scenes = $('#input-scenes').val();
        // let quantity = $('#input-quantity').val();
        let data_format = $('#id_data_format').val();
        let message = $('#input-message').val();
        let reward = $('#id_total_amount').text();
        let project_id = modal_create.attr('data-project');
        let creator_id = $('.mcreator.active').attr('data-creator');
        let valid_date = $('#id_valid_offer').val();
        let valid_time = $('#valid_time_offer').val();
        let note = $('#input-remarks').val() ?? '';
        let pick_up_method = $('#id_pickup_offer').val();
        // let note_type = $('.tab-note-form-offer .tab-content').find('.tab-pane.active').attr('id').trim();
        let delivery_place = $('#id_delivery_place_offer').val();
        let period = $('#id_period_offer').val();
        let allow_subcontracting = $($('.allow_subcontracting')[0]).hasClass('checked') ? 1 : 0;
        let selected_job_type = []
        $('.skills-item-offer.selected').each(function() {
            selected_job_type.push($(this).attr('data-id'));
        })
        valid_date = valid_date + ' ' + valid_time;
        let is_blank = checkValidateBlank(['#budget', '#id_contract']);
        if (is_blank || !creator_id) {
            return
        }

        buttom_dom.addClass('disabled');

        data_form.append('creator_id', creator_id);
        data_form.append('project_id', project_id);
        data_form.append('range_deadline', period);
        data_form.append('contract', contract);
        data_form.append('scenes', scenes);
        // data_form.append('quantity', quantity);
        data_form.append('data_format', data_format);
        data_form.append('message', message);
        data_form.append('reward', reward);
        data_form.append('deadline', deadline);
        data_form.append('time_deadline', time_deadline);
        data_form.append('valid_date', valid_date);
        data_form.append('note', note);
        data_form.append('pick_up_method', pick_up_method);
        data_form.append('delivery_place', delivery_place);
        data_form.append('allow_subcontracting', allow_subcontracting);
        data_form.append('selected_job_type', selected_job_type);

        $.ajax({
            type: "POST",
            contentType: false,
            processData: false,
            cache: false,
            url: "/check_offer_creator",
            data: data_form,
            success: function (data) {
                for (var i = 0; i< added_files.length; i++) {
                    data_form.append('key_files[]', added_files[i].key);
                    data_form.append('real_names[]', added_files[i].real_name);
                }
                if (data.budget === 'over') {
                    removeProgress();
                    buttom_dom.removeClass('disabled');
                    $("#over-budget").modal();
                } else {
                    //TODO: delete when complete offer form.
                    if (is_uploading) {
                        let timeoutUpload = setTimeout(function () {
                            clearInterval(waiting_file_loading);
                            toastr.error(gettext('Something went wrong!'));
                        }, 900000);
                        let waiting_file_loading = setInterval(function () {
                            let progress = getProgressUploaded();
                            let progressDom = $('.upload-button-wrapper');
                            activeProgress();
                            progressDom.find('.fill .process').css('width', progress + '%');
                            if (!is_uploading) {
                                clearTimeout(timeoutUpload);
                                clearInterval(waiting_file_loading);
                                sendAjaxCreateEditOffer(data_form, buttom_dom, true, 'create')
                            }
                        }, 100);
                    } else {
                        sendAjaxCreateEditOffer(data_form, buttom_dom, false, 'create')
                    }
                }
            },
            fail: function (data) {
                toastr.error(gettext('Something went wrong!'));
                buttom_dom.removeClass('disabled');
            },
            error: function(xhr, status, error) {
                var err = eval("(" + xhr.responseText + ")")
                alert(err.message);
                buttom_dom.removeClass('disabled');
            },
            complete: function () {
            }
        });


    });
}

function intitActionForm() {
    // $('#id_valid_offer').datepicker('remove')
    // $('#id_valid_offer').datepicker({
    //     format: 'yyyy/mm/dd',
    //     locale: 'ja',
    //     forceParse: false,
    // })
    var date = new Date();
    var deadlineDate = new Date($('#deadline-date').val())
    setRangeEndDateValidOffer(null);
    setRangeDateValidOffer(new Date());
    if (!$('#id_valid_offer').val()) {
        $("#id_valid_offer").val(formatDate(deadlineDate));
        // $('#id_valid_offer').datepicker('setDate', formatDate(deadlineDate));
    } else {
        if(new Date(formatDate($('#id_valid_offer').val())).getTime() <= new Date().getTime()) {
            if (is_editing) {
                $("#id_valid_offer").val(formatDate(new Date()));
            } else {
                $("#id_valid_offer").val(formatDate(new Date().addDays(5)));
            } 
            // $('#id_valid_offer').datepicker('setDate', formatDate(new Date()));
        }
        else {
            $("#id_valid_offer").val(formatDate($("#id_valid_offer").val()));
            // $('#id_valid_offer').datepicker('setDate', formatDate($('#id_valid_offer').val()));
        }
    }
    // if(!is_editing) {
    //     setRangeDateValidOffer(new Date());
    // } else {
    //     if (new Date(formatDate(new Date())).getTime() >= new Date($('#deadline-date').val()).getTime()) {
    //         setRangeDateValidOffer(new Date($('#id_valid_offer').val()));
    //         setRangeEndDateValidOffer(new Date($('#id_valid_offer').val()));
    //
    //     } else {
    //         setRangeDateValidOffer(new Date());
    //         setRangeEndDateValidOffer(null);
    //     }
    // }
    
    $(document).on('input', '#id_valid_offer', function(e) {
        if (this.value.length > 10) {
            this.value = this.value.slice(0, 10);
        }
        this.value = this.value.replace(/[^/0-9]/gm, '');
    });

    $(document).on('change input', '#id_note_offer', function(){
        var $this = $(this)
        $this.parents('.tab-content').find('.tab-pane:not(.active) #id_note_offer').each(function() {
           $(this).val($this.val());     
        })
    });

    if (!$('.select-deadline_time').val()) {
        $('.select-deadline_time').val('10:00');
    }

    if($('#deadline-date').val() && !is_editing) {
        let startDate = moment(new Date());
        let endDate = moment(new Date($('#deadline-date').val()));
        var defaultDate = [
          new Date(),
          moment(new Date()).add(1, "months").toDate(),
        ];
        if (startDate && endDate) {
          defaultDate = [moment(startDate).toDate(), moment(endDate).toDate()];
        }
       flatpickr("#id_valid_offer", {
         mode: "single",
         dateFormat: "Y/m/d",
         defaultDate: $("#id_valid_offer").val(),
         showMonths: 1,
         onOpen: function (selectedDates, dateStr, instance) {
           $(instance.element)
             .next(".c-icon-date-range")
             .addClass("is-icon-active");
         },
         onClose: function (selectedDates, dateStr, instance) {
           $(instance.element)
             .next(".c-icon-date-range")
             .removeClass("is-icon-active");
         },
         onChange: function (selectedDates, dateStr, instance) {
           let startDate = selectedDates[0];
           let endDate = selectedDates[1];
           $(instance.element).attr(
             "data-start-time",
             moment(startDate).format("yyyy-MM-DD HH:mm:ss")
           );
           if (endDate)
             $(instance.element).attr(
               "data-end-time",
               moment(endDate).format("yyyy-MM-DD HH:mm:ss")
             );
         },
         // minDate: "today",
         // maxDate: new Date().fp_incr(120),
       });
       flatpickr("#id_period_offer", {
         mode: "range",
         dateFormat: "Y/m/d",
         defaultDate: defaultDate,
         showMonths: 1,
         onOpen: function (selectedDates, dateStr, instance) {
           $(instance.element)
             .next(".c-icon-date-range")
             .addClass("is-icon-active");
         },
         onClose: function (selectedDates, dateStr, instance) {
           $(instance.element)
             .next(".c-icon-date-range")
             .removeClass("is-icon-active");
         },
         onChange: function (selectedDates, dateStr, instance) {
           let startDate = selectedDates[0];
           let endDate = selectedDates[1];
           $(instance.element).attr(
             "data-start-time",
             moment(startDate).format("yyyy-MM-DD HH:mm:ss")
           );
           if (endDate)
             $(instance.element).attr(
               "data-end-time",
               moment(endDate).format("yyyy-MM-DD HH:mm:ss")
             );
         },
         // minDate: "today",
         // maxDate: new Date().fp_incr(120),
       });
    }

    $(document).on('change input', '#deadline-date', function(e) {
        // if(new Date(e.target.value).getTime() <= new Date(formatDate(new Date())).getTime()){
        //     setRangeDateValidOffer(new Date($('#id_valid_offer').val()));
        //     setRangeEndDateValidOffer(new Date($('#id_valid_offer').val()));
        // } else {
        //     setRangeDateValidOffer(new Date());
        //     setRangeEndDateValidOffer(null);
        // }
    })

    initTimeValid();
    initMinTimeValidDate();
}

function initMinTimeValidDate () {
    $("#valid_time_offer").datetimepicker({format: "HH:mm"});
    $(document).on('change input dp.change', '#id_valid_offer, .select-deadline_time, #deadline-date', function() {
        let timeDeadline = $('.select-deadline_time').val();
        let dateValid = moment(new Date($('#id_valid_offer').val())).format('YYYY/MM/DD');
        let dateDeadline =  moment(new Date($('#deadline-date').val())).format('YYYY/MM/DD');
        if (timeDeadline && dateValid && dateDeadline) {
            if (dateDeadline === dateValid) {
                setTimeout(() => {
                    $("#valid_time_offer").data('DateTimePicker').maxDate(timeDeadline);

                    let valValid = $('#id_valid_offer').val() + " " + $("#valid_time_offer").val()
                    let valDeadline = $('#deadline-date').val() + " " + $('.select-deadline_time').val()

                    if(new Date(valValid) > new Date(valDeadline)) {
                        $('#valid_time_offer').val($('.select-deadline_time').val());
                    }
                }, 500);
            } else {
                setTimeout(() => {
                    $("#valid_time_offer").data('DateTimePicker')?.maxDate(false);
                }, 500);
            }
        }
    })
}

function initTimeValid() {
    if(!is_editing){
        $('#valid_time_offer').val('10:00');
    }
    if (!$('#valid_time_offer').val()) {
        $('#valid_time_offer').val('10:00');
    }

    $(document).on('focusout', '#id_valid_offer, #deadline-date', function() {
        if(!$(this).val() || !(moment(new Date($(this).val()), 'YYYY/M/D', true).isValid())) {
            setInitialDateOffer($(this))
        } else if ($(this).is('#id_valid_offer') && 
        (new Date($(this).val()).getTime() < new Date(formatDate(new Date())).getTime() || new Date($(this).val()).getTime() > new Date($('#deadline-date').val()).getTime())) {
            setInitialDateOffer($(this))
        } else if($(this).is('#deadline-date') && new Date($(this).val()).getTime() <= new Date($('#id_valid_offer').val()).getTime()) {
            setInitialDateOffer($('#id_valid_offer'));
            setInitialDateOffer($('#valid_time_offer'));
        }
    })

    $(document).on('dp.change', '#valid_time_offer, .select-deadline_time', function() {
        if(!$(this).val() || !moment($(this).val(), 'HH:mm', true).isValid()) {
            setInitialDateOffer($(this))
        }
    })
}

function setInitialDateOffer(target_input) {
    let startDate = moment(new Date());
    if(target_input.is('#valid_time_offer')) {
        target_input.val($('.select-deadline_time').val());
    } else if(target_input.is('#deadline-date')) {
        $("#deadline-date").val(
          moment(moment(new Date()).add(5, "days")).format("YYYY/M/D")
        );
        // $('#deadline-date').datepicker('setDate',  moment(moment(new Date()).add(5, 'days')).format('YYYY/M/D'));
    } else if(target_input.is('.select-deadline_time')){
        $('.select-deadline_time').val('10:00');
    } else if(target_input.is('#id_period_offer')) {
        let startDate = moment(new Date());
        let endDate = moment(new Date($('#deadline-date').val()));
        var defaultDate = [
          new Date(),
          moment(new Date()).add(1, "months").toDate(),
        ];
        if (startDate && endDate) {
          defaultDate = [moment(startDate).toDate(), moment(endDate).toDate()];
        }
        flatpickr("#id_period_offer", {
          mode: "range",
          dateFormat: "Y/m/d",
          defaultDate: defaultDate,
          showMonths: 1,
          onOpen: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .addClass("is-icon-active");
          },
          onClose: function (selectedDates, dateStr, instance) {
            $(instance.element)
              .next(".c-icon-date-range")
              .removeClass("is-icon-active");
          },
          onChange: function (selectedDates, dateStr, instance) {
            let startDate = selectedDates[0];
            let endDate = selectedDates[1];
            $(instance.element).attr(
              "data-start-time",
              moment(startDate).format("yyyy-MM-DD HH:mm:ss")
            );
            if (endDate)
              $(instance.element).attr(
                "data-end-time",
                moment(endDate).format("yyyy-MM-DD HH:mm:ss")
              );
          },
          // minDate: "today",
          // maxDate: new Date().fp_incr(120),
        });
        // $('#id_period_offer').daterangepicker({
        //     startDate: startDate,
        //     endDate: endDate,
        // });
    }
}

function setRangeDateValidOffer(startDate) {
    $("#id_valid_offer").val(startDate);
    // $('#id_valid_offer').datepicker('setStartDate', startDate);
}

function setRangeEndDateValidOffer(endDate) {
    $("#id_valid_offer").val(endDate);
    // $('#id_valid_offer').datepicker('setEndDate', endDate);
    // $('#id_valid_offer').datepicker("option", "maxDate", endDate);
}
    

function sendAjaxCreateEditOffer(data_form, buttom_dom, uploading, typeForm) {
    let url = typeForm === 'edit' ? '/messenger/edit_information_offer' : '/offer_creator';
    $('#modal-confirm-upload').find('iframe').attr('src', 'about:blank');
    $('#btn__submit-upload').addClass('disable');
    $('#modal-confirm-upload').modal('show');
    $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
    $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
    $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').addClass('btn--disabled')
    $('#modal-confirm-upload').removeClass('no-button')
    $('#modal-confirm-upload .checkbox-and-button-container').removeClass('hide')
    $('#modal-confirm-upload .checkbox-and-button-container .btn-text').text('オファーを送る')
    $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').removeClass('icon-send-plane icon-handshake')
    $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
    request_counter++
    let current_counter = String(request_counter)
    $('#modal-confirm-upload').attr('data-request-id', current_counter);
    $.ajax({
        type: "POST",
        contentType: false,
        processData: false,
        cache: false,
        url: url,
        data: data_form,
        beforeSend: function (data) {
            // toastr.info(gettext('Uploading ...'));
        },
        success: function (data) {
            
            let timeout = 50;
            if (uploading) {
                let progressDom = $('.upload-button-wrapper');
                progressDom.find('.fill .process').css('width', '100%');
                setTimeout(function () {
                    progressDom.removeClass('clicked').addClass('success')
                }, 1000);
                setTimeout(function () {
                    removeProgress();
                }, 2000);
                timeout = 2000;
            }

            if(waiting_cancel_request.includes(current_counter)) {
                if(data.form_create_type == 'create-contract-artist') {
                    $.ajax({
                        type: 'POST',
                        datatype: "json",
                        url: '/direct/offer_creator_reject',
                        data: {
                            'project_id': data.project_id,
                            'offer_id': data.offer_id,
                            'real_names': data.real_names,
                            'form_create_type': data.form_create_type
                        },
                        success: function(data) {
                            console.log('successfully cancelled offer' + current_counter)
                            $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                        },
                        error: function() {
                            console.log('error when cancel offer' + current_counter)
                            $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                        }
                    })
                } else {
                    $.ajax({
                        type: 'POST',
                        datatype: "json",
                        url: '/messenger/offer_creator_edit_reject',
                        data: {
                            'new_offer_id': data.new_offer_id,
                        },
                        success: function(data) {
                            console.log('successfully cancelled offer edit' + current_counter)
                            $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                        },
                        error: function() {
                            console.log('error when cancel offer edit' + current_counter)
                            $('#modal-create-offer .create-offer__action .create-offer-submit').removeClass('disabled')
                        }
                    })
                }
                
                const index = waiting_cancel_request.indexOf(current_counter);
                if (index > -1) {
                    waiting_cancel_request.splice(index, 1);
                }
            } else {
                if(data.form_create_type == 'create-contract-artist') {
                    setTimeout(function() {
                        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
                        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
                        $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
                        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').removeClass('btn--disabled')
                        $('#modal-confirm-upload').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
                        $('#modal-confirm-upload').attr('data-project-id', data.project_id)
                        $('#modal-confirm-upload').attr('data-real-name', data.real_names.join('/'))
                        $('#modal-confirm-upload').attr('data-offer-id', data.offer_id)
                        $('#modal-confirm-upload').attr('data-type', data.form_create_type)
                        $('#modal-upload-plan, #modal-upload-contract, #modal-drag-file').attr('data-type', '');
                        if(user_role === 'admin' || user_role === 'master_admin') {
                            scrollBottomPDFArtist($('#modal-confirm-upload').find('iframe'), $('#modal-confirm-upload'));
                        }
                    }, timeout)
                } else if(data.form_create_type == 'edit-contract-artist') {
                    setTimeout(function() {
                        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox')[0].checked = false;
                        $('#modal-confirm-upload .checkbox-and-button-container .btn-content i').addClass('icon-send-plane disable')
                        $('#modal-confirm-upload .checkbox-and-button-container label.form-check-label span').text('内容をチェックしました')
                        $('#modal-confirm-upload .checkbox-and-button-container #form-approve-checkbox').siblings('label.form-check-label').removeClass('btn--disabled')
                        $('#modal-confirm-upload').find('iframe').attr('src', '/static/pdfjs/web/viewer.html?file=' + encodeURIComponent(data.url) + '#zoom=page-width');
                        $('#modal-confirm-upload').attr('data-new-offer-id', data.new_offer_id)
                        $('#modal-confirm-upload').attr('data-is-delete-file', data.is_delete_file)
                        $('#modal-confirm-upload').attr('data-offer-id', data.offer_id)
                        $('#modal-confirm-upload').attr('data-type', data.form_create_type)
                        $('#modal-upload-plan, #modal-upload-contract, #modal-drag-file').attr('data-type', '');
                        if(user_role === 'admin' || user_role === 'master_admin') {
                            scrollBottomPDFArtist($('#modal-confirm-upload').find('iframe'), $('#modal-confirm-upload'));
                        }
                    }, timeout)
                }
            }
        },
        fail: function (data) {
            toastr.error(gettext('Something went wrong!'));
        },
        error: function (data) {
            toastr.error(gettext('Something went wrong!'));
            buttom_dom.removeClass('disabled');
        },
        complete: function () {
            removeProgress();
            myDropzone.removeAllFiles();
            myDropzone.files = []
        }
    });
}


function resultCreateOffer(data) {
    if (data.budget === 'over') {
        $(this).removeClass('disabled');
        $("#over-budget").modal();
    } else {
        // toastr.success(gettext('I made an offer.'));
        setTimeout(function () {
            window.location.href = data.url;
        }, 1000);
    }
}

function resultCreateOfferWithUploading(data) {
    let progressDom = $('.upload-button-wrapper');
    if (data.budget === 'over') {
        removeProgress();
        $(this).removeClass('disabled');
        $("#over-budget").modal();
    } else {
        progressDom.find('.fill .process').css('width', '100%');
        setTimeout(function () {
            // toastr.success(gettext('I made an offer.'));
            progressDom.removeClass('clicked').addClass('success')
        }, 1000);
        setTimeout(function () {
            removeProgress();
            window.location.href = data.url;
        }, 2000);
    }
}

// Tab Skills
function activeTab() {
    $('.tabs-skill').tab('show')
}

function activeTabOffer() {
    $($('.modal-create-edit-offer .tabs-skill-offer')[0]).tab('show');
}

// Count skill selected
function countSkills(tabId, countSelected) {
    tabId = $('.tab-pane.active').attr('id');
    countSelected = $(`#${tabId}`).find('.skills-item.selected').length;
    if (countSelected > 0) {
        $('.nav-item.active').find('.skill-selected').removeClass('hide')
    } else {
        $('.nav-item.active').find('.skill-selected').addClass('hide')
    }
    $('.nav-item.active').find('.skill-selected').attr('value', countSelected).text(countSelected)
}

function countSkillsOffer(tabId, countSelected) {
    // tabId = $('.tab-pane-offer.active').attr('id');
    countSelected = $(`#${tabId}`).find('.skills-item-offer.selected').length;
    if (countSelected > 0) {
        $(`.modal-create-edit-offer .nav-item .nav-link.${tabId}`).parent().find('.skill-selected').removeClass('hide')
    } else {
        $(`.modal-create-edit-offer .nav-item .nav-link.${tabId}`).parent().find('.skill-selected').addClass('hide')
    }
    $(`.modal-create-edit-offer .nav-item .nav-link.${tabId}`).parent().find('.skill-selected').attr('value', countSelected).text(countSelected)
}

// Selected Skill
function selectSkill() {
    isSelected = $(this).hasClass('selected');
    let dataId = $(this).attr('data-id');
    let checkbox = $(`#skill_${dataId}`);
    let tabId = $(this).parents().find('.tab-pane.active').attr('id');
    let count;

    if (isSelected) {
        $(this).removeClass('selected');
        checkbox.removeAttr('checked');
        count --;
    } else {
        $(this).addClass('selected');
        checkbox.attr('checked', 'checked');
        count ++;
    }
    countSkills(tabId, count);
}

function selectSkillOffer() {
    let currentValueContract = $('#id_contract').val();
    listType = currentValueContract.split('/').filter((val)=> val.trim()).map((val)=>val.trim());
    if(!$(this).hasClass('selected') && !listType.includes($(this).html())){
        currentValueContract += (!currentValueContract ? '' : ' / ') + $(this).html();
        $('#id_contract').val(currentValueContract);
    }

    isSelected = $(this).hasClass('selected');
    let dataId = $(this).attr('data-id');
    let checkbox = $(`#skill_${dataId}`);
    let tabId = $(this).closest('.tab-pane-offer').attr('id');
    let count;

    if (isSelected) {
        $(this).removeClass('selected');
        checkbox.removeAttr('checked');
        count --;
    } else {
        $(this).addClass('selected');
        checkbox.attr('checked', 'checked');
        count ++;
    }
    countSkillsOffer(tabId, count);
}
$(document).on("click", '.skills-item', selectSkill);
$(document).on("click", '.skills-item-offer:not(.disabled)', selectSkillOffer);

function scrollTopButton() {
    $(document).on('click', '.button-scroll-top-container', function(e) {
        $($(document).find('.modal-create-edit-offer .create-offer')[0]).animate({
            scrollTop: 0
        }, 500);
    })
}

function onCloseModalOffer(){
    $(document).on('hidden.bs.modal', '.modal-create-edit-offer', function(){
        $(document).find('#budget').parent().removeClass('error-input');
        $(document).find('.error-budget').remove();
        $(document).find('#budget').removeClass('error');
        $(document).find('#id_total_amount').html('0');
        $('.modal-create-edit-offer').find('.job-title-container .tab-content-offer .skills-item-offer').each(function() {
            if($(this).hasClass('selected')){
                $(this).trigger('click');
            }
            $(this).removeClass('disabled');
        })
    })
}

$(document).ready(function () {
    searchCreator();
    playSaleContent();
    activeTab();
    activeTabOffer();
    showModalContract('modal-create-offer');
    actionCreateOfferCreator();
    intitActionForm();
    scrollTopButton();
    onCloseModalOffer();
});
