const percentMaxWidthPreviewPC = 0.618;
const maxHeightVariationsPC = 358;
const maxWidthDeviceSP = 767;
const minHeightAudio = 165;
const maxWidthCommentAreaPC = 640;
const percentMaxHeightPreviewSP = 0.382;
const totalPaddingHorizontalScreen = 64;
const totalPaddingVerticalScreen = 32;
const totalPaddingVerticalPreview = 8;
const totalPaddingHorizontalPreview = 8;
const paddingHorizontalAudio = 32;
const maxWidthIpadDevice = 821
const halfPaddingHorizontalScreen = totalPaddingHorizontalPreview / 2;
const totalPaddingVerticalScene = 10;
const paddingTopVariationList = 8;
var isShowClosePopup = false;
const max_width_resize_sp = 695;
const max_width_tablet_device = 908;
function projectDetail() {
    $('.pd-chapter').each(function (i) {
        if (i == 0) {
            $(this).find('.pd-chapter__content').show();
        }
    });

    projectChapterToggle();
    projectChapterEditDelete();
    projectChapterCreate();
    projectFilter();
    projectOrder();
}

function projectChapterToggle() {

    $(document).on('click', '.pd-section__header .pd-chapter__toggle', function () {
        if (!$(this).hasClass('active')) {
            $(this).addClass('active');
            $(".pd-chapter").addClass('active');
            $(".pd-chapter").find('.pd-chapter__title').addClass('active');
            $(".pd-chapter").find('.pd-chapter__content').slideDown(300);
        } else {
            $(this).removeClass('active');
            $(".pd-chapter").removeClass('active');
            $(".pd-chapter").find('.pd-chapter__title').removeClass('active');
            $(".pd-chapter").find('.pd-chapter__content').slideUp(300);
        }
    });
}

function projectChapterEditDelete() {
    $(document).on('click', '.pd-chapter__title', function () {
        let action_element = $(this).find(".pd-chapter__action");
        if(action_element.hasClass("active")){
            action_element.removeClass("active");
        }else{
            action_element.addClass("active");
        }
    });

    $(document).on('mouseenter mouseleave', '.pd-chapter__title', function (e) {
        let action_element = $(this).find(".pd-chapter__action");
        if(action_element.hasClass("active") && e.type === 'mouseleave'){
            action_element.removeClass("active");
        }
        action_element.toggleClass("active-hover", e.type === 'mouseenter');
    });

    $(document).on('click', '.pd-chapter__title .pd-chapter__edit', function (e) {
        $('.modal-container-edit').css('display', 'flex');
        var product_scene_id = $(this).parents('.pd-chapter').attr('data-product-scene-id');
        let product_scene_dom = $(this).parents('.pd-chapter__title').find(".pd-chapter__name").text();
        $('#edit-chapter').find('input.input-chapter-name').val(product_scene_dom);

        $('#edit-chapter .smodal-close').on('click', function(e){
            e.preventDefault();
            e.stopPropagation();
            $('.modal-container-edit').css('display', 'none');
        })

        $("#edit-chapter .btn-edit-product-scene").off('click').on("click", function(e) {
            let product_scene_text = $('#edit-chapter').find('.input-chapter-name').val().trim();
            if (product_scene_text != '' && product_scene_id) {
                $(this).addClass('active');
                $.ajax({
                    type: "POST",
                    url: '/ajax/edit_product_scene_name/',
                    data:
                        {
                            'name': product_scene_text,
                            'product_scene_id': product_scene_id
                        },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        if (response.code === '200') {
                            $('.modal-container.chapter__modal-container').css('display', 'none');
                            let product_scene = $('.pd-chapter[data-product-scene-id^=' + response.product_scene_id + ']');
                            product_scene.find('.pd-chapter__name').text(response.new_name);
                            let option = $('#product_scene option[data-value=' + response.product_scene_id + ']');
                            option.attr('data-name', response.new_name);
                            option.text('data-name', response.new_name);
                            // toastr.success('チャプター名を更新しました');
                        } else {
                            toastr.error('エラーが発生しました', 'チャプター名を編集');
                        }
                    },
                    error: function() {
                        toastr.error('エラーが発生しました', 'シーン並び替え');
                    },
                    complete: function () {
                        $('.btn-create-product-scene').removeClass('active');
                    }
                })

            }
        })
    });

    $(document).on('click', '.pd-chapter__title .pd-chapter__delete ', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let product_scene_id = $(this).parents('.pd-chapter').attr('data-product-scene-id');
        var product_scene_dom = $(this).parents('.pd-chapter__title');
        bootbox.confirm({
            message: "本当に削除しますか?",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result === true) {
                    $.ajax({
                        type: "POST",
                        url: "/ajax/delete_product_scene/",
                        data: {
                            'product_scene_id': product_scene_id
                        },
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            if (response.code === '200') {
                                $('.pd-chapter[data-product-scene-id^=' + response.product_scene_id + ']').remove();
                                $('.cvideo .project-delivery-item-content[data-product-scene=' + response.product_scene_id + ']').remove();
                                if (!$('.tab--video-watting_checkback .project-delivery-item-content').length) {
                                    $('.tab--video-watting_checkback').remove()
                                }
                                countSceneUpdate(response);
                                if (!response.real_delete) {
                                    $('.pd-section.pd-section--deleted-video').remove();
                                    $(response.html_deleted).insertAfter($('.pd-section.pd-section--all-video'));
                                } else {
                                    if (!$('.pd-section.pd-section--deleted-video .pd-chapter').length) {
                                        $('.pd-section.pd-section--deleted-video').remove();
                                    }
                                }
                                let listItemChapter = $('.filter-item-project');
                                if (listItemChapter.length > 0) {
                                    let itemChapterRemoved = $(`.filter-item-project[data-ps-id="${response.product_scene_id}"]`);
                                    if (itemChapterRemoved.length < 2){
                                        $('.item-project-delivery').trigger('click');
                                    }else {
                                        itemChapterRemoved.prev().trigger('click');
                                    }
                                    itemChapterRemoved.remove();
                                }
                            }
                        },
                        error: function () {
                            toastr.error('エラーが発生しました', 'チャプター削除');
                        }
                    })
                }
            }
        })
    });
}

function projectChapterCreate() {
    $(document).on('click', '.new-chapter__create', function (e) {
        e.preventDefault();

        var chapter_title = $('#chapter-title').val();
        var new_chapter = '<div class="pd-chapter">' +
            '<div class="pd-chapter__title">' + chapter_title +
            '<div class="pd-chapter__line"></div>' +
            '<div class="pd-chapter__toggle"></div>' +
            '</div>' +
            '<div class="pd-chapter__content">' +
            '<div class="pd-chapter__list mscrollbar">' +
            '<a class="pd-chapter__add" href="#">' +
            '<div class="pd-chapter__add-content">' +
            '<div class="pd-chapter__add-icon"><i class="icon icon--sicon-plus"></i>' +
            '</div>' +
            '<div class="pd-chapter__add-text">シーンを追加</div>' +
            '</div>' +
            '</a>' +
            '</div>' +
            '</div>' +
            '</div>';

        $('.pd-chapter-list').append(new_chapter);
        $('#pnew-chapter').modal('hide');
        // SoremoProject.projectChapterToggle();
    });
}

function createChapter(){
    $('.button-upload-video-scene-title').off().on('click', function() {
        $('.modal-container-create').css('display', 'flex');
        $('#create-chapter').find('input.input-chapter-name').val('');
    });
}

function checkCollapseChapter(){
    let is_open = $('.pd-chapter__toggle').hasClass("active");
    if (!is_open){
        $('.pd-chapter.active').find('.pd-chapter__content').slideUp(300);
        $('.pd-chapter__title').removeClass('active');
        $('.pd-chapter').removeClass('active');
    }
}

let document_status = false;

var video_chapter;
var video_section;

function projectSwitch() {

    $(document).on('change', '#switch-chapter', function () {
        if (!last_load_product_scene) {
            $(this).prop('checked', true);
            return
        }
        video_section = $('.pd-section--all-video');
        const checked = $(this).is(':checked');

        if (!checked) {
            // Call Ajax
            video_chapter = video_section.find('.pd-section__main').clone(true);
            var response = '<div class="pd-section__content"><div class="pd-video-list">';
            video_section.find('.cvideo:not(.hide)').each(function () {
                response += '<div class="cvideo">' + $(this).html() + '</div>';
            });

            response += '</div></div>';

            video_section.find('.pd-section__main').empty();
            video_section.find('.pd-section__main').append(response);
        } else {
            video_section.find('.pd-section__main').empty();
            video_section.find('.pd-section__main').append(video_chapter.html());
            createChapter();
            checkCollapseChapter();
        }
        projectRating();
        sort_direction_check($('.tab--video-all'));
        dragDropProcessing();
    });
}

function projectFilter() {
    $(document).on('click', '.video-filter', function () {
        let parent = $(this).parents('.pd-section--all-video');
        let all_scene = parent.find('.project-delivery-item-content:not(.deleted)').parents('.cvideo');
        let done_scene = parent.find('.project-delivery-item-content:not(.deleted)[data-status=5], .project-delivery-item-content:not(.deleted)[data-status=6]').parents('.cvideo');
        if ($(this).hasClass('video-all')) {
            $(this).removeClass('video-all');
            $(this).addClass('video-heart');
            $(this).html('<i class="icon icon--sicon-heart" title="検収済み"></i>検収済み');
            all_scene.addClass('hide');
            done_scene.removeClass('hide');
            // Do something - ajax
        } else if ($(this).hasClass('video-heart')) {
            $(this).removeClass('video-heart');
            $(this).addClass('video-heart-o');
            $(this).html('<i class="icon icon--sicon-heart-o" title="未検収"></i>未検収');
            all_scene.removeClass('hide');
            done_scene.addClass('hide');
            // Do something - ajax
        } else {
            $(this).removeClass('video-heart-o');
            $(this).addClass('video-all');
            $(this).html('<i class="icon icon--sicon-heart-o" title="全て"></i>全て');
            all_scene.removeClass('hide');
            // Do something - ajax
        }
    });
}

function projectOrder() {
    $(document).on('click', '.video-order-type', function () {
        let parent = $(this).parents('.tab--video-all');
        if ($(this).hasClass('active')) {
            return;
        } else {
            $(this).addClass('active');
            $(this).siblings().removeClass('active');
            sort_direction_check(parent)
        }
    });
}


function projectScene() {
    $('.cscene-variation .sselect-wrapper').each(function () {
        var search = $(this).find('select').attr('data-search');
        // console.log(search);
        var searchText = $(this).find('select').attr('data-search-text');

        $(this).find('select').SumoSelect({
            search: (search == 'true') ? true : false,
            searchText: searchText,
            showTitle: false
        });

        $('.cscene-variation p').attr('title', '');
    });

    $('.cscene-horizontal').each(function (key, item) {
        var sliderHorizontalIdName = 'sliderHorizontal' + key;
        var sliderHorizontalNavIdName = 'sliderHorizontalNav' + key;

        this.id = sliderHorizontalIdName;
        $('.cscene-horizontal-dots')[key].id = sliderHorizontalNavIdName;

        var number_horizontal_dots = $($('.cscene-horizontal-dots')[key]).find('.cscene__variation-dot').length;
        // console.log(number_horizontal_dots);
        $($('.cscene-horizontal-dots')[key]).addClass('horizontal-' + number_horizontal_dots + '-dots');
        var slidesToShow = 5;
        if (number_horizontal_dots === 1) {
            slidesToShow = 1
        } else if (number_horizontal_dots === 2) {
            slidesToShow = 2
        } else if (number_horizontal_dots === 3) {
            slidesToShow = 5
        } else if (number_horizontal_dots === 4) {
            slidesToShow = 7
        } else if (number_horizontal_dots === 5) {
            slidesToShow = 9
        }

        var sliderHorizontalId = '#' + sliderHorizontalIdName;
        var sliderHorizontalNavId = '#' + sliderHorizontalNavIdName;

        $(sliderHorizontalId).slick({
            infinite: false,
            prevArrow: false,
            nextArrow: false,
            slidesToShow: 1,
            slidesToScroll: 1,
            dots: false,
            centerMode: true,
            accessibility: false,
            // focusOnSelect: true,
            asNavFor: sliderHorizontalNavId,
            centerPadding: '0',
            speed: 0,
            swipe: false,
           useTransform: false,
        }).on('afterChange', function (event, slick, index, direction) {
           if ($(document).width() > maxWidthIpadDevice) {
                resizeScenePC();
           } else if ($(document).width() <= maxWidthDeviceSP) {
               resizeScene();
               let listToolTip = $('.variation-button-container');
               listToolTip.find('.variation-button-name-tooltip-container').css({
                   'display': 'none'
               })
               setTimeout(function () {
                   setPositionTooltipVariationTouchmove();
               }, 500)
           } else {
               resizeScenePC();
               let listToolTip = $('.variation-button-container');
               listToolTip.find('.variation-button-name-tooltip-container').css({
                   'display': 'none'
               })
               setTimeout(function () {
                   setPositionTooltipVariationTouchmove();
               }, 500)
           }
        }).on('setPosition', function (event, slick) {
            applyStyleParentSlide();
            if ($('.has-variations').length > 0) {
                if ($(document).width() < maxWidthIpadDevice) {
                    let listVariationContainer = $('.variation-button-container');
                    listVariationContainer.each(function (i, el) {
                        $(this).find('.variation-button-name-tooltip-container').css({
                            'display': 'none'
                        })
                    })
                    let variationButtonActive = $('.variation-button-container.active')
                    calculatePositionTooltipVariation(variationButtonActive, variationButtonActive.find('.variation-button-name-tooltip-container')[0]);
                    variationButtonActive.find('.variation-button-name-tooltip-container').css({
                        'display': 'flex',
                        'transform': 'translate(-50%, -100%)'
                    })
                    let listVariation = $('.list-variation');
                    checkPositionTooltipVariation(listVariationContainer, listVariation);
                }
            }
        });

        $(sliderHorizontalNavId).on('afterChange init reinit', function (event, slick, index, direction) {
            slick.$slides.removeClass('slide-prev').removeClass('slide-next');

            var currentSlide = slick.slickCurrentSlide();
            $(this).closest('.cscene').find('select.select')[0].sumo.selectItem(currentSlide);
            // console.log($(this).closest('.cscene').find('select.select').val());

            $(this).closest('.cscene').find('select.select').on('change', function () {
                slick.slickGoTo($(this).val());
                return;
            });

            for (var i = 0; i < slick.$slides.length; i++) {
                var $slide = $(slick.$slides[i]);
                if ($slide.hasClass('slick-current')) {
                    // update DOM siblings
                    $slide.prev().addClass('slide-prev');
                    $slide.next().addClass('slide-next');
                    break;
                }
            }

            let version_dom = $(this).closest('.cscene').find('.cscene-horizontal .cscene__variation.slick-current .cscene-vertical-dots .cscene__version-dot.slick-current');
            if (version_dom.length < 1) {
                if ($(this).closest('.cscene').find('.cscene-horizontal .cscene__variation.slick-current .cscene-vertical-dots .cscene__version-dot.slide-next').prev('.slick-slide').length > 0) {
                    $(this).closest('.cscene').find('.cscene-horizontal .cscene__variation.slick-current .cscene-vertical-dots .cscene__version-dot.slide-next').prev('.slick-slide').trigger('click');
                } else {
                    $(this).closest('.cscene').find('.cscene-horizontal .cscene__variation.slick-current .cscene-vertical-dots .cscene__version-dot:first-child').trigger('click');
                }
            }
        }).on('beforeChange', function (event, slick, direction) {
            slick.$slides.removeClass('slide-prev').removeClass('slide-next');
        }).slick({
            infinite: false,
            prevArrow: false,
            nextArrow: false,
            slidesToShow: slidesToShow,
            slidesToScroll: 1,
            centerMode: true,
            accessibility: false,
            arrows: false,
            focusOnSelect: true,
            asNavFor: sliderHorizontalId,
            centerPadding: '20%', // 34px
            speed: 0,
            swipe: false,
            useTransform: false,
        }).on('afterChange', function (event, slick, index, direction) {
            // console.log('aa')
             if ($(document).width() > maxWidthDeviceSP) {
                 resizeScenePC();
            } else {
                resizeScene();
            }
        }).on('setPosition', function () {
              applyStyleParentSlide();
        })

        setTimeout(function () {
            // $(sliderHorizontalId).slick('refresh');
            // $(sliderHorizontalNavId).slick('refresh');
            // setTimeout(function () {
            //     $('#sliderHorizontalNav0').slick('slickGoTo', 0);
            // }, 10);
        }, 200);

        // $(window).on('resize', function () {
        //     setTimeout(function() {
        //         if (is_pc === "True" && !checkVideoFullScreen()) {
        //             setTimeout(function () {
                        // @TODO: check only in parent element
                        // $(sliderHorizontalId).slick('refresh');
                        // $(sliderHorizontalNavId).slick('refresh');
                        // setTimeout(function () {
                        //     $('#sliderHorizontalNav0').slick('slickGoTo', 0);
                        // }, 10)
        //             }, 200);
        //         }
        //     }, 300)
        // });
    });

    $('.cscene-vertical').each(function (key, item) {
        var sliderVerticalIdName = 'sliderVertical' + key;
        var sliderVerticalNavIdName = 'sliderVerticalNav' + key;

        this.id = sliderVerticalIdName;
        $('.cscene-vertical-dots')[key].id = sliderVerticalNavIdName;

        var sliderVerticalId = '#' + sliderVerticalIdName;
        var sliderVerticalNavId = '#' + sliderVerticalNavIdName;

        $(sliderVerticalId).slick({
            infinite: false,
            slidesToShow: 1,
            slidesToScroll: 1,
            prevArrow: false,
            nextArrow: false,
            dots: false,
            vertical: true,
            verticalSwiping: true,
            accessibility: false,
            // swipeToSlide: true,
            focusOnSelect: true,
            asNavFor: sliderVerticalNavId,
            speed: 0,
            swipe: false,
            useTransform: false,
        }).on('afterChange', function (event, slick, direction) {
            // console.log($(this).find('.cscene__version.slick-current').attr('data-date'));
        }).on('setPosition', function () {
            if ($(document).width() < maxWidthIpadDevice) {
                // resizeScenePC();
                let listToolTip = $('.variation-button-container');
                listToolTip.find('.variation-button-name-tooltip-container').css({
                    'display': 'none'
                })
                setTimeout(function () {
                    setPositionTooltipVariationTouchmove();
                }, 500)
            }
        })

        var max_version = $(sliderVerticalNavId).find('.cscene__version-dot').length;
        // Fix vertical dots hidden by add padding or use CSS
        var scene_padding = max_version * 12;
        $(this).closest('.cscene').css('padding-bottom', scene_padding + 'px');
        // console.log(max_version);

        $(sliderVerticalNavId).on('afterChange init', function (event, slick, direction) {
            slick.$slides.removeClass('slide-prev').removeClass('slide-next');

            var currentSlide = slick.slickCurrentSlide();
            let version_dom = $(this).find('.cscene__version-dot.slick-current');

            let version_date = version_dom.attr('data-date');
            $('.tfile-infor').removeClass('active');
            let current_scene = version_dom.attr('data-scene_id');
            $('.pd-scene-title-detail').find('.pd-section-file').find('.tfile-infor[data-scene-id^=' + current_scene + ']').addClass('active');
            // if (version_date !== undefined) {
            //     $(this).closest('.cscene').find('.cscene-meta__date').text(version_date);
            //     let variation_id = $(this).find('.cscene__version-dot').last().attr('data-scene_id');
            //     let url_string = window.location.pathname;
            //     let refresh = url_string.split('scene')[0] + 'scene/' + variation_id;
            //     window.history.pushState({path: refresh}, '', refresh);
            //     go_back++;
            // }

            for (var i = 0; i < slick.$slides.length; i++) {
                var $slide = $(slick.$slides[i]);
                if ($slide.hasClass('slick-current')) {
                    // update DOM siblings
                    $slide.prev().addClass('slide-prev');
                    $slide.next().addClass('slide-next');
                    break;
                }
            }
        }).on('beforeChange', function (event, slick, direction) {
            slick.$slides.removeClass('slide-prev').removeClass('slide-next');
            $('.cscene-vertical-dots').removeClass('hide')
        }).slick({
            infinite: false,
            slidesToShow: max_version, // 4
            slidesToScroll: max_version,
            centerMode: true,
            accessibility: false,
            arrows: false,
            focusOnSelect: true,
            vertical: true,
            verticalSwiping: true,
            // swipeToSlide: true,
            asNavFor: sliderVerticalId,
            speed: 0,
            swipe: false,
            useTransform: false,
        }).on('afterChange', function (event, slick, index, direction) {
            // console.log('bb');
            if ($(document).width() > maxWidthDeviceSP) {
                resizeScenePC();
            } else {
                resizeScene();
            }
        });

        // setTimeout(function () {
            // $(sliderVerticalId).slick('refresh');
            // $(sliderVerticalNavId).slick('refresh');
            // setTimeout(function () {
            //     $('#sliderHorizontalNav0').slick('slickGoTo', 0);
            // }, 10)

        // }, 200);

        // $(window).on('resize', function () {
        //     setTimeout(function() {
        //         console.log('pc', checkVideoFullScreen())
        //         if (is_pc === 'True' && !checkVideoFullScreen()) {
        //             setTimeout(function () {
        //                 // $(sliderVerticalId).slick('refresh');
        //                 // $(sliderVerticalNavId).slick('refresh');
        //                 // setTimeout(function () {
        //                 //     $('#sliderHorizontalNav0').slick('slickGoTo', 0);
        //                 // }, 10)
        //             }, 200);
        //         }
        //     }, 300)
        // });

        $('.cscene__variation-dot').off('click').on('click', function () {
            let new_index = $(this).attr('data-slick-index');
            $('#sliderHorizontalNav0').slick('slickGoTo', new_index);
            $('.cscene-variation .sselect-wrapper select')[0].sumo.selectItem(new_index);
            stop_video_audio();
            
            // Initialize HLS videos after slick slide change
            if (typeof window.initializeAllHLSVideos === 'function') {
                setTimeout(function() {
                    window.initializeAllHLSVideos();
                }, 100);
            }
        })

        $(item).off('mousedown').on('mousedown', function() {
            $('.cscene-vertical-dots').addClass('hide')
            $(this).off('mouseup').on('mouseup', function (e) {
                $('.cscene-vertical-dots').removeClass('hide');
            });
        })
    });

    $('.cscene__version-dot').off('click').on('click', function () {
        stop_video_audio();
    });
}


function checkVideoFullScreen() {
    return document.webkitCurrentFullScreenElement !== null;
}

function reloadLinkAudio(){
    let list_audio = $(".pd-scene").find(".s-audio");
    list_audio.each((index, audio) => {
        let wave_index = $(audio).attr("data-wavesurfer");
        let audio_source = $(audio).find(".s-audio-source");
        let wave_scene =  wavesurfer_arr[parseInt(wave_index)];
        let link = audio_source.attr("data-link");
        let peaks_string = audio_source.attr("data-peaks-loaded");
        if (!peaks_string){
            return;
        }
        if($(audio).parents(".slick-active").length){
            if ($('.cscene__version-horizontal').hasClass('active')) {
                let small_peaks = audio_source.attr("data-small-peaks");
                let array_small_peaks = small_peaks.split(" ").map(Number);
                wave_scene.load(link, array_small_peaks, 'none');
                wave_scene.loaded = false;
            } else {
                let array_peaks = peaks_string.split(" ").map(Number);
                wave_scene.load(link, array_peaks, 'none');
                wave_scene.loaded = false;
            }
        }
    });
}

function projectFileToggle() {
    $(document).on('click', '.pd-file-heading', function () {
        var closest_section_file = $(this).closest('.pd-section-file');
        if ($(window).width() > max_width_resize_sp) {
            $('.wallet-pl').css('pointer-events', 'none');
            $(this).parents('.prdt').find('.pd-section-file').toggleClass('active');
            $(this).parents('.prdt').find('.pd-section-file').find('.pd-file-toggle').toggleClass('active');
            $('.pd-section-file.file-hide').addClass('hide-file-tab')
            $(this).parents('.prdt').find('.pd-section-file').find('.pd-file-content').toggleClass('active');
            $(this).toggleClass('active');
            $(this).parents('.prdt').find('.pd-section.pd-section--detail').find('.pd-section__content').toggleClass('active');
            $('.cscene__version-horizontal').toggleClass('active');
            $(window).trigger('resize');
            setTimeout(function () {
                $('.pd-section-file.file-hide').removeClass('hide-file-tab')
                $('.wallet-pl').css('pointer-events', 'unset');
            }, 400)
            setTimeout( ()=>{
                reloadLinkAudio();
            }, 500)
        } else {
           closest_section_file.find('.pd-file-content').slideToggle(300);
        }
        if (!$(this).hasClass('active') && closest_section_file.find('.pd-file-content').length < 1) {
            let data = {};
            let scene_title_id;
            let project_id;
            let parent_dom;
            if ($(this).parents('.pd-scene-title-detail').length > 0) {
                parent_dom = $(this).parents('.pd-scene-title-detail');
                scene_title_id = $(this).parents('.pd-scene-title-detail').attr('data-scene-title-id');
                data = {
                    'scene_title_id': scene_title_id,
                    'type_comment': 'scene_comment'
                }
            } else if ($(this).parents('.pd-product-comment').length > 0) {
                parent_dom = $(this).parents('.pd-product-comment');
                project_id = $(this).parents('.project-item').attr('data-project-id');
                data = {
                    'project_id': project_id,
                    'type_comment': 'project'
                }
            }
            if (scene_title_id || project_id) {
                $.ajax({
                    type: "GET",
                    url: "/top/get_all_file_scene_title",
                    async: false,
                    data: data,
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        $(response.html).appendTo(parent_dom.find('.pd-section-file'));
                        sScrollbarBottom();
                        removeDuplicatedDate();
                    },
                    complete: function () {
                        calcMoreActionComment($('.prdt .mmessage'));
                        calcPositionDropdownComment2();
                        hoverDropdownMessage()
                    }
                });
            }
        }
        hoverBtnActionMessage()
        clickBtnActionMessage()
        resizeCommentInput2();
    });

    $(document).on('click', '.pd-comment__heading', function () {
        $(this).toggleClass('active');
        $(this).closest('.pd-comment').find('.pd-comment__main').slideToggle(300);
    });

    $(document).on('click', '.pd-file', function () {
        $(this).closest('.pd-file').toggleClass('active');
        $(this).closest('.pd-file').find('.pd-file-content').toggleClass('active');
        $(this).closest('.pd-section-file').find('.pd-file-content').toggleClass('active');
        $(this).closest('.pd-comment').toggleClass('active');

        if(!$(this).closest('.pd-file').hasClass('active')) {
            $('.pd-file').toggleClass('active');
            $('.pd-file').find('.pd-file-toggle').toggleClass('active');
            $('.pd-file').find('.pd-file-content').toggleClass('active');
            $('.pd-section-file').find('.pd-file-toggle').toggleClass('active');
            $(this).toggleClass('active');
            $(".pd-comment").width(0).height(0);
            $(".pd-comment").hide(300);
            $('.pd-section-file').addClass('pd-section-file-active');
            $('.pd-section-file').removeClass('file-hide');
        }
    });

    $(document).on('click', '.pd-section-file-active', function () {
        $('.pd-file').toggleClass('active');
        $(".pd-comment").show();
        $(".pd-comment").width('100%').height('80vh');
        $('.pd-section-file').removeClass('pd-section-file-active');
        $('.pd-section-file').addClass('file-hide');
    });
}

function resizeCommentInput2() {
    if ($(window).width() > max_width_sp_device) {
        setTimeout(function () {
            let main_block = $('.pd-section__content.main-talk-room')
            if (main_block.length < 1) {
                main_block = $('.offer-content-message')
            }
            if (main_block.length > 0) {
                let width_main_block = main_block.width();
                let left_main_block = main_block.offset().left;
                let footer_comment_block = $('.footer-comment-block');
                let width_footer_comment_block = footer_comment_block.width();
                let fixedBlockLeft = left_main_block + (width_main_block - width_footer_comment_block) / 2;
                footer_comment_block.css({
                    'width': width_main_block + 'px',
                    'left': fixedBlockLeft + 'px'
                });
            }
        }, 350)
    }
}

function resolveComment(target) {
    target.find('.video-comment-resolve').off().on('click', function () {
        let target = $(this).parents('.video-comment-item-reply');
        let comment_id = target.attr('data-cmt-id');

        //hide/show
        while (target.is('.sub-item')) {
            target = target.prev();
        }

        let resolved = target.is('.resolved');
        if (target.is('.resolved')) {
            show_unresolved_comment(target);
        } else {
            hide_resolved_comment(target);
        }
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/top/resolve_comment",
            data: {
                'comment_id': comment_id,
                'resolved': resolved,
                'type': 'project'
            }, fail: data => {
                let text = target.is('.resolved') ? '再開' : '解決';
                toastr.error('エラーが発生しました', 'コメント' + text);
                if (resolved) {
                    hide_resolved_comment(target);
                } else {
                    show_unresolved_comment(target, true);
                }
            }
        })
    });
}

function projectRating() {
    $('.stars:not(selected)').each(function () {
        var star = $(this);
        let rating = Math.round(star.attr('data-rating'));
        $(this).find('span[data-value^=' + rating + ']').addClass('active');
        if ($(this).find('span.active').length > 0) {
            star.addClass('selected');
        }

        $(this).find('span').off('click').on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            if (!star.hasClass('cannot-check')) {
                let star_dom = $(this);
                star.addClass('selected');
                let current_value = star.find('span.active').attr('data-value');
                $(this).addClass('active');
                $(this).siblings('span').removeClass('active');
                let new_value = star.find('span.active').attr('data-value');

                if (new_value !== current_value) {
                    let parent = $(this).parents('.project-delivery-item-content');
                    if (parent.length < 1) {
                        parent = $(this).parents('.pd-scene-title-detail');
                    }
                    let scene_title_id = parent.attr('data-scene-title-id');
                    if (scene_title_id) {
                        $.ajax({
                            type: "POST",
                            datatype: "json",
                            url: "/scene/rating_scene",
                            data: {
                                'scene_title_id': scene_title_id,
                                'value': new_value
                            },
                            success: function (data) {
                                if (data.status === 'success') {
                                    let scene_title_dom = $('.project-delivery-item-content[data-scene-title-id^=' + scene_title_id + ']');
                                    if (scene_title_dom.length < 1) {
                                        scene_title_dom = parent
                                    }
                                    scene_title_dom.attr('data-rating', data.rating);
                                    $(".stars.can-rate").attr("data-rating", new_value);

                                    let new_rating = Math.round(data.rating);
                                    scene_title_dom.find('.stars').addClass('selected');
                                    scene_title_dom.find('.stars').find('span[data-value^=' + new_rating + ']').addClass('active');
                                    scene_title_dom.find('.stars').find('span[data-value^=' + new_rating + ']').siblings('a').removeClass('active');
                                }
                            },
                            error: function () {
                                toastr.error('エラーが発生しました', '好き');
                                if (!current_value) {
                                    star.removeClass('selected');
                                    star_dom.parents('.stars').find('a').removeClass('active');
                                } else {
                                    star_dom.parents('.stars').find('a[data-value^=' + current_value + ']').addClass('active');
                                    star_dom.parents('.stars').find('a[data-value^=' + current_value + ']').siblings('a').removeClass('active');
                                }
                            }
                        })
                    }
                }
            }
        });

    });
}

function projectTree() {
    $(document).on('click', '.tversion-button', function (e) {
        e.preventDefault();

        var close_version_list = $(this).closest('.tversion-list');
        var close_version_input = $(this).closest('.tversion-add').find('.tversion-input');

        close_version_input.trigger('click');
        close_version_input.on('change', function () {
            // console.log($(this).val());

            var new_version = '<li class="tversion-item">' +
                '<div class="tversion-content"><span>' + '2020/05/09' + '</span>' +
                '<div class="tversion-action">' +
                '<input class="tversion-input" type="file"></input>' +
                '<a class="tversion-edit tbutton" href="#"><i class="icon icon--sicon-pencil"></i></a>' +
                '<a class="tversion-delete tbutton" href="#"><i class="icon icon--sicon-trash"></i></a></div>' +
                '</div>' +
                '</li>';
            close_version_list.find('.tversion-add').after(new_version);
        });
    });

    // Edit version reselect video
    $(document).on('click', '.tversion-edit', function (e) {
        e.preventDefault();

        var close_version_list = $(this).closest('.tversion-list');
        var close_version_input = $(this).closest('.tversion-action').find('.tversion-input');

        close_version_input.trigger('click');
        close_version_input.on('change', function () {
            // console.log($(this).val());
        });
    });

    // Delete version video
    $(document).on('click', '.tversion-delete', function (e) {
        e.preventDefault();

        $(this).parents('.tversion-item').remove();

        // Update something
    });

    // Edit variation
    $(document).on('click', '.tvariation-edit', function (e) {
        e.preventDefault();

        var close_variation_item = $(this).closest('.tvariation-item');
        var close_variation_name = close_variation_item.find('.tvariation-name');

        var old_val = close_variation_name.text();
        close_variation_name.html('<input type="text" class="tvariation-name-new" value="' + old_val + '">');

        // Focus to last character
        var variationInput = $('.tvariation-name-new');
        variationInput.val(variationInput.val());
        var strLength = variationInput.val().length;
        variationInput[0].setSelectionRange(strLength, strLength);
        variationInput.focus();

        // setTimeout(function(){
        variationInput.on('focus', function () {
            // Do something
            close_variation_name.addClass('editing');
            console.log('focus');
        }).blur(function () {
            console.log('blur');
            var new_val = $('.tvariation-name-new').val();
            close_variation_name.text(new_val);

            $('.video-modal-chapter').text(new_val);
            close_variation_name.removeClass('editing');
        });
        // }, 3000);

        // Process click on tree doesn't blur
        // $('.video-modal__tree').on('click', function() {
        //   $('.tvariation-name-new').trigger('blur');
        // });

        // close_variation_input.trigger('click');
        // close_variation_input.on('change', function() {
        //   // console.log($(this).val());
        // });
    });

    // Delete variation
    $(document).on('click', '.tvariation-delete', function (e) {
        e.preventDefault();

        $(this).parents('.tvariation-item').remove();

        // Update something
    });

    // Edit scene
    $(document).on('click', '.tscene-edit', function (e) {
        e.preventDefault();

        var close_scene_item = $(this).closest('.tscene-item');
        var close_scene_name = close_scene_item.find('.tscene-name');

        var old_val = close_scene_name.text();
        close_scene_name.html('<input type="text" class="tscene-name-new" value="' + old_val + '">');

        // Focus to last character
        var sceneInput = $('.tscene-name-new');
        sceneInput.val(sceneInput.val());
        var strLength = sceneInput.val().length;
        sceneInput[0].setSelectionRange(strLength, strLength);
        sceneInput.focus();

        $('.tscene-name-new').focus(function () {
            // Do something
        }).blur(function () {
            var new_val = $('.tscene-name-new').val();
            close_scene_name.text(new_val);

            $('.video-modal-chapter').text(new_val);
        });
    });

    // Delete scene
    $(document).on('click', '.tscene-delete', function (e) {
        e.preventDefault();

        $(this).parents('.tscene-item').remove();

        // Update something
    });

    // Edit chapter
    $(document).on('click', '.tchapter-edit', function (e) {
        e.preventDefault();

        var close_chapter_item = $(this).closest('.tchapter-item');
        var close_chapter_name = close_chapter_item.find('.tchapter-name');

        var old_val = close_chapter_name.text();
        close_chapter_name.html('<input type="text" class="tchapter-name-new" value="' + old_val + '">');

        // Focus to last character
        var chapterInput = $('.tchapter-name-new');
        chapterInput.val(chapterInput.val());
        var strLength = chapterInput.val().length;
        chapterInput[0].setSelectionRange(strLength, strLength);
        chapterInput.focus();

        $('.tchapter-name-new').focus(function () {
            // Do something
        }).blur(function () {
            var new_val = $('.tchapter-name-new').val();
            close_chapter_name.text(new_val);

            $('.video-modal-chapter').text(new_val);
        });
    });

    // Delete chapter
    $(document).on('click', '.tchapter-delete', function (e) {
        e.preventDefault();

        $(this).parents('.tchapter-item').remove();

        // Update something
    });
}


function goToSceneActive(scene_id) {
    let version = $('.cscene__version.slick-slide[data-scene-id^=' + scene_id + ']');
    let variation = version.parents('.cscene__variation');
    let version_index = version.attr('data-index');
    let variation_index = variation.attr('data-index');
    let current_variation_index = $('.cscene__variation-dot.slick-current').attr('data-index');
    let current_version_index = $('.cscene__version-dot.slick-current').attr('data-index');
    if (current_variation_index !== variation_index || current_version_index !== version_index) {
        $('#sliderHorizontalNav0').slick('slickGoTo', variation_index);
        //$('#sliderHorizontal0').slick('slickGoTo', version_index);
        variation.find('.cscene__version-dot[data-index^=' + version_index + ']').trigger('click');
        
        // Initialize HLS videos after slick slide change
        if (typeof window.initializeAllHLSVideos === 'function') {
            setTimeout(function() {
                window.initializeAllHLSVideos();
            }, 100);
        }
    }
}

function projectToggleResolved() {
    $('.pd-scene-title-detail, .pd-product-comment').on('change', '#switch-checkbox-comment, #order-monthy', function () {
        const checked = $(this).is(':checked');
        actionShowIconResolved()
        calcMoreActionComment($('.main-talk-room .mmessage.resolved.clicked'));
        calcMoreActionComment($('.scene-style .mmessage'));
    })
}

function actionShowIconResolved() {
    let toggleDom = $('.pd-product-comment #order-monthy');
    if (!toggleDom.length) {
        toggleDom = $('.pd-scene-title-detail #switch-checkbox-comment');
    }
    let checked = toggleDom.is(':checked');

    let received_comments = $(".mmessage--received");
    let received_status = received_comments.find('.mmessage-status');
    let received_action = received_comments.find('.mmessage-action');
    if (checked) {
        $('.pd-scene-title-detail').removeClass('show-comment-unresolved').addClass('show-comment-all');
        if (!toggleDom.parents('.pd-section__content').find('.mmessage-list').hasClass('view_only')) {
            received_comments.each(function () {
                let message_active = $(this);
                if (message_active.height() <= 75) {
                    message_active.find('.mmessage-status').addClass('hide');
                } else {
                    message_active.find('.mmessage-info').css({height: message_active.find('.mmessage-main').height()})
                }
            });
            received_action.removeClass('hide');
        }
    } else {
        $('.mmessage.resolved .s-audio-control.active').each(function () {
            $(this).trigger('click')
        });
        received_status.removeClass('hide');
        received_action.addClass('hide');
        $('.pd-scene-title-detail').removeClass('show-comment-all').addClass('show-comment-unresolved');
    }
}


function seenComment(type) {
    let scene_title_id;
    let product_id;
    let data;
    if ($('.mmessage-list').hasClass('not-seen')) {
        if (type === 'scene') {
            scene_title_id = $('.pd-scene-title-detail').attr('data-scene-title-id');
            data = {
                'scene_title_id': scene_title_id,
                'comment_type': 'scene'
            }
        } else {
            product_id = $('.project-item.active').attr('data-project-id');
            data = {
                'product_id': product_id,
                'comment_type': 'project'
            }

        }
        if (scene_title_id || product_id) {
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/top/seen_comment",
                data: data,
                success: function () {
                    console.log('update seen successful');
                    $('.mmessage-list').removeClass('not-seen');
                }
            })
        }
    }
}

function newWavesurferInit() {
    let current_length = wavesurfer_arr.length;
    let list_audio = $('.s-audio-waveform');
    if (list_audio.length > 0) {
        if (!$('.pd-section-file').hasClass('active')) {
            $('.cscene__version-horizontal').addClass('active')
        }
        let step = 0;
        for (let i = 0; i < list_audio.length ; i++) {
            const this_wave = $(list_audio[i]);
            if (this_wave.find('wave').length < 1) {
                const $mplayer = this_wave.closest('.s-audio');
                if ($mplayer.length > 0 && $mplayer.find('.s-audio-source').length > 0) {
                    const link = $mplayer.find('.s-audio-source').attr('data-link');
                    const waveColor = $mplayer.find('.s-audio-source').attr('data-waveColor');
                    const progressColor = $mplayer.find('.s-audio-source').attr('data-progressColor');
                    const peaks_loaded = $mplayer.find('.s-audio-source').attr('data-peaks-loaded');
                    let file_id = $mplayer.parents('.mmessage').find('.mmessenger--audio-wave').attr('data-file-id');
                    if (!file_id) {
                        file_id = $mplayer.parents('.minfo-file_info').attr('data-file-id');
                    }
                    const wavesurfer = WaveSurfer.create({
                        container: list_audio[i],
                        waveColor: "#a7a8a9",
                        progressColor: '#009ace',
                        cursorColor: "#009ace",
                        barWidth: 2,
                        barRadius: 3,
                        cursorWidth: 1,
                        barGap: 1,
                        barHeight: 0.6,
                        pixelRatio: 1,
                        mediaControls: false,
                        height: 64,
                        backend: 'MediaElement',
                        fillParent: true,
                        hideScrollbar: true,
                        splitChannels: true,
                        plugins: [
                            WaveSurfer.cursor.create({
                                showTime: true,
                                opacity: 1,
                                customShowTimeStyle: {
                                    'background-color': '#000',
                                    color: '#fff',
                                    padding: '2px',
                                    'font-size': '10px'
                                },
                                formatTimeCallback: formatTimeCallback,
                            })
                        ]
                    });

                    wavesurfer_arr[current_length + i] = wavesurfer;

                    let cmt_container = this_wave.parents(".s-audio");
                    cmt_container.attr('data-wavesurfer', current_length + i);
                    let array_peaks = [];

                    if ($mplayer.hasClass('s-audio--white')){
                        let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                        if (peaks_loaded && small_peaks.length > 1) {
                            if (wavesurfer.backend?.buffer?.numberOfChannels < 2){
                                array_peaks = peaks_loaded.split(" ").map(Number);
                                wavesurfer.load(link, array_peaks, 'none');
                                wavesurfer.loaded = false;
                            }else{
                                wavesurfer.load(link);
                                updatePeaksWave(wavesurfer);    
                            }
                        } else {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer);
                        }
                    }else{
                        if (peaks_loaded) {
                            if (wavesurfer.backend?.buffer?.numberOfChannels < 2){
                                array_peaks = peaks_loaded.split(" ").map(Number);
                                wavesurfer.load(link, array_peaks, 'none');
                                wavesurfer.loaded = false;
                            }else{
                                wavesurfer.load(link);
                                updatePeaksWave(wavesurfer);    
                            }
                        } else {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer);
                        }
                    }

                    // Play on audio load
                    //need this because i have moved control block to outside pj262-518
                    if ($mplayer.find('.s-audio-control:not(.video-pin-time)').length > 0){
                        $mplayer.find('.s-audio-control:not(.video-pin-time)').off().on('click', function (e) {
                            const peaks_loaded = $mplayer.find('.s-audio-source').attr('data-peaks-loaded');
                            const array_peaks = peaks_loaded.split(" ").map(Number);
                            if (!array_peaks.length) {
                                return;
                            }
                            if (!$(this).hasClass('video-pin-time')) {
                                if (!wavesurfer.loaded) {
                                    if ($mplayer.hasClass('s-audio--white') && $('.cscene__version-horizontal').hasClass('active')){
                                        let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                                        if (small_peaks.length > 1){
                                            let array_small_peaks = small_peaks.split(" ").map(Number);
                                            wavesurfer.load(link, array_small_peaks);
                                        }
                                    }else{
                                        wavesurfer.load(link, array_peaks);
                                    }
                                }
                                let playing = false;
                                if(wavesurfer.isPlaying()) {
                                    playing = true;
                                }
                                stop_video_audio();
                                if(!playing) {
                                    wavesurfer.playPause();
                                }
                            }
                        });
                    }else{
                        $mplayer.parent().find('.s-audio-control:not(.video-pin-time)').off().on('click', function (e) {
                            const peaks_loaded = $mplayer.find('.s-audio-source').attr('data-peaks-loaded');
                            const array_peaks = peaks_loaded.split(" ").map(Number);
                            if (!array_peaks.length) {
                                return;
                            }
                            if (!$(this).hasClass('video-pin-time')) {
                                if (!wavesurfer.loaded) {
                                    if ($mplayer.hasClass('s-audio--white') && $('.cscene__version-horizontal').hasClass('active')){
                                        let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                                        if (small_peaks.length > 1){
                                            let array_small_peaks = small_peaks.split(" ").map(Number);
                                            wavesurfer.load(link, array_small_peaks);
                                        }
                                    }else{
                                        wavesurfer.load(link, array_peaks);
                                    }
                                }
                                let playing = false;
                                if(wavesurfer.isPlaying()) {
                                    playing = true;
                                }
                                stop_video_audio();
                                if(!playing) {
                                    wavesurfer.playPause();
                                }
                            }
                        });
                    }


                    wavesurfer.on('waveform-ready', function () {
                        if (!wavesurfer.loaded) {
                            wavesurfer.loaded = true;
                        }
                        var duration = wavesurfer.getDuration();
                        $mplayer.find('.s-audio-time').text('00:00');
                        $mplayer.find('.s-audio-time-total').text('00:00');
                        $mplayer.find('.s-audio-time-total').text(convertSecondsToTime(duration));
                        if (wavesurfer.backend?.buffer?.numberOfChannels < 2){
                            $mplayer.find('.s-audio-time').addClass('is-single');
                            $mplayer.find('.s-audio-time-total').addClass('is-single');
                        }
                    });

                    // Display player time
                    wavesurfer.on('audioprocess', function () {
                        if (wavesurfer.getCurrentTime() > 0) {
                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));
                        } else {
                            $mplayer.find('.s-audio-time').text('00:00');
                        }
                    });

                    // Check Playpause button
                    wavesurfer.on('pause', function () {
                        $mplayer.removeClass('active');
                        if ($mplayer.find('.s-audio-control').length > 0){
                            $mplayer.find('.s-audio-control').removeClass('active');
                        }else{
                            $mplayer.parent().find('.s-audio-control').removeClass('active');
                        }

                        let activeAudio = parseInt($($mplayer[0]).attr("data-wavesurfer"));
                        if($('.mmessage-reply').hasClass('active') || $('.mmessage-edit').hasClass('active')) {
                            setColorActive(wavesurfer_arr[activeAudio]);
                        }else {
                            if ($mplayer[0]?.parentElement?.classList?.value?.includes("messenger-content-file")){
                                setColorInActive(wavesurfer_arr[activeAudio]);
                            }
                        }
                        updatePinAudio(wavesurfer);
                        //hide backward and forward audio button when pause (default hide)
                        // $mplayer.find('.backward-audio').hide();
                        // $mplayer.find('.forward-audio').hide();
                    });

                    wavesurfer.on('play', function () {
                        $mplayer.addClass('active');
                        if ($mplayer.find('.s-audio-control').length > 0){
                            $mplayer.find('.s-audio-control').addClass('active');
                        }else{
                            $mplayer.parent().find('.s-audio-control').addClass('active');
                        }

                        let activeAudio = parseInt($($mplayer[0]).attr("data-wavesurfer"));
                        if (!$mplayer.hasClass('s-audio--white')) {
                            setColorActive(wavesurfer_arr[activeAudio]);
                        }

                        if($mplayer.find('.s-audio-control.pin-time-audio').hasClass('active')) {
                            document.onkeyup = function(e) {
                                if (!$(".mcomment-input-text.mcomment-autoExpand").is(":focus")) {
                                    const keyCode = e.which;
                                    let startTime = 0;
                                    let currentTime = parseInt(wavesurfer.getCurrentTime());
                                    let durationTime = parseInt(wavesurfer.getDuration());
                                    let now = startTime;

                                    if (keyCode === 37) {
                                        now = currentTime - 5;
                                        if(now < 0) {
                                            wavesurfer.playPause();
                                            wavesurfer.setCurrentTime(0);
                                        }
                                        else {
                                            wavesurfer.setCurrentTime(now);
                                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));

                                        }
                                    }
                                    else if (keyCode === 39) {
                                        now = currentTime + 5;
                                        if (now > durationTime) {
                                            wavesurfer.playPause();
                                            wavesurfer.setCurrentTime(0);
                                        }
                                        else {
                                            wavesurfer.setCurrentTime(now);
                                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));
                                        }
                                    }
                                    else if (keyCode === 32) {
                                        wavesurfer.playPause();
                                    }
                                }
                            }
                        }

                        //show backward and forward audio button when play (default hide)
                        // $mplayer.find('.backward-audio').show();
                        // $mplayer.find('.forward-audio').show();
                    });

                    wavesurfer.on('seek', function () {
                        if (!wavesurfer.loaded) {
                            wavesurfer.load(link, array_peaks);
                        }
                        updatePinAudio(wavesurfer)
                    });

                    hoverWavesurfer();

                    // backward audio 5s
                    $mplayer.parent().find('.backward-audio').on('click', function (e){
                        wavesurfer.skip(-5)
                    });

                    // forward audio 5s
                    $mplayer.parent().find('.forward-audio').on('click', function (e){
                        wavesurfer.skip(5)
                    });
                }
            }
        }
    }
}

function updatePinAudio(wavesurfer) {
    let index_wave = wavesurfer_arr.indexOf(wavesurfer);
    let parent_wave = $('.s-audio[data-wavesurfer^=' + index_wave + ']');
    if (parent_wave.hasClass('s-audio--white')) {
        let pins = $('.pd-comment__main .mmessage-component .mcomment-pin');
        pins.each(function (i, e) {
            if ($(e).is('.active')) {
                $(e).find('.pin-icon-time').html(msToTime(wavesurfer.getCurrentTime()));
                $('.mcomment-input-title').html('<i class="icon icon--sicon-pin"></i>' + '<span>' + msToTime(wavesurfer.getCurrentTime()) + '</span>');
            }
        })
    }
}


function updatePeaksWave(wavesurfer) {
    wavesurfer.on('waveform-ready', function () {
        let peaks = wavesurfer.backend.mergedPeaks;
        let peaks_string = "";
        if (peaks){
            peaks_string = peaks.join(" ");
        }
        let values = {};
        let wave_index = wavesurfer_arr.indexOf(wavesurfer).toString();
        let dom_container = $('.s-audio[data-wavesurfer^=' + wave_index + ']');
        if (dom_container.length > 0) {
            if (dom_container.hasClass('s-audio--white')) {
                let scene_id = dom_container.attr("data-scene-id");
                let small_peaks = wavesurfer.backend.getPeaks(64);
                small_peaks = small_peaks.join(" ");
                let wave_element = $(wavesurfer.container).parent().find(".s-audio-source");
                wave_element.attr("data-peaks-loaded", peaks_string);
                wave_element.attr("data-small-peaks", small_peaks);
                values = {
                    "scene_id": scene_id,
                    "peaks": peaks_string,
                    "small_peaks": small_peaks,
                    'type': 'variation'
                }
            } else {
                let cmt_id = dom_container.parents('.mmessenger--audio-wave').attr("data-file-id");
                if (dom_container.parents('.pd-product-comment').length) {
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'project'
                    };
                } else  if (dom_container.parents('.pd-scene-title-detail').length){
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'scene'
                    };
                } else if (user_role === 'admin') {
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'messenger_owner'
                    };
                }
            }
            $.ajax({
                type: "POST",
                url: "/top/update_comment",
                data: values,
                dataType: 'json',
                success: function (data) {
                    console.log("success");
                    let message_dom;
                    if (data.file_id) {
                        message_dom = $('.mmessenger--audio-wave[data-file-id^=' + data.file_id + ']');
                    } else {
                        message_dom = $('.s-audio--white[data-scene-id^=' + data.scene_id + ']');
                    }
                    if (message_dom.length) {
                        message_dom.find('.s-audio-source').attr('data-peaks-loaded', data.peaks_loaded)
                    }
                },
                error: function (e) {
                    console.log(e);
                }
            });
        }

    });
}

function hoverWavesurfer() {
    $(document).on('mouseenter mouseleave', '.s-audio-waveform', function (e) {
        if (e.type === 'mouseenter') {
            let index_wave = parseInt($(this).parents('.s-audio--audio-wave').attr('data-wavesurfer'));
            let wavesurfer = wavesurfer_arr[index_wave];
            if (wavesurfer && !wavesurfer.loaded) {
                let link = $(this).parents('.s-audio--audio-wave').find('.s-audio-source').attr('data-link');
                let peaks_loaded = $(this).parents('.s-audio--audio-wave').find('.s-audio-source').attr('data-peaks-loaded');
                let array_peaks = [];
                let $mplayer = $(this).parents(".s-audio");
                if ($mplayer.hasClass('s-audio--white')){
                    if (!peaks_loaded) {
                        return;
                    }
                    if ($('.cscene__version-horizontal').hasClass('active')) {
                        let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                        if (small_peaks){
                            let array_small_peaks = small_peaks.split(" ").map(Number);
                            wavesurfer.load(link, array_small_peaks);
                        }
                    } else {
                        let array_peaks = peaks_loaded.split(" ").map(Number);
                        wavesurfer.load(link, array_peaks);
                    }
                }else{
                    if (peaks_loaded && !wavesurfer.loaded) {
                        array_peaks = peaks_loaded.split(" ").map(Number);
                        if (peaks_loaded.length < 1024) {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer)
                        } else {
                            wavesurfer.load(link, array_peaks);
                        }
                    } else if (!wavesurfer.loaded) {
                        wavesurfer.load(link);
                        updatePeaksWave(wavesurfer)
                    }
                }
            }
        }
    })
}

function updateModalInfo(button, isNewButton = false) {
    //get Chapter and Variation text
    let product_scene_dom = button.parents('.cscene.cscene--video');
    if (isNewButton) {
        product_scene_dom = button.parent();
    }
    let product_scene_name = product_scene_dom.find('.cscene-heading__product').first().text();
    let scene_title_name = product_scene_dom.find('.cscene-heading__title').first().text();
    let modal_chapter = $('.cscene-heading__product').text();
    let modal_scene = $('.cscene-heading__title').text();
    $("#modal-chapter").text(modal_chapter);
    $("#modal-scene").text(modal_scene);
    var chapterElementText = product_scene_name + ' | ' + scene_title_name;
    var variationElementText = $('#variation-select').parent().find(' .CaptionCont.SelectBox').attr('title');
    let slug = String(user) + $(".cscene__share").attr('share-url');

    let slugElementText = "/scene/" + slug + "/show";
    let expired_date = changeTextExpiredDate();
    let check_show_comment = $(".switch-share-link input").prop("checked")
    create_or_update_scene_link(slug, check_show_comment, expired_date);
    let today = new Date();
    var date_expired = false;
    $('#isSetExpired, .can_show_comment').on('change', function(e){
        onChangeLink(product_scene_dom, e);
    });

    $('#input-expired-day').on('input', function(e){
        if (this.value.length > this.maxLength) {
            this.value = this.value.slice(0, this.maxLength);
        }
        this.value = this.value.replace(/\D/g,'');
        onChangeLink(product_scene_dom, e);
    })
    //set Chapter, Variation, ShareLink text for ShareForm
    $('#shareModal .modal-content .modal-title').text(chapterElementText + ' | ' + variationElementText);
    var inputShareLinkElement = $('#shareModal input#scene_share_link');
    let share_url = window.location.origin + slugElementText;
    inputShareLinkElement.val(share_url); //demo

    if (!$("#qrcode").children().length){
        var qrcode = new QRCode("qrcode", {
            text: share_url,
            width: 128,
            height: 128,
            colorDark : "#000000",
            colorLight : "#ffffff",
            correctLevel : QRCode.CorrectLevel.H
        });
    }

    //init slider for modal
    var sliderContent = $('#shareModal .modal-body .video-time-slider-content');
    var sliderBars = sliderContent.find('.video-time-slider-bar');
    var video_element = button.parent().find('video').get(0);
    var videoInterval = [];
    var pinStopInterval = [];
    if (video_element) {
        videoInterval = setInterval(function () {
            if (video_element.readyState === 4) {
                sliderBars.each(function (i, el) {
                    var min_value = 0;
                    var max_value = video_element.duration;
                    var min_current = 0;
                    var max_current = video_element.duration;
                    if (!$(el).hasClass('noUi-target')) {
                        var slider_item = sliderContent.parent();

                        sliderContent.find('.video-time-slider-start').text(formatTime(min_current));
                        sliderContent.find('.video-time-slider-end').text(formatTime(max_current));

                        var start = [min_current, max_current];

                        noUiSlider.create(el, {
                            animate: true,
                            animationDuration: 0,
                            start: start,
                            connect: false,
                            range: {
                                'min': min_value,
                                'max': max_value
                            }
                        }).on('slide', function (values, handle, unencoded, tap, positions) {
                            var min = parseInt(values[0]);
                            var max = parseInt(values[1]);
                            var currentHandle = handle === 0 ? 'min' : 'max';

                            if (currentHandle == 'max') {
                                sliderContent.find('.video-time-slider-end').text(formatTime(max));
                            } else if (currentHandle == 'min') {
                                sliderContent.find('.video-time-slider-start').text(formatTime(min));
                            }
                        });

                        setHandleForSlider(el, slider_item);
                    } else {
                        sliderContent.find('.video-time-slider-start').text(formatTime(min_current));
                        sliderContent.find('.video-time-slider-end').text(formatTime(max_current));
                        //if exist update max min
                        el.noUiSlider.updateOptions({
                            start: [min_current, max_current],
                            range: {
                                'min': min_value,
                                'max': max_value
                            }
                        });
                    }
                    //implement update sharelink url
                    el.noUiSlider.on('change', function () {
                        var sliderValues = this.get();
                        //update start_at for share link
                        inputShareLinkElement.val(UpdateUrl(inputShareLinkElement.val(), {'start_at': parseInt(sliderValues[0]).toString()}));
                    });

                });
                clearInterval(videoInterval);
            }
        }, 300);
    }
}

function onChangeLink(product_scene_dom, e){
    if($(e.target).attr("id") == "isSetExpired"){
        if(!$(e.target).prop('checked')){
            $('#input-expired-day').val('');
        }
    }
    let slug = String(user) + $(".cscene__share").attr('share-url');
    let date_expired = changeTextExpiredDate();
    let show_comment = $('.can_show_comment').prop('checked');
    create_or_update_scene_link(slug, show_comment, date_expired);
}

function changeTextExpiredDate(){
    let value = $('#input-expired-day').val();
    let checked = $("#isSetExpired").prop('checked');
    let result_text = $(".expired-date")[0];
    let date_expired = new Date();

    var numbers = /^[0-9]+$/;
    if (!value.match(numbers)){
        if(value){
            $(result_text).text("番号のみを入力してください。");
            return;
        }
    }
    let date_format = "";
    if(!checked){
        $('#input-expired-day').prop("disabled", true);
        $(result_text).text("");
    }else{
        $('#input-expired-day').prop("disabled", false);
        if(value){
            date_expired = date_expired.addDays(parseInt(value));
        }
        date_format = date_expired.getFullYear() + '/' + String(date_expired.getMonth() + 1)
        + '/' + String(date_expired.getDate());
        let result = "共有終了日: " + date_format;
        $(result_text).text(result);
    }
    return date_format;
}

function create_or_update_scene_link(slug, show_comment, expired_date){
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/top/create_or_update_scene_link",
        data: {
            'slug': slug,
            'show_comment': show_comment,
            'expired_date': expired_date
        },
        success: function(){
            console.log("create share link success");
        }
    });
}

function scene_title_share_setting(newButton = null){
    console.log('scene_title_share_setting')
    let switch_btn = $("#share-setting");
    let checked = switch_btn.prop("checked");
    if(!checked){
        $("#share-setting").parents(".modal-content").find(".modal-body").hide();
    }
    switch_btn.on("change", function(e){
        let checked = $(this).prop("checked");
        if(checked){
            $("#share-setting").parents(".modal-content").find(".modal-body").show();
            update_scene_title_can_share(true, newButton);
            $('#input-expired-day').val(7);
            $('.expired-time').attr('data-expired-time', moment(new Date()).format('YYYY/MM/DD'));
        }else{
            $("#share-setting").parents(".modal-content").find(".modal-body").hide();
            update_scene_title_can_share(false);
        }
    })
}

function update_scene_title_can_share(share, newButton){
        let scene_title_id = $('.pd-scene-title-detail').attr('data-scene-title-id');
        $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/update_can_share",
        data: {
            'scene_title_id': scene_title_id,
            'can_share': share,
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let sceneShare = $(".cscene__share");
            let sceneShareImg = $(".cscene__share img");
            let sceneShareTxtSP = $(".cscene__share .txt-share-sp");
            let sceneShareTxtPC = $(".share-scene-pc .txt-bellow-icon");
            if(share){
                sceneShare.find("span").addClass("u-text-blue");
                sceneShare.addClass('cscene__can-share can-share-new');
                sceneShareTxtSP.text('共有中');
                sceneShareTxtPC.text('共有中');
                sceneShareImg.attr('src', '/static/images/scene-detail/icon-share-active.svg');
                let slug =  String(user) + sceneShare.attr('share-url');
                let date_expired = changeTextExpiredDate();
                let show_comment = $('.can_show_comment').prop('checked');
                create_or_update_scene_link(slug, show_comment, date_expired);
            }else{
                sceneShare.find("span").removeClass("u-text-blue");
                sceneShare.removeClass('cscene__can-share can-share-new');
                sceneShareTxtSP.text('このシーンを共有');
                sceneShareTxtPC.text('共有中');
                sceneShareImg.attr('src', '/static/images/scene-detail/icon-share.svg')
                $('#switch-chapter').prop('checked', true)
            }
        },
    })
}


$('.copy-link').off().on('click', function () {
    let link_element = $("#scene_share_link");
    link_element.select();
    document.execCommand('copy');
    // toastr.success("リンクをコピーしました。")
});


function undoneScene(target) {
    let parent = target.parents('.project-chapter-video-item, .project-delivery-item, .project-video-item, .project-delivery-item-content, .pd-scene-title-detail');
    let scene_title_id;
    if (parent.is('.project-chapter-video-item')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.project-video-item')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.project-delivery-item-content')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.pd-scene-title-detail')) {
        scene_title_id = parent.data('scene-title-id');
    }
    else {
        scene_title_id = parent.find('.project-delivery-item-content').data('scene-title');
    }
    let project_id = $('.project-item').data('project-id');
    let scene_id = parent.data('scene-id');
    if(!scene_id){
        scene_id = parent.find('.cscene__variation').data('scene-id');
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/undone",
        data: {
            'scene_title_id': scene_title_id,
            'project_id': project_id,
            'scene_id': scene_id,
            'type': 'scene_detail'
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            if (response.status === '200') {
                // toastr.success('検収を戻しました');
                let item = $(`.pd-scene-title-detail[data-scene-title-id=${scene_title_id}]`);
                icon = item.find('.project-chapter-video-undone');
                const spanDivIcon = icon.find(".material-symbols-rounded");
                icon.addClass("project-chapter-video-done");
                if (response.role === 'admin') {
                    icon.addClass("cannot-check")
                }
                spanDivIcon.removeClass("u-text-blue");
                spanDivIcon.addClass("u-text-gray");
                icon.removeClass("project-chapter-video-undone");
                
                
            } else {
                toastr.error('エラーが発生しました', '進行中に戻す');
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', '進行中に戻す');
        }
    })
}

function doneVideo(target) {
    let parent = target.parents('.project-video-item, .pd-scene-title-detail, .project-delivery-item-content');
    let scene_id;
    let scene_title_id;
    if (parent.is('.project-video-item')) {
        scene_id = parent.find('.video-item-component').data('scene-id');
    } else {
        scene_title_id = parent.attr('data-scene-title-id')
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/top/mark_as_done",
        data: {
            'scene_id': scene_id,
            'scene_title_id': scene_title_id
        },
        success: function (data) {
            if (data.result === 'success') {
                // toastr.success('検収しました');
                let heart_button = parent.find(".project-chapter-video-done");
                const spanDivIcon = heart_button.find(".material-symbols-rounded");
                spanDivIcon.addClass("u-text-blue")
                spanDivIcon.removeClass("u-text-gray");
                heart_button.removeClass("project-chapter-video-done");
                heart_button.addClass("project-chapter-video-undone");
                $(`.project-delivery-item-content[data-scene-title-id=${scene_title_id}]`).each(function (index, item) {
                    let icon = $(item).find('.icon--sicon-heart-o');
                    icon.removeClass("project-chapter-video-done");
                    icon.addClass("project-chapter-video-undone");
                });
                $(`.pd-section--delivery-video .project-delivery-item-content[data-scene-title-id=${scene_title_id}], .pd-section--update-video .project-delivery-item-content[data-scene-title-id=${scene_title_id}]`).each(function (index) {
                    $(this).parents(".cvideo").remove();
                    if (!$('.pd-section--update-video .project-delivery-item-content').length) {
                        $('.pd-section--update-video').remove()
                    }
                    countSceneUpdate(data);
                });
                projectRating();
            } else {
                toastr.error('エラーが発生しました', 'ハートつける');
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', 'ハートつける');
        }
    })
}

function countSceneUpdate(data) {
    let countTitle = $('.pd-section--update-video .project-delivery-item-content').length;
    $('.pbanner-tab-all .number-notification').attr('value', countTitle).text(countTitle);
    // Update progress bar
    $('.project-item').find('.pbanner__progress').html(data.progress_bar);
}

function actionBottomVideo() {
    $(document).on('click', '.project-chapter-video-undone:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if($(e.target).hasClass("project-chapter-video-done") && !$(e.target).hasClass("cannot-check")){
            doneVideo($(this))
        }else{
            undoneScene($(this));
        }
    });

    $(document).on('click', '.project-chapter-video-done:not(.cannot-check)', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if($(e.target).hasClass("project-chapter-video-undone")){
            undoneScene($(this));
        }else{
            doneVideo($(this))
        }
    });

    $(document).find('.cscene__bookmark, .bookmark-button').off().on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let scene_title_id = $(".cscene__bookmark.bookmark-button").parents('.pd-scene-title-detail').attr('data-scene-title-id');
        console.log($(".cscene__bookmark.bookmark-button").find('span').hasClass("bookmark"));
        if($(".cscene__bookmark.bookmark-button").find('span').hasClass("bookmark")){
            bookmark(scene_title_id, 'unbookmark')
        }else{
            bookmark(scene_title_id, 'bookmark');
        }
    });

}

function bookmark(scene_title_id, type_bookmark) {
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/bookmark_scene_title",
        data: {
            'scene_title_id': scene_title_id,
            'type_bookmark': 'scene',
            'type_action': type_bookmark,
        },
        success: function (data) {
            let span_bookmark = $('.cscene__bookmark.bookmark-button span');
            let p_bookmark = $('.bookmark-button p');
            if (type_bookmark === 'bookmark') {
                span_bookmark.addClass("u-text-blue").addClass("bookmark").removeClass("unbookmark");
                p_bookmark.text('保存済み')
                p_bookmark.addClass("u-text-blue");
                let style = document.querySelector(':root');
                let item = $('.slick-active.slick-current video')
                let img = item.attr('poster')
                if(!img) {
                    img = '#a7a8a9';
                    style.style.setProperty('--bg-album', img);
                } else {
                    style.style.setProperty('--bg-album', 'url(' + img + ') no-repeat scroll 50% 50% / cover padding-box border-box');
                }

                $('.icon-bookmark-navbar').addClass('showing')
                setTimeout(() => {
                    $('.icon-bookmark-navbar').addClass('add')
                }, 600)

                setTimeout(() => {
                    $('.icon-bookmark-navbar').removeClass('add showing')
                    style.style.setProperty('--bg-album', '#a7a8a9');
                }, 1000)
            } else {
                span_bookmark.addClass("unbookmark").removeClass("u-text-blue").removeClass("bookmark");
                p_bookmark.removeClass("u-text-blue");
                p_bookmark.text('コレクション')
                // toastr.success(data.mes);
            }
        },
        error: function () {
            toastr.error('エラーが発生しました');
        }
    })
}


function setActionForProjectItem(project_video_item_el, variation_id = -1) {
    let $project_video_item = project_video_item_el;

    $('video').on('play', function () {
        var vid = this;
        document.onkeyup = function(e) {
            if (!$(".mcomment-input-text.mcomment-autoExpand").is(":focus")) {
                $(vid).blur();
                const keyCode = e.which;
                let currentTime = parseInt(vid.currentTime);
                let now;

                if (keyCode === 37) {
                    now = currentTime - 5;
                    if(now < 0) {
                        vid.pause();
                        vid.currentTime = 0;
                    }
                    else {
                        vid.currentTime = now;
                    }
                } else if (keyCode === 39) {
                    now = currentTime + 5;
                    if (now > vid.duration) {
                        vid.pause();
                        vid.currentTime = 0;
                    }
                    else {
                        vid.currentTime = now;
                    }
                    console.log(currentTime)
                }
                else if (keyCode === 32) {
                    if (vid.paused || vid.ended) {
                        vid.play();
                    } else {
                        vid.pause();
                    }
                }
            }
        }
    });

    // update pintime when video pause
    $('video').on('pause', function () {
        let video = $(this)[0];
        let pins = $(this).parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
        if (pins.length < 1) {
            pins = $(this).parents('.pd-scene-title-detail').find('.pd-comment__main .mmessage-component .mcomment-pin')
        }
        pins.each(function (i, e) {
            if ($(e).is('.active')) {
                $(e).find('.pin-icon-time').html(msToTime(video.currentTime));
                $('.mcomment-input-title').html('<i class="icon icon--sicon-pin"></i>' + '<span>' + msToTime(video.currentTime) + '</span>');
            }
        })
    });

    $project_video_item.find('.video-item-share, .cscene__share').off().on('click', function () {
        updateModalInfo($(this))
        scene_title_share_setting();
        const element = document.querySelector('.owner-top');
        if (element) {
            element.classList.add("open-edit")
            element.classList.remove("close-edit")
        }
    });

    $project_video_item.find('video.cscene__not-radio').on('loadeddata', function () {
        let video_dom = $(this);
        let e = $(this)[0];
        let v_width = e.videoWidth;
        let v_height = e.videoHeight;
        let v_ratio = parseFloat(v_width) / parseFloat(v_height);
        if (v_ratio < 16 / 9) {
            video_dom.parents('.ccscene__thumb').removeClass('cscene__version-horizontal').addClass('cscene__version-vertical');
            video_dom.removeClass('cscene__ratio-43').addClass('cscene__ratio-35');
        } else {
            video_dom.parents('.ccscene__thumb').addClass('cscene__version-horizontal');
            video_dom.addClass('cscene__ratio-43');
        }
        let scene_dom = $(this).parents('.cscene__version');
        let scene_id = scene_dom.attr('data-scene-id');
        let data = new FormData();
        data.append('scene_id', scene_id);
        data.append('video_width', v_width);
        data.append('video_height', v_height);
        $.ajax({
            type: "POST",
            datatype: "json",
            contentType: false,
            processData: false,
            cache: false,
            url: "/top/update_size_scene",
            data: data,
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                console.log('ok')
            },
        })
    });

    $('video').on('play', function () {
        let scene_id = $(this).parents('.cscene__version').attr('data-scene-id');
        $('.s-audio[data-scene-id!=' + scene_id + ']').each(function () {
            if ($(this).find('.s-audio-control.active')) {
                let wave_index = $(this).attr('data-wavesurfer');
                let wave = wavesurfer_arr[wave_index];
                if (wave && wave.isPlaying()) {
                    wave.pause()
                }
            }
        })
    })
}

function initProjectComment(target) {
    target.mCustomScrollbar({
        theme: "light-2",
        autoExpandScrollbar: true,
        scrollInertia: 100,
        mouseWheel: {scrollAmount: 200},
        alwaysShowScrollbar: 1
    });

    // Scrollbar TextArea
    // mScrollbarInit();

    //Message button init
    initButtonSendProjectComment(target);

    let current_length = wavesurfer_arr.length;
    target.find('.video-comment-audio-wave').each(function (i, item) {
        if (!$(item).find('wave').length && !wavesurfer_arr[i + current_length]) {
            var audio_url = $(this).data('audio');

            var wavesurfer = WaveSurfer.create({
                container: item,
                waveColor: '#a7a8a9',
                progressColor: "#009ace",
                cursorColor: "#009ace",
                barWidth: 2,
                barRadius: 4,
                cursorWidth: 1,
                barGap: 1,
                mediaControls: false,
                height: 64,
                responsive: true,
                hideScrollbar: true,
                partialRender: true,
                splitChannels: true,
                barHeight: 0.6,
                backend: 'MediaElement'
            });

            wavesurfer_arr[current_length + i] = wavesurfer;
            let cmt_container = $(item).parents(".video-comment-item-reply");
            let cmt_id = cmt_container.attr("data-cmt-id");
            let peaks_loaded = cmt_container.attr("data-peaks-loaded");

            if(peaks_loaded){
                let array_peaks = peaks_loaded.split(" ");
                wavesurfer.load(audio_url, array_peaks, 'none');
            } else {
                wavesurfer.load(audio_url);
                wavesurfer.on('waveform-ready', function () {
                    let peaks = wavesurfer.backend.getPeaks(32);
                    let peaks_string = "";
                    for(let i = 0; i < peaks.length; i++)
                    {
                        peaks_string += String(Math.round(peaks[i] * Math.pow(10,8))/Math.pow(10,8)) + " ";
                    }
                    var values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'project'
                    }
                    $.ajax({
                        type: "POST",
                        url: "/top/update_comment",
                        data: values,
                        dataType: 'json',
                        success: function (data) {
                            console.log("success");
                        },
                        error: function (e) {
                            console.log(e);
                        }
                    });
              });
            }

            $(this).siblings('.video-comment-audio-title, .video-pin-time').on('click', function () {
                stop_video_audio();
                let is_play = false;
                let target_pin = $(this).siblings(".video-comment-audio-wave").siblings('.video-pin-time');
                if ($(this).siblings(".video-comment-audio-wave").length !== 0) {
                    $('video').each((i, e) => e.pause());
                    target.find('.video-pin-time').each(function(){
                        $(this).removeClass('playing')
                    });
                    for (i = 0; i < wavesurfer_arr.length; i++) {
                        if (wavesurfer_arr[i]) {
                            if (wavesurfer_arr[i].isPlaying()) {
                                if(wavesurfer_arr[i] === wavesurfer){
                                    is_play = true
                                }
                                wavesurfer_arr[i].playPause();
                            }
                        }
                    }
                    if(!is_play){
                        target_pin.addClass('playing');
                        wavesurfer.play();
                        wavesurfer.on('pause', function () {
                            if (wavesurfer.getDuration() === wavesurfer.getCurrentTime()) {
                                target_pin.removeClass('playing');
                            }
                            target_pin.removeClass('playing');
                        })
                    }
                }
            })
        }
    });

    window.URL = window.URL || window.webkitURL;
    target.find('input[id^="messenger-attach-"]').on('change', function (e) {
        let context = this;
        let files = this.files;

        if (files.length) {
            let video = document.createElement('video');
            video.preload = 'metadata';
            video.onloadedmetadata = function () {
                window.URL.revokeObjectURL(video.src);
                let duration = video.duration;
                $(context).attr('data-duration', Math.floor(duration));
            };
            video.src = URL.createObjectURL(files[0]);
        } else {
             $(context).attr('data-duration', -1);
        }


        let fileName = files[0].name;
        let clear_file_dom = "clear_" + $(e.target)[0].id;
        let comment_box = $(this).closest('.video-comment-item.comment-form');
        if (comment_box.find('.comment__textarea-file').length > 0) {
            comment_box.find('.comment__textarea-file span').text(fileName);
        } else {
            comment_box.find('.video-comment-message').prepend('<div class="comment__textarea-file"><span>' + fileName +
                '</span><button type="button" class="clear_file close '+ clear_file_dom +'" aria-hidden="true">×</button></div>')
        }

        comment_box.find('.clear_file').off().on('click', function () {
            let target_input = $(this).parents('.video-comment-message').find('input.video-comment-input-attach');
            $(this).parents('.video-comment-item.comment-form').find('.comment__textarea-file').remove();
            target_input.attr('data-duration', -1);
            target_input.val('');
        });
    });

    let messages = target.find('.s-text, s-filetext, .s-filedisable');
    $.each(messages, function (i, v) {
        let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
        v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
    });
    if (target.parents('.project-video-item.show-comment').find('.video-item-comment').length) {
        target.parents('.project-video-item.show-comment').find('.video-item-comment').mCustomScrollbar('scrollTo', 'bottom');
    }

    $('.fa-download, .comment__download-icon-down').on('click', function (e) {
        e.stopPropagation();
        let comment_id = $(this).parents('.video-comment-item-reply').attr('data-cmt-id');
        $.ajax({
            type: "GET",
            url: "/top/get_file_download_link",
            data: {
                "comment_id": comment_id,
                'type': 'project'
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                window.location.href = response.url;
            },
            fail: function (response) {
                toastr.error('エラーが発生しました', 'ファイルをダウンロード');
            }
        })
    });
}

function initButtonSendProjectComment(target) {
    target.find('.video-comment-input-text.cs-textarea').off('keyup').keyup(function () {
        let comment_content = $(this).val();
        if (!comment_content) {
            let button = $(this).parents('.video-comment-message').find('.button.button--text.button--text-primary');
            button.addClass('button--disabled');
            $(this).parents('.video-comment-message').find('.video-comment-input-label').addClass('button--disabled');
            $(this).parents('cs-textarea-wrapper').height('36px');
        } else {
            let button = $(this).parents('.video-comment-message').find('.button.button--text.button--text-primary.button--disabled');
            button.removeClass('button--disabled');
            $(this).parents('.video-comment-message').find('.video-comment-input-label.button--disabled').removeClass('button--disabled');
        }
        autoResize(this)
    });

    target.find('.video-comment-button-send').off('click').on('click', function () {
        if (!$(this).find('a.button--disabled').length) {
            autoResize($(this).siblings('.video-comment-input').find('textarea')[0]);
            $(this).find('a').addClass('button--disabled');
            let target = $(this);
            let project_id = target.parents('.project-item.active').attr('data-project-id');
            let comment = $(this).parents('.video-comment-message').find('.video-comment-input-text.cs-textarea').val();
            let parent_id = $(this).parents('.video-comment-item').data('parent-id');
            let messenger_attach_element = $(this).parents('.video-comment-item').find('input.video-comment-input-attach').first();
            let messenger_attach = messenger_attach_element.get(0).files[0];
            let duration = messenger_attach_element.first().attr('data-duration');
            let data = new FormData();
            data.append('file', messenger_attach);
            data.append('duration', duration);
            data.append('type', 'project');



            let right = parent_id && !$(this).parents('.video-comment-item').prev().is('.right') ? '' : ' right';
            data.append('right', right);

            let values = {
                'project_id': project_id,
                'comment': comment,
                'parent_id': parent_id,
            };

            for (const property in values) {
                data.append(property, values[property])
            }

            $(append_placeholder_comment(!parent_id, false, right, data)).insertBefore(target.parents('.video-comment-item')).hide().slideDown(300);
            $(this).parents('.video-comment-message').find('.video-comment-input-text.cs-textarea').val('');
            $(this).parents('.video-comment-item').find('input.video-comment-input-attach').val('');
            $('[for^="'+ $(this).parents('.video-comment-item').find('input.video-comment-input-attach')[0].id +'"]').addClass('button--disabled');
            $(this).parents('.video-comment-item').find('.comment__textarea-file').remove();
            $.ajax({
                type: "POST",
                url: "/top/create_comment",
                data: data,
                cache: false,
                processData: false,
                contentType: false,
                xhr: function () {
                    var xhr = new window.XMLHttpRequest();
                    if (messenger_attach) {
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                let percentComplete = (evt.loaded / evt.total) * 70;
                                $('.popover .upload-button-wrapper .fill .process').css('width', percentComplete + '%');
                            }
                        }, false);
                    }
                    return xhr;
                },
                beforeSend: function(data) {
                    if(messenger_attach) {
                        // toastr.info('アップロード中…');
                        $('.popover .upload-button-wrapper').css('display', 'flex');
                        $('.popover .upload-button-wrapper').addClass('clicked');
                        $('.popover .upload-button-wrapper .fill .process').css('width', '2%');
                    }else{
                        // toastr.info('コメント作成しています。');
                    }
                },
                success: function (data) {
                    if (data.status === '200') {
                        if (data.file) {

                            $('.popover .upload-button-wrapper .fill .process').css('width', '100%');
                            setTimeout(function () {
                                $('.popover .upload-button-wrapper').removeClass('clicked').addClass('success')
                            }, 1000);
                            setTimeout(function () {
                                $('.popover .upload-button-wrapper').removeClass('success').css('display', 'none')
                                $('.popover .upload-button-wrapper .fill .process').css('width', '0');
                            }, 2000);
                            // toastr.success('アップロードを完了しました。');
                        } else {
                            // toastr.success('コメントを作成しました。');
                        }
                        let target_dom = target.parents('.video-comment-item').prev();
                        $(data.comment).insertBefore(target.parents('.video-comment-item'));
                        target_dom.remove();
                        target_dom = target.parents('.popover.project-video-item');
                        initProjectComment(target_dom);
                    }
                },
                error: function (data) {
                    toastr.error('エラーが発生しました');
                    $('.popover .upload-button-wrapper').removeClass('clicked');
                    $('.popover .upload-button-wrapper').css('display', 'none');
                    $('.popover .upload-button-wrapper .fill .process').css('width', '0');
                    let target_dom = target.parents('.video-comment-item').prev();
                    target_dom.remove();
                    target_dom = target.parents('.popover.project-video-item');
                    initProjectComment(target_dom);
                }
            })
        }
    });
}

function setOpenVideoModalFor(_element, is_scene_detail) {
    _element.on('click', '.open-video-modal', function (e) {
        const element = document.querySelector('.owner-top');
        if (element) {
            element.classList.add("open-edit")
            element.classList.remove("close-edit")
        }
        e.preventDefault();
        e.stopPropagation();
        stop_video_audio();
        let scene_id = $(this).parents('.cscene.cscene--video').find('.cscene__variation.slick-current').first().data('scene-id');
        let scene_title_id = $(this).parents('.pd-section').attr('data-scene-title-id');
        if(is_scene_detail){
            scene_id = _element.find('.cscene__variation.slick-current').first().data('scene-id');
            scene_title_id = _element.attr('data-scene-title-id');
        }
        // let video_modal = $('#video-modal');
        let video_modal = $('#modal-upload-scene');
        video_modal.attr('action-type', 'edit');
        video_modal.attr('data-scene-id', scene_id);
        video_modal.attr('data-scene-title-id', scene_title_id);
        video_modal.find('.modal-dialog__header__text h1').text('シーンの編集');
        $.ajax({
            method: "GET",
            url: "/top/get_video_modal?scene_id=" + scene_id,
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                video_modal.find('.u-wrapper-reading').empty();
                video_modal.find('.u-wrapper-reading').html(response.html);
                // SoundcheckProject.videoModal();
                video_modal.modal('show');
                let countIndex = $('.scene-take-container').length + 1;
                if(countIndex > 1) {
                    $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
                } else {
                    $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
                }
                // video_modal.on('hidden.bs.modal', function () {
                //     if($('#processingSceneModal')[0].style.display === 'block') {
                //         $('body').addClass('modal-open');
                //     }
                // });
            },
            error: function (response) {
                console.log('error', response.responseText);
                toastr.error(response.responseJSON.message);
            }
        });

    });
}

function copyURL() {
    $('.video-item-share-btn, .cscene__share').off().on('click', function () {
        var share_url_element = $(this).parent().find('input#video-share-link');
        share_url_element.select();
        document.execCommand('copy');
    });
}

function initSearch() {
    let project_item = $('.project-item').first();
    let searchModal = $('#searchModal').first();
    let project_id = project_item.data('project-id');
    $('.project-item__search').on('click', function () {
        $(this).addClass('active');
    });
    let content_project = '';
    $('.project-item__search input, #pm-search').on('focus', function (e) {
        if ($('.project-tab-messenger.active').length) {
            $(this).attr('list', 'search_offer_scenes')
        } else {
            if ($('.pd-section--delivery-video').length > 0) {
                content_project = $('.project-tab-progress .tab--video-progress').clone(true);
            }
            $(this).removeAttr('list')
        }
    });

    $('.project-item__search input, #pm-search').on('keydown', function (e) {
        if ((e.key === 'Enter' || e.keyCode === 13) && !$('.project-tab-messenger.active').length) {
            let keyword = $(this).val();

            if (keyword !== '') {
                $.ajax({
                    type: "GET",
                    datatype: "json",
                    url: window.location.pathname + "/search?q=" + keyword,
                    beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        $(".loader").show();
                        $('.project-chapter-item-search').remove()
                    },
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        $('.tab--video-progress').addClass('hide');
                        $(response.html).appendTo($('.project-tab-progress'));
                        projectRating();
                        $('.sumo-select select').each(function () {
                            $(this).SumoSelect();
                        });
                        setWidthInput($('.mcommment[id="comment-input-1"]'));
                    },
                    error: function (response) {
                        $(".loader").hide();
                        toastr.warning("エラーが発生しました");
                    },
                    complete: function () {
                        $(".loader").hide();
                        setWidthInput($('.mcommment[id="comment-input-1"]'));
                    }
                });
            } else {
                $('.tab--video-progress').removeClass('hide');
                $('.project-chapter-item-search').remove();
                setWidthInput($('.mcommment[id="comment-input-1"]'));
            }
        }
    })
}


function get_messenger_artist(project_id, offer_active=null, filter_offer=null, show_offer=false) {
    let checkCompNow = $('.project-tab-messenger.active .psearch-main').length;
    let titlePage = $('.owner-top').attr('data-title-page') + ' | DM';
    $('title').text(titlePage);
    if (project_id) {
        let tab_messenger = $('.tab--messenger-artist');
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_messenger_artist",
            data: {
                'project_id': project_id,
                'offer_active': offer_active,
                'filter_offer': filter_offer
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                if (!$('.switch-dm').hasClass('tab-disabled')) {
                    $('.switch-dm').addClass('tab-disabled');
                }
                // if ($(".tab--messenger-artist").text().trim() === '') {
                    tab_messenger.append(`<div class="loading-process"><div class="load-more-loading loading-z-index"></div></div>`);
                // }

            },
            success: function (data) {
                let first_load = false;
                $('.load-more-loading').remove();
                $('.loading-process').remove();
                if (!$('.psearch-main').length) {
                    first_load = true
                }
                $('.tab--messenger-artist').empty();
                tab_messenger.append(data.html);
                if (user_role === 'master_client' && $('.psearch-main').length) {
                    $('.pbanner-tab-all').trigger('click');
                }
                if (user_role === 'admin') {
                    // searchCreator();
                    playSaleContent();

                }
                if (user_role === 'admin' || user_role === 'master_admin') {
                    dragDropSearch();
                }

                styleGuide();
                if (first_load || !!checkCompNow) {
                    if (user_role === 'admin' || user_role === 'master_admin') {
                        dragDropMessengerOwner();
                        validateContractForm();
                    }
                }

                list_scenes = data.list_scenes;
                $('#search_offer_scenes').remove();

                if (list_scenes) {
                    let html_data_list = $(`<datalist id='search_offer_scenes'></datalist>`);
                    list_scenes.map((value) => {
                        let option_dom = $(`<option>` + escapeHtml(value) + `</option>`);
                        option_dom.appendTo(html_data_list);
                    });
                    html_data_list.insertAfter('#pm-search');
                }
                let item_offers = $('.item-offer');
                if (item_offers.length > 0 && !offer_active) {
                    offer_active = item_offers.first().attr('data-offer');
                }
                if (data.offer_status && data.offer_status !== 'not-exists' && offer_active) {
                    if (is_pc === "True" || from_refer){
                        $('.mitem[data-offer^=' + offer_active + ']').click();
                        setTimeout(function () {
                            let top_length = $('.mitem.mactive').offset().top - 500;
                            $('.mlist-wrap.mscrollbar--vertical').animate({scrollTop: top_length}, 1000);
                            from_refer = false;
                        }, 1000)
                    }
                } else if (data.offer_status !== 'not-exists') {
                    if (is_pc === "True") {
                        $(".mlist .list--offers-project .list-old-item").children().eq(0).trigger('click');
                    }
                }
                if(show_offer) {
                    let should_active_offer = $('.list--offers-project .notification.notification--blue.notification--round:not(.hide)').filter(function() {
                        return parseInt($(this).text()) > 0 && !$(this).parents('.mscene.mitem').is('.mactive')
                    })
                    if(should_active_offer.length) {
                        should_active_offer.first().trigger('click')
                    }
                }
                getOnlineStatusUser(project_id);
                $('.input-search-offer').on('input', function () {
                    if ($(this).val() === '') {
                        $('.search-delete-icon').addClass('hide');
                    } else {
                        $('.search-delete-icon').removeClass('hide');
                    }
                })

                $('.search-delete-icon').on('click', function () {
                    $('.input-search-offer').val('');
                    $(this).addClass('hide')
                })
                scrollListComment($('.mmessage-list'))
                if (data.offer_status && data.offer_status !== 'not-exists') {
                    $('.switch-dm-txt').removeClass('navbar-active')
                    if (data.offer_status === 'waiting') {
                        $('.switch-dm-txt.text-left').addClass('navbar-active')
                    } else {
                        //processing
                        $('.switch-dm-txt.text-right').addClass('navbar-active')
                    }
                }
            },
            complete: function () {
                setWidthInput($('.mcommment[id="comment-input-1"]'));
                tab_messenger.find('.load-more-loading').remove()
                if ($('.psearch-main').length > 0) {
                    $('.navigation-top-app-bar').css('display', 'none');
                }
                scrollCommentBar();
                resizeCommentInput();
                scrollListComment($('.column-list-offer .custom-list-wrap'))
                $('.switch-dm').removeClass('tab-disabled');
               resetTopMessageList()
                if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
                    calcHeightColumnRight()
                }
                 if ($(window).width() < max_width_sp_device && $('.project-tab-messenger.active').length > 0) {
                     resetPositionCommentBox();
                 }
            }
        })
    }
}

function resetPositionCommentBox() {
    let header_global = $('.sheader');
    let bottom_nav = $('.block-navigation-bar');
    let footer_comment_block = $('.footer-comment-block');
    let height_bottom_nav = bottom_nav.outerHeight();
    const space_bottom_dm = 60;
    const space_bottom_dm_has_header = 70;
    let top_header_value = parseInt(header_global.css('top').replace('px', ''));
    if (bottom_nav.hasClass('hide')) {
        footer_comment_block.css('bottom', '0px');
    } else {
        if (top_header_value < 0) {
            footer_comment_block.css('bottom', `${space_bottom_dm}px`);
        } else {
            footer_comment_block.css('bottom', `${space_bottom_dm_has_header + height_bottom_nav}px`);
        }
    }
}

function calcHeightColumnRight() {
   let column_right = $('.mcolumn--right');
   let header_global = $('.sheader');
    let banner_block = $('.new-banner-project');
    let top_app_bar = $('.navigation-top-app-bar')
    let nav_bottom = $('.block-navigation-bar');
    let height_nav_bottom = 0;
    if (nav_bottom.length > 0 && !nav_bottom.hasClass('hide')) {
        height_nav_bottom = nav_bottom.outerHeight()
    }
    let topHeaderValue = parseInt(header_global.css('top').replace('px', ''));
    if (topHeaderValue < 0) {
        column_right.css({
            'max-height': `calc(100vh - ${top_app_bar.outerHeight() + banner_block.outerHeight()}px)`,
            'top': `${top_app_bar.outerHeight() + banner_block.outerHeight()}px`,
            'margin-top': '0'
        })
    }else {
        column_right.css({
            'max-height': `calc(100vh - ${header_global.outerHeight() + height_nav_bottom + top_app_bar.outerHeight() + banner_block.outerHeight()}px)`,
            'top': `${header_global.outerHeight() + top_app_bar.outerHeight() + banner_block.outerHeight()}px`
        })
    }
}
function resetTopMessageList() {
    let header_global = $('.sheader');
    let banner_block = $('.new-banner-project');
    let content_active = $('.project-tab.project-tab-messenger.active');
    let top_app_bar = $('.navigation-top-app-bar')
    let nav_bottom = $('.block-navigation-bar');
    let height_nav_bottom = 0;
    if (nav_bottom.length > 0 && !nav_bottom.hasClass('hide')) {
        height_nav_bottom = nav_bottom.outerHeight();
    }
    let total_space_top = top_app_bar.outerHeight() + banner_block.outerHeight() + header_global.outerHeight();
    let total_space_top_without_header = top_app_bar.outerHeight() + banner_block.outerHeight();
    let comment_block = $('.footer-comment-block');
    let add_offer_block = $('.action-add-offer')
    let left_offer = $('.column-list-offer .mcolumn-content')
    let height_add_offer_block = 0;
    if (add_offer_block.length > 0) {
        height_add_offer_block = add_offer_block.outerHeight();
    }
    if (header_global.length > 0) {
        let topHeaderValue = parseInt(header_global.css('top').replace('px', ''));
        if (topHeaderValue < 0) {
            add_offer_block.css('bottom', 0)
            if ($(window).width() < max_width_sp_device) {
                content_active.css('top', `${banner_block.outerHeight()}px`)
                left_offer.css(`calc(100vh - ${banner_block.outerHeight() + top_app_bar.outerHeight() + height_add_offer_block}px)`)
            } else if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device){
                   content_active.css('top', `${banner_block.outerHeight() + top_app_bar.outerHeight()}px`)
            }
            else {
                content_active.css('top', `${total_space_top_without_header}px`)
            }
        } else {
            add_offer_block.css('bottom', `${height_nav_bottom}px`)
            if ($(window).width() < max_width_sp_device) {
                content_active.css('top', `${header_global.outerHeight() + banner_block.outerHeight()}px`)
                left_offer.css('height', `calc(100vh - ${header_global.outerHeight() + banner_block.outerHeight() + top_app_bar.outerHeight() + height_add_offer_block + nav_bottom.outerHeight()}px)`)
                comment_block.css('bottom', `${comment_block.outerHeight() + nav_bottom.outerHeight()}px`)

            } else if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
                content_active.css('top', `${header_global.outerHeight() + banner_block.outerHeight() + top_app_bar.outerHeight()}px`)
            } else {
                content_active.css('top', `${total_space_top}px`)
            }
        }
    }
}
function actionSearch() {
    $('.sform-group__icon').on('click', function () {
        $(".pd-search-keyword .sform-group__input-group").width('100%');
        $(".pd-search-keyword .sform-group__input-group").css('display', 'block');
        $('.sform-group__icon').css('display', 'none');
    });

    $('.search-delete').on('click', function () {
        $(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').val('');
        if ($(this).closest('.sform-group__input-group').find('.sform-control[type="search"]').is('#pm-search')) {
            $('.tab--video-progress').removeClass('hide');
            $('.project-chapter-item-search').remove();
        }
        removeSearchOffer();
        setWidthInput($('.mcommment'))
        $(".pd-search-keyword .sform-group__input-group").width('0');
        $(".pd-search-keyword .sform-group__input-group").css('display', 'none');
        $('.sform-group__icon').css('display', 'block');
        $(this).hide();
    });
}


function changePlaceHolderSearch() {
    let placeHolderRole = user_role === 'admin' ? 'スレッドを検索' : 'プロジェクト名を検索';
    let placeHolderSearch = $('.project-tab-messenger.active').length ? placeHolderRole : 'シーンを検索';
    $('#pm-search').attr('placeholder', placeHolderSearch);
}

function toggleCollapse() {
    $(document).on('click', '.messenger-infor .mcolumn-header', function (e) {
        resizeCommentInput2()
        e.preventDefault();
        if ($(window).width() > max_width_sp_device) {
            $(this).closest('.mcolumn--right').toggleClass('active');
            $(this).closest('.mcolumn--right').find('.mcolumn-header-toggle').toggleClass('active');
            $(this).closest('.mcolumn--right').find('.mcolumn-content').toggleClass('active');
            $(this).toggleClass('active');
            $(this).closest('.mrow').find('.mcolumn--wrap').toggleClass('active');

            $('.mcolumn-header-toggle').on('click', function () {
                if ($(window).width() < max_width_sp_device || $(window).width() > max_width_tablet_device) {
                    if ($('.mcolumn--right').hasClass('active')){
                        $(".mcolumn--right").css('width', '100%');
                    }else {
                        $(".mcolumn--right").width('55px');
                    }
                }
            });

            $('.mcolumn-header .icon--sicon-storage').on('click', function () {
                $(".mcolumn--right").width('100%');
            });
        }
        setTimeout(() => {
            setWidthInput($('.mcommment[id="comment-input-1"]'));
            setTimeout(() => {
                setWidthInput($('.mcommment[id="comment-input-1"]'));
                setTimeout(() => {
                    setWidthInput($('.mcommment[id="comment-input-1"]'));
                    setTimeout(() => {
                        setWidthInput($('.mcommment[id="comment-input-1"]'));
                        setTimeout(() => {
                            setWidthInput($('.mcommment[id="comment-input-1"]'));
                            setTimeout(() => {
                                setWidthInput($('.mcommment[id="comment-input-1"]'));
                            }, 50);
                        }, 50);
                    }, 50);
                }, 50);
            }, 50);
        }, 10);
    });
}

function removeSearch() {
    $('#pm-search').val('');
    $('.pd-search-keyword .sform-group__input-group').width('0');
    $('.pd-search-keyword .sform-group__input-group').css('display', 'none');
    $('.pd-search .sform-group__icon').css('display', 'block');
    $('.search-delete').hide();
}

 //Tooltip-------------------------
 function getbuttonPCPositionFirst() {
    let buttonPC = $('.btn-tutorial-pc').get(0);
     if (buttonPC) {
         buttonPC.style.top = $(window).height() - buttonPC.offsetHeight - 10 + 'px';
     }
}

function getbuttonPCPosition() {
    let newVideoMenu = $('.new-video-menu').get(0);
    let buttonPC = $('.btn-tutorial-pc').get(0);
    if (buttonPC) {
        buttonPC.style.left = newVideoMenu.offsetWidth + newVideoMenu.offsetLeft - buttonPC.offsetWidth - 10 + 'px';
        if ($(window).scrollTop() + $(window).height() === $(document).height()) {
            buttonPC.style.top = $(window).height() - (newVideoMenu.offsetParent.scrollHeight - newVideoMenu.offsetHeight) - 45 + 'px';
        } else {
            buttonPC.style.top = $(window).height() - buttonPC.offsetHeight - 10 + 'px';
        }
    }
}

 function tooltipShow() {
    let allTooltip = $('.all-tooltip');
    let background = $('.tutorial-container-background');
    let tooltips = $('.all-tooltip .tooltip-tutorial').get();
    let allIndexTooltip = [];
    let pbannerTabs = $('.pbanner-tabs').get(0);
    let fullDiv = $('main').get(1);
    let pins = [ $('.pbanner-tab-all').get(0), $('.pbanner-tab--exchange').get(0), $('.pbanner-tab-message').get(0) ].filter((value, key) => {
        if(!!value || value !== undefined)
            allIndexTooltip.push(key);
            return value;
    });

    let pinHeights = [ pbannerTabs, pbannerTabs, pbannerTabs ].filter((value) => {
        if(!!value || value !== undefined)
            return value
    });

    if(pins.length === 0) {
        $('.btn-tutorial-pc').addClass('hide-button-tutorial');
        $('.btn-tutorial-sp').addClass('hide-button-tutorial');
    } else if(pins.length === 1) {
        $('.btn-prev-tutorial').addClass('hide-button-tutorial')
    }

    // let newVideoMenu = $('.new-video-menu').get(0);
    let buttonPC = $('.btn-tutorial-pc').get(0);

    window.addEventListener('resize', contentPosition, false);
    window.addEventListener('scroll', getbuttonPCPosition, false);

    function getOffset(el) {
        const rect = el.getBoundingClientRect();
        return {
            left: rect.left + window.scrollX,
            top: rect.y + window.scrollY,
        };
    }

    getbuttonPCPosition();

    function contentPosition() {
        getbuttonPCPosition()
        allTooltip = $('.all-tooltip');
        background = $('.tutorial-container-background');
        tooltips = $('.all-tooltip .tooltip-tutorial').get();
        allIndexTooltip = [];
        pbannerTabs = $('.pbanner-tabs').get(0);
        fullDiv = $('main').get(1);
        pins = [
            $('.pbanner-tab-all').get(0),
            $('.pbanner-tab--exchange').get(0),
            $('.pbanner-tab-message').get(0),
            ].filter((value, key) => {
                if(!!value || value !== undefined)
                    allIndexTooltip.push(key);
                    return value;
            });

        pinHeights = [ pbannerTabs, pbannerTabs, pbannerTabs ].filter((value) => {
            if(!!value || value !== undefined)
                return value;
        });

        pins.forEach((pin, key) => {
        let tooltip = tooltips[allIndexTooltip[key]];
        let pinHeight = pinHeights[key];
        let content = tooltip.querySelector('.tooltip-content');
        let arrow = content.querySelector('.arrow-tutorial');

        if (getOffset(pin).left + content.offsetWidth / 2 > fullDiv.offsetWidth) {
            const extraLeft = fullDiv.offsetWidth - (getOffset(pin).left + content.offsetWidth / 2);
            content.style.left = getOffset(pin).left - content.offsetWidth / 2 + extraLeft + 'px';
        } else if (getOffset(pin).left + fullDiv.offsetLeft < content.offsetWidth / 2) {
            content.style.left = -fullDiv.offsetLeft;
        } else {
            content.style.left =  getOffset(pin).left - content.offsetWidth / 2 + pin.offsetWidth / 2 + 'px';
        }

        if($(window).width() <= 993) {
            content.style.top = pinHeight.getBoundingClientRect().top + 'px';
        } else {
            content.style.top = pinHeight.getBoundingClientRect().top - content.offsetHeight - arrow.offsetHeight * 3 + 'px';
        }
        if($(window).width() <= 993) {
            arrow.style.left = pin.getBoundingClientRect().left - content.offsetLeft + pin.offsetWidth / 2 + 'px';
        } else {
            arrow.style.left = pin.getBoundingClientRect().left - content.offsetLeft + pin.offsetWidth / 3 + 'px';
        }
        });
    }
    let indexTooltip = 0;

    $(document).on('click', '.btn-tutorial-sp, .btn-tutorial-pc', function (e) {
        indexTooltip = 0
        $('html, body').animate({scrollTop: 0,}, 500);
        $('.srm3').css({"overflow": "hidden"})
        setTimeout(() => {
        contentPosition();
        allTooltip.addClass('active-tooltip');
        background.addClass('active-background');
        tooltips[allIndexTooltip[indexTooltip]].querySelector('.tooltip-content').classList.add('active-tooltip');
        $(`#count-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(indexTooltip + 1);
        $(`#total-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(pins.length);
        }, 500);
    });

    $(document).on('click', '.btn-next-tutorial', function (e) {
        contentPosition();
        if (indexTooltip < pins.length - 1) {
            $('html, body').animate({ scrollTop: 0, }, 100);
            setTimeout(() => {
                contentPosition();
                tooltips[allIndexTooltip[indexTooltip]].querySelector('.tooltip-content').classList.remove('active-tooltip');
                tooltips[allIndexTooltip[indexTooltip + 1]].querySelector('.tooltip-content').classList.add('active-tooltip');
                indexTooltip++;
                $(`#count-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(indexTooltip + 1);
                $(`#total-number-toturial${allIndexTooltip[indexTooltip] + 1}`).html(pins.length);
            }, 105)
        } else {
            $('html, body').animate({ scrollTop: 0, }, 500);
            $('.srm3').css({"overflow": "auto"})
            allTooltip.removeClass('active-tooltip');
            background.removeClass('active-background');
            tooltips[allIndexTooltip[indexTooltip]].querySelector('.tooltip-content').classList.remove('active-tooltip');
            indexTooltip = 0;
        }
        getbuttonPCPosition()
    });
    $(document).on('click', '.btn-prev-tutorial', function (e) {
        $('html, body').animate({ scrollTop: 0, }, 100 );
        setTimeout(() => {
            contentPosition()
            tooltips[allIndexTooltip[indexTooltip]].querySelector('.tooltip-content').classList.remove('active-tooltip');
            tooltips[allIndexTooltip[indexTooltip - 1]].querySelector('.tooltip-content').classList.add('active-tooltip');
            indexTooltip--;
        }, 100)
    });
}
//Tooltip-------------------------

//Floating input---------------------------
function setWidthInput(dom) {
    if (!dom.hasClass('mcomment-new')) {
        if (dom.length === 0 || dom.parent()[0].offsetWidth === 0) {
            setTimeout(() => {
                setWidthInput($('.mcommment[id="comment-input-1"]'));
            }, 500);
        } else {
            const domThisElement = dom
            const domParentMaction = domThisElement.parent();
            domThisElement.css("width", `${Number(domParentMaction[0].offsetWidth) - 32}px`);
        }
    }
}

function checkWitdhInput() {
    $( window ).resize(function() {
        setWidthInput($('.mcommment[id="comment-input-1"]'));
    })
}

$(document).on('click', '.pbanner-tab-message.active', function(){
    if ($('.psearch-main').length > 0) {
        $('.navigation-top-app-bar').css('display', '');
    }
    setWidthInput($('.mcommment[id="comment-input-1"]'));
})
//End Floating input---------------------------
//ACR results modal---------------

function acrResults () {
    $(document).on('click', '.acr-result-icon.deactive', function(e) {
        e.stopPropagation();
        e.preventDefault();
    });

    $(document).on('click', '#modal-ACR-check-popup .smodal-close', function(e) {
        e.stopPropagation();
        e.preventDefault();
        $('#modal-ACR-check-popup').modal('hide');
    })
        
    $(document).on('click', '.acr-result-icon.active', function(e) {
        e.stopPropagation();
        e.preventDefault();
        $('#modal-ACR-check-popup').modal({
            backdrop: false,
        }) 
        $('#modal-ACR-check-popup').modal('show');
        let pk = '';
        let file_type = '';
        if($(this).parents('.tfile-infor.tfile-infor--scene.tfile-type.tfile-type--file').length) {
            pk = $(this).parents('.tfile-infor.tfile-type.tfile-infor--scene.tfile-type--file').attr('data-scene-id')
            file_type = 'Scene'
        } else if ($(this).parents('.mfolder__sub').length) {
            pk = $(this).parents('.mfolder__sub').attr('data-file-id')
        } else if ($(this).parents('.tfile-infor.tfile-type.tfile-type--file').length) {
            pk = $(this).parents('.tfile-infor.tfile-type.tfile-type--file').attr('data-file-id')
        } else if ($(this).parents('.list-group-item').length) {
            pk = $(this).parents('.list-group-item').attr('data-file-id')
        }else if ($(this).parents('.scene-style').length && $(this).hasClass('acr-result-icon btn-finger-print active')) {
            pk = $(this).attr('data-file-id')
        }
        $.ajax({
            type: "GET",
            url: "/top/get_acr_result_by_file",
            data: {
                'pk': pk
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                let acr_result = JSON.parse(response['result'])
                let music_container = $('#modal-ACR-check-popup .master-match')

                if(acr_result['music']) {
                    music_container.removeClass('hide')
                    music_container.find('.ACR-check-detail-content-bottom').remove()
                    acr_result['music'].forEach(function(i,e) {
                        // let start_second = convertSecondsToTimeWithHours(i.offset);
                        // let end_second = convertSecondsToTimeWithHours(i.played_duration + i.offset)
                        let duration_played = convertSecondsToTimeWithHours(i.played_duration)
                        let start_second = convertSecondsToTimeWithHours(Math.round(i.result.db_begin_time_offset_ms/1000))
                        let end_second = convertSecondsToTimeWithHours(Math.round(i.result.db_begin_time_offset_ms/1000) + i.played_duration)
                        let score = i.result.score
                        if (score > 100) {
                            score = 100;
                        }
                        let artist = ''
                        i.result.artists.forEach(function(i, e) {
                            if(e==0) {
                                artist += i.name
                            } else {
                                artist += (', ' + i.name)
                            }
                        })

                        let cover_url = '/static/images/default_cover_art.svg'
                        if(i.cover_url) {
                            cover_url = i.cover_url
                        }

                        let title = i.result.title

                        $(
                        `<div class="ACR-check-detail-content-bottom">
                        <div class="ACR-check-detail-content-item">
                            <div class="ACR-item-left">
                                <div class="ACR-image-album-thumb">
                                    <img src="${cover_url}" alt="thumb">
                                    <div class="ACR-matching-percent">
                                        <div class="ACR-percent-bar-border">
                                            <div class="ACR-matching-percent-child" style="height: 100%; width: ${score}%;"></div>
                                        </div>
                                        <div class="ACR-percent-num">${score}%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="ACR-item-middle">
                                <div class="ACR-item-title">${title}</div>
                                <div class="ACR-item-artist-name">${artist}</div>
                                <div class="ACR-item-artist-match-time">
                                    <div class="ACR-item-artist-list-match">
                                        <div class="ACR-item-artist-list-match-item">${start_second} - ${end_second}</div>
                                    </div>
                                    <div class="ACR-item-artist-total-match">${duration_played}</div>
                                </div>
                            </div>
                            <div class="ACR-item-right">
                                <i class="icon icon--sicon-search"></i>
                            </div>
                            </div></div>`).insertAfter(music_container.find('.ACR-check-detail-content-top'))
                    })
                } else {
                    music_container.addClass('hide')
                }

                let cover_container = $('#modal-ACR-check-popup .cover-match')

                if(acr_result['cover_songs']) {
                    cover_container.removeClass('hide')
                    cover_container.find('.ACR-check-detail-content-bottom').remove()
                    acr_result['cover_songs'].forEach(function(i,e) {
                        let artist = ''
                        i.result.artists.forEach(function(i, e) {
                            if(e==0) {
                                artist += i.name
                            } else {
                                artist += (', ' + i.name)
                            }
                        })

                        let cover_url = '/static/images/default_cover_art.svg'
                        if(i.cover_url) {
                            cover_url = i.cover_url
                        }

                        let title = i.result.title

                        $(`<div class="ACR-check-detail-content-bottom">
                        <div class="ACR-check-detail-content-item">
                            <div class="ACR-item-left">
                                <div class="ACR-image-album-thumb">
                                    <img src="${cover_url}" alt="thumb">
                                </div>
                            </div>
                            <div class="ACR-item-middle ">
                                <div class="ACR-item-title">${title}</div>
                                <div class="ACR-item-artist-name">${artist}</div>
                            </div>
                            <div class="ACR-item-right ">
                                <i class="icon icon--sicon-search"></i>
                            </div>
                        </div></div>`).insertAfter(cover_container.find('.ACR-check-detail-content-top'))
                    })
                } else {
                    cover_container.addClass('hide')
                }


                let custom_container = $('#modal-ACR-check-popup .custom-match')

                if(acr_result['custom_files']) {
                    custom_container.removeClass('hide')
                    custom_container.find('.ACR-check-detail-content-bottom').remove()
                    acr_result['custom_files'].forEach(function(i,e) {
                        // let start_second = convertSecondsToTimeWithHours(i.offset);
                        // let end_second = convertSecondsToTimeWithHours(i.played_duration + i.offset)
                        let duration_played = convertSecondsToTimeWithHours(i.played_duration)
                        let start_second = convertSecondsToTimeWithHours(Math.round(i.result.db_begin_time_offset_ms/1000))
                        let end_second = convertSecondsToTimeWithHours(Math.round(i.result.db_begin_time_offset_ms/1000) + i.played_duration)
                        let score = i.result.score
                        if (score > 100) {
                            score = 100;
                        }
                        let artist = ''

                        let cover_url = '/static/images/default_cover_art.svg'

                        let title = i.result.title

                        $(`<div class="ACR-check-detail-content-bottom">
                        <div class="ACR-check-detail-content-item">
                            <div class="ACR-item-left">
                                <div class="ACR-image-album-thumb">
                                    <img src="${cover_url}" alt="thumb">
                                    <div class="ACR-matching-percent">
                                        <div class="ACR-percent-bar-border">
                                            <div class="ACR-matching-percent-child" style="height: 100%; width: ${score}%;"></div>
                                        </div>
                                        <div class="ACR-percent-num">${score}%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="ACR-item-middle">
                                <div class="ACR-item-title">${title}</div>
                                <div class="ACR-item-artist-name">${artist}</div>
                                <div class="ACR-item-artist-match-time">
                                    <div class="ACR-item-artist-list-match">
                                        <div class="ACR-item-artist-list-match-item">${start_second} - ${end_second}</div>
                                    </div>
                                    <div class="ACR-item-artist-total-match">${duration_played}</div>
                                </div>
                            </div>
                            <div class="ACR-item-right">
                                <i class="icon icon--sicon-search"></i>
                            </div>
                        </div></div>`).insertAfter(custom_container.find('.ACR-check-detail-content-top'))
                    })
                } else {
                    custom_container.addClass('hide')
                }
            }
        });
    });

    $(document).on('hidden.bs.modal shown.bs.modal', '#modal-ACR-check-popup', function(e){
        console.log('type', e.type);
        if(e.type === 'shown' && $(document).find('.no-modal-backdrop')) {
            $(document).find('.no-modal-backdrop').remove();
        }
    })

    $(document).on('click', '.ACR-item-right', function() {
        const title = $(this).closest('.ACR-check-detail-content-bottom').find('.ACR-item-title').text();
        const artist = $(this).closest('.ACR-check-detail-content-bottom').find('.ACR-item-artist-name').text();
        window.open('http://google.com/search?q='+title + ' ' + artist);
    })
}

//ACR results modal end-----------

function getDraftMessageDM(offer_id) {
    let data_form = new FormData();
    data_form.append('offer_id', offer_id);
    data_form.append('type_comment', '1');
    currentOffer = offer_id;

    $.ajax({
        type: "POST",
        contentType: false,
        processData: false,
        cache: false,
        url: "/message/get_draft_message",
        data: data_form,
        success: function (data) {
            if(data.ok) {
                return;
            }
            list_file_id = JSON.parse(data.list_file_id);
            list_folder_id = JSON.parse(data.list_folder_id);

            if(!data.message && !Object.keys(list_file_id).length && !Object.keys(list_folder_id).length){
                $(document).find('.mcomment-top').hide();
                $(document).find('.mcomment-input-text').attr('type_input', '');
                $(document).find('.mcomment-send.active').removeClass('active');
                return;
            }
            $(document).find('.mcomment-bottom').trigger('click');
            $(document).find('.mcomment-input-text').val(data.message);
            calculateHeightCommentInput($(document).find('.mcomment-input-text')[0])
            // valInput = data.message;
            $(document).find('.mcomment-input-text').focus();
            var file_conatiner_dom = $(document).find('.mattach-previews');
            file_conatiner_dom.empty();
            if(data.file_name){
                var file_name = data.file_name.split('\\');
                for(var i = 0; i < file_name.length; i++ ){
                    file_conatiner_dom.append(`<div class="mattach-template collection-item item-template" data-total="" data-loaded="">
                    <div class="mattach-info" data-dz-thumbnail=""><div class="mcommment-file"><div class="progress"><div class="determinate" style="width: 100%; transition: all 1s ease 0s;" data-dz-uploadprogress=""></div></div>
                    <div class="mcommment-file__name" data-dz-name="">${file_name[i]}</div>
                    <div class="mcommment-file__delete" href="#!" data-dz-remove=""><span class="progress-text"></span><i class="icon icon--sicon-close"></i></div></div></div></div>`)
                }
            }
            $(document).find('.mcomment-input-text').attr('type_input', '');
            $(document).find('.mcomment-send').addClass('active');
        },
        fail: function (data) {
            // toastr.error(gettext('Something went wrong!'));
        },
        error: function(xhr, status, error) {
            // var err = eval("(" + xhr.responseText + ")")
            // alert(err.message);
        },
        complete: function () {
            setWidthInput($('.mcommment[id="comment-input-1"]'));
        }
    });
}

function initDraftMessageOnLoaded(){
    setTimeout(() => {
        let offer_id = $(document).find('.mitem.mactive').attr('data-offer');
        if(!offer_id) {
            return initDraftMessageOnLoaded();
        }
        if(window.location.href.toString().includes("tab=messenger") || window.location.href.toString().includes(`offer=${offer_id}`)){
            // getDraftMessageDM(offer_id);
        }
    }, 1500);


}

function actionClickMitem(){
    $(document).on('click', '.mcolumn.mcolumn--left .mitem:not(.active)', async function(){
        // await doneTyping($(document).find('textarea.mcomment-input-text').val(), currentOffer)
        const order_id = $(this).attr('data-offer');
        // valInput=''
        list_file_id={}
        list_folder_id={}
        if(order_id) {
            // getDraftMessageDM(order_id);
        }
    })
}

function getOnlineStatusUser(project_id) {
    let data_form = new FormData();
    let filter_offer = $('.martist #offer-filter').is(':checked') ? 'processing' : 'waiting'
    data_form.append('project_id', project_id);
    data_form.append('filter_offer', filter_offer);

    $.ajax({
        type: "POST",
        contentType: false,
        processData: false,
        cache: false,
        url: "/message/get_online_status_DM",
        data: data_form,
        success: function (data) {
            objStatus = data.list_status;
            listKey = Object.keys(objStatus || {});
            if(listKey.length) {
                for(var i=0; i<listKey.length; i++) {
                    if(objStatus[listKey[i]]) {
                        $(document).find(`.mscene.mitem[data-offer=${listKey[i]}] .user-status-icon.offline`).removeClass('offline')
                        $(document).find(`.mscene.mitem[data-offer=${listKey[i]}] .user-status-icon:not(.online)`).addClass('online')
                    } else {
                        $(document).find(`.mscene.mitem[data-offer=${listKey[i]}] .user-status-icon.online`).removeClass('online')
                        $(document).find(`.mscene.mitem[data-offer=${listKey[i]}] .user-status-icon:not(.offline)`).addClass('offline')
                    }
                }
            }
        },
        fail: function (data) {
            // toastr.error(gettext('Something went wrong!'));
        },
        error: function(xhr, status, error) {
            // var err = eval("(" + xhr.responseText + ")")
            // alert(err.message);
        },
        complete: function () {
        }
    });
}

function check_user_online_func() {
    setTimeout(() => {
        let project_id = $(document).find('.project-item').attr('data-project-id');
        if(!project_id || !$(document).find(`.mscene.mitem`).length || !$(document).find('.pbanner-tab-message.active').length) {
            return check_user_online_func();
        }
        if(window.location.href.toString().includes("tab=messenger") || $(document).find('.pbanner-tab-message.active').length){
            getOnlineStatusUser(project_id);
            setInterval(() => {
                getOnlineStatusUser(project_id);
            }, 30000);
        }
    }, 1000);
}

$(document).ready(function () {
    registerToastCloseAction();
    projectDetail();
    projectSwitch();
    createChapter();
    projectTree();
    projectFileToggle();
    openModalPauseVideo();
    actionSearch();
    toggleCollapse();
    initSearch();
    setWidthInput($('.mcommment[id="comment-input-1"]'));
    checkWitdhInput();
    acrResults();
    removeOverLayModal();
    openUploadForm();
    // addDirector();
    manageMemberNewVersion();
    if (!window.location.pathname.includes('/scene/')) {
        tooltipShow();
        getbuttonPCPosition();
        getbuttonPCPositionFirst();
    }
    initDraftMessageOnLoaded();
    //actionClickMitem();
    check_user_online_func();

    $(document).on('keypress', function (e) {
        if (e.which == 13 && !$('.maction textarea.mcomment-input-text.mcomment-autoExpand').is(":focus") && ($('.bootbox-edit-accept').length || $('.input-chapter-name-modal').is(":focus"))) {
            e.preventDefault();
            if ($('.bootbox-edit-accept').length) {
                $('.bootbox-edit-accept').trigger('click');
            } else {
                $('.btn-edit-product-scene').trigger('click');
            }
        }
    });
    $('#shareModal').on('hidden.bs.modal', function (e) {
        const element = document.querySelector('.owner-top');
        if (element) {
            element.classList.remove("open-edit")
            element.classList.add("close-edit")
        }
    });
    if (checkSafari()) {
        setTimeout(() => {
            $('.c-icon-attach').addClass('c-icon-attach-safari');
            $('.c-icon-send').addClass('c-icon-send-safari');
        }, 2000);
    }
});

function openUploadForm() {
    $('.project-tab-progress').on('click', '.pd-chapter__add', function () {
        let product_scene_id = $(this).parents('.pd-chapter').attr('data-product-scene-id');
        let name_product_scene = $('#product_scene option[data-value^=' + product_scene_id + ']').attr('data-name');
        $('#modal-upload-scene').find('.modal-dialog__header__text h1').text('シーンの作成');

        $('#modal-upload-scene').modal('show')
        $('#modal-upload-scene').attr('action-type', 'create');
        $('#modal-upload-scene .scene-list-take').empty();
        $('#modal-upload-scene').attr('product-scene-id', product_scene_id);
        $('#modal-upload-scene').attr('product-scene-val', name_product_scene);
    });

    $('.btn-create-product-scene').off('click').on('click', function e() {
        let product_scene_text = $('#create-chapter').find('.input-chapter-name').val();
        let product_id = $('#create-chapter').attr('data-project-id');
        product_scene_text = product_scene_text.trim();
        if (!$(this).hasClass('active') && product_scene_text != '' && product_id) {
            //e.preventDefault();
            $(this).addClass('active');
            $.ajax({
                type: "POST",
                url: '/top/create_product_scene_by_name',
                data:
                    {
                        'product_scene_val': product_scene_text,
                        'product_id': product_id
                    },
                beforeSend: function(xhr, settings) {
                    xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                },
                success: function (data) {
                    $('.modal-container.chapter__modal-container').css('display', 'none');
                    $('.pd-chapter-list').append(data.product_scene_html);
                    $('#product_scene').html(data.datalist_html);
                    dragDropProcessing();
                    let listChapterItem = $('.chapter-item')
                    let chapterHtml = `<li class="filter-item-project chapter-item" data-ps-id="${data.product_scene_id}">${data.product_scene_name}</li>`
                    listChapterItem.last().after(chapterHtml)
                    $('.chapter-item').last().trigger('click')
                },
                error: function() {
                    toastr.error('エラーが発生しました', 'シーン並び替え');
                },
                complete: function () {
                    $('.btn-create-product-scene').removeClass('active');
                }
            })

        }
    })
}

function convertSecondsToTimeWithHours(seconds) {
    if (!isNaN(seconds)) {
        var time;
        time = new Date(seconds * 1000).toISOString().substr(11, 8);

        var time_arr = time.split(':');

        if(time_arr[0][0] == '0') {
            time_arr[0][0] = ''
        }
        time = time_arr[0] + ':' + time_arr[1] + ':' + time_arr[2];

        return time;
    }
}

function resizeScene() {
    const commonHeader = $('.sheader');
    let heightCommonHeader = commonHeader.hasClass('d-block-header') && $('.scene-detail-page').length === 0 ? commonHeader.height() : 0;
    let sceneVideos = $('.scene-type-video');
    let sceneDocuments = $('.scene-type-document');
    let sceneAudios = $('.scene-type-audio');
    let headerPageArea = $('.header-page');
    let heightHeaderPageArea = headerPageArea.length > 0 ? headerPageArea.outerHeight() : 0;
    let hasVariations = $('.has-variations');
    let heightVariationsArea = hasVariations.length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
    let commentArea = $('.pd-comment');
    let blockScene = $('.pd-scene.block-scene-video');
    let heightCommentArea = commentArea.height() + 8;
    let maxWidthViewPort =  blockScene.attr('data-width-viewport');
    let maxHeightViewPort = blockScene.attr('data-height-viewport');
    let maxHeightPreviewSP = percentMaxHeightPreviewSP * maxHeightViewPort;
    if (sceneVideos.length > 0) {
        resizeSceneVideo(sceneVideos, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader);
    }
    if (sceneDocuments.length > 0) {
        resizeDocument(sceneDocuments, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader)
    }
    if (sceneAudios.length > 0) {
        resizeAudio(sceneAudios, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader)
    }
}

function resizeDocument(sceneDocuments, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader) {
    sceneDocuments.each(function (i, scene) {
        let widthPreview = maxWidthViewPort;
        let heightVariations = $('.has-variations').length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
        let newHeightPreview = maxHeightViewPort - (heightVariations + heightHeaderPageArea + heightCommentArea + heightCommonHeader);
        let spaceHeightPreview;
        if (newHeightPreview < maxHeightPreviewSP) {
            spaceHeightPreview = maxHeightPreviewSP;
            heightVariations = maxHeightViewPort - (spaceHeightPreview + heightHeaderPageArea + heightCommentArea + heightCommonHeader);
        } else {
            spaceHeightPreview = maxHeightViewPort - (heightHeaderPageArea + heightCommentArea + heightVariations + heightCommonHeader);
        }
        let heightPreview = spaceHeightPreview
        let sizePreview = checkSizePreview(heightVariations, spaceHeightPreview, widthPreview, heightPreview, maxWidthViewPort);
        let newHeightPreviewScene = sizePreview.heightPreview;
        if (newHeightPreviewScene < maxHeightPreviewSP) {
            newHeightPreviewScene = maxHeightPreviewSP
            heightVariations = maxHeightViewPort - (heightHeaderPageArea + heightCommentArea + newHeightPreviewScene + heightCommonHeader);
        }
        applyStyleSizePreview($(this), sizePreview, heightVariations)
    })
}

function resizeAudio(sceneAudios, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader) {
    sceneAudios.each(function (i, scene) {
        let widthPreview = maxWidthViewPort;
        let heightPreview = $(this).children().children().height();
        if (heightPreview < minHeightAudio){
            heightPreview = minHeightAudio
        }
        let heightVariations = $('.has-variations').length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
        let spaceHeightPreview = heightPreview;
        if (heightVariations > 0) {
            heightVariations = maxHeightViewPort - (spaceHeightPreview + heightHeaderPageArea + heightCommentArea + heightCommonHeader);
        }
        let sizePreview = checkSizePreview(heightVariations, spaceHeightPreview, widthPreview, heightPreview, maxWidthViewPort);
        applyStyleSizePreview($(this), sizePreview, heightVariations)
        $(this).children().children().css({
            'display': 'flex',
            'width': (maxWidthViewPort - paddingHorizontalAudio) + 'px',
        })
    })
}

function resizeSceneVideo(sceneVideos, heightHeaderPageArea, heightVariationsArea, heightCommentArea, maxHeightPreviewSP, maxWidthViewPort, maxHeightViewPort, heightCommonHeader) {
    sceneVideos.each(function (i, scene) {
        let videoScene = $(this).find('video');
        let widthVideo = videoScene.attr('data-width');
        let heightVideo = videoScene.attr('data-height');
        let heightVariations = $('.has-variations').length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
        let newHeightPreview = maxHeightViewPort - (heightVariations + heightHeaderPageArea + heightCommentArea + heightCommonHeader);
        let spaceHeightPreview;
        if (newHeightPreview < maxHeightPreviewSP) {
            spaceHeightPreview = maxHeightPreviewSP;
            heightVariations = maxHeightViewPort - (spaceHeightPreview + heightHeaderPageArea + heightCommentArea + heightCommonHeader);
        } else {
            spaceHeightPreview = maxHeightViewPort - (heightHeaderPageArea + heightCommentArea + heightVariations + heightCommonHeader);
        }
        let sizeVideo = checkSizePreview(heightVariations, spaceHeightPreview, widthVideo, heightVideo, maxWidthViewPort);
        let newHeightVideo = sizeVideo.heightPreview;
        if (newHeightVideo < maxHeightPreviewSP) {
            heightVariations = maxHeightViewPort - (heightHeaderPageArea + heightCommentArea + newHeightVideo + heightCommonHeader);
        }
        $(this).children().css({
            'width': 'auto'
        })
        if (sizeVideo.widthPreview === maxWidthViewPort) {
            $(this).find('video').css({
                'border-radius': 'unset'
            })
        }
        applyStyleSizePreview($(this), sizeVideo, heightVariations)
    })
}

function applyStyleSizePreview(preview, sizePreview, heightVariations) {
    let widthPreview = sizePreview.widthPreview;
    let heightPreview = sizePreview.heightPreview;
    preview.attr({
        'data-preview-height': heightPreview,
        'data-preview-width': widthPreview,
        'data-max-height-variations': heightVariations,
    })
    preview.css({
        'width': widthPreview + 'px',
        'height': heightPreview + 'px',
    })
    preview.parent().css({
        // 'width': widthPreview + 'px',
        'width': '100%',
        'height': heightPreview + 'px',
        'padding-bottom': 0
    })
}

function resizeScenePC() {
    const commonHeader = $('.sheader');
    let heightCommonHeader = commonHeader.hasClass('d-block-header') && $('.scene-detail-page').length === 0 ? commonHeader.height() : 0;
    let sceneVideos = $('.scene-type-video');
    let sceneDocuments = $('.scene-type-document');
    let sceneAudios = $('.scene-type-audio');
    let headerPageArea = $('.header-page');
    let heightHeaderPageArea = headerPageArea.length > 0 ? headerPageArea.outerHeight() : 0;
    let hasVariations = $('.has-variations');
    let heightVariationsArea = hasVariations.length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
    let maxWidthPreview = $('.pd-scene.block-scene-video').attr('data-width-viewport') * percentMaxWidthPreviewPC;
    let heightViewPort = $('.pd-scene.block-scene-video').attr('data-height-viewport');
    let maxHeightPreview = heightViewPort - (totalPaddingVerticalScene + heightCommonHeader);
    if (sceneVideos.length > 0) {
        resizeSceneVideoPC(sceneVideos, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview);
    }
    if (sceneDocuments.length > 0) {
        resizeDocumentPC(sceneDocuments, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview)
    }
    if (sceneAudios.length > 0) {
        resizeAudioPC(sceneAudios, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview)
    }
}

function resizeDocumentPC(sceneDocuments, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview) {
    sceneDocuments.each(function (i, scene) {
        let heightVariations = $('.has-variations').length > 0 ? $('.list-variation').prop('scrollHeight') : 0;
        let widthViewPort = $('.pd-scene.block-scene-video').attr('data-width-viewport');
        let widthViewPortAfterCalc = widthViewPort - (totalPaddingHorizontalScreen + totalPaddingHorizontalPreview);
         if (heightVariations >= maxHeightVariationsPC){
            heightVariations = maxHeightVariationsPC;
        }
        let spaceHeightPreview = maxHeightPreview - (heightVariations + paddingTopVariationList + totalPaddingVerticalScreen + heightHeaderPageArea);
        let sizeDocument = checkSizePreview(heightVariations, spaceHeightPreview, maxWidthPreview, spaceHeightPreview, maxWidthPreview);
        let maxWidthDocument = widthViewPort * percentMaxWidthPreviewPC;
        if (sizeDocument.widthPreview > maxWidthDocument) {
            sizeDocument.widthPreview = maxWidthDocument;
        }
        let widthComment = widthViewPortAfterCalc - sizeDocument.widthPreview;
        applyStyleSizePreviewPC($(this), sizeDocument, heightVariations, widthComment)
    })
}

function resizeAudioPC(sceneAudios, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview) {
    sceneAudios.each(function (i, scene) {
        let blockSceneVideo = $('.pd-scene.block-scene-video');
        let widthViewPort = blockSceneVideo.attr('data-width-viewport');
        let heightViewPort = maxHeightPreview;
        let widthViewPortAfterCalc = widthViewPort - (totalPaddingHorizontalScreen + totalPaddingHorizontalPreview);
        let heightViewPortAfterCalc = heightViewPort - (totalPaddingVerticalScreen + totalPaddingVerticalPreview);
        let heightVariations = maxHeightVariationsPC;
        if (heightVariations > maxHeightVariationsPC){
            heightVariations = maxHeightVariationsPC;
        }
        let heightPreview = heightViewPortAfterCalc - (heightVariations + paddingTopVariationList + heightHeaderPageArea);
        let maxWidthAudio = widthViewPort * percentMaxWidthPreviewPC;
        let widthComment = widthViewPortAfterCalc - maxWidthAudio;
        let sizeAudio = {
            'widthPreview': maxWidthAudio,
            'heightPreview': heightPreview
        }
        applyStyleSizePreviewPC($(this), sizeAudio, heightVariations, widthComment)
        $(this).children().children().css({
            'display': 'flex',
            'width': (maxWidthAudio - paddingHorizontalAudio) + 'px',
        })
    })
}

function resizeSceneVideoPC(sceneVideos, heightHeaderPageArea, heightVariationsArea, maxWidthPreview, maxHeightPreview) {
    let widthCommentArea = $('.pd-comment').width();
    const blockSceneVideo  = $('.pd-scene.block-scene-video');
    sceneVideos.each(function (i, scene) {
        let videoScene = $(this).find('video');
        let widthVideo = videoScene.attr('data-width');
        let heightVideo = videoScene.attr('data-height');
        let heightViewPort = maxHeightPreview - (totalPaddingVerticalScreen + totalPaddingVerticalPreview);
        let widthViewPort = blockSceneVideo.attr('data-width-viewport') - (totalPaddingHorizontalScreen + totalPaddingHorizontalPreview);
        if (heightVariationsArea >= maxHeightVariationsPC){
            heightVariationsArea = maxHeightVariationsPC;
        }
        let spaceHeightPreview = heightViewPort - (heightVariationsArea + heightHeaderPageArea + paddingTopVariationList);
        let sizeVideo = checkSizePreview(heightVariationsArea, spaceHeightPreview, widthVideo, heightVideo, maxWidthPreview);
        let paddingHozScreen = 0;
        if (sizeVideo.widthPreview < maxWidthPreview){
            if (widthCommentArea >= maxWidthCommentAreaPC){
                widthCommentArea = maxWidthCommentAreaPC;
                paddingHozScreen = ((widthViewPort + totalPaddingHorizontalScreen + halfPaddingHorizontalScreen) - (widthCommentArea + sizeVideo.widthPreview)) / 2;
            }else {
                let spaceCommentHasUsage = widthViewPort - (sizeVideo.widthPreview + widthCommentArea);
                let widthCommentNeeded  = maxWidthCommentAreaPC - widthCommentArea;
                if (spaceCommentHasUsage > widthCommentArea){
                    widthCommentArea = maxWidthCommentAreaPC;
                    spaceCommentHasUsage = (widthViewPort) - (sizeVideo.widthPreview + widthCommentArea);
                    paddingHozScreen = (totalPaddingHorizontalScreen + halfPaddingHorizontalScreen + spaceCommentHasUsage) / 2;
                }else {
                   if (spaceCommentHasUsage > widthCommentNeeded){
                       widthCommentArea += widthCommentNeeded;
                       paddingHozScreen = (widthViewPort + totalPaddingHorizontalScreen + halfPaddingHorizontalScreen - (widthCommentArea + sizeVideo.widthPreview)) / 2;
                   }else {
                       widthCommentArea += spaceCommentHasUsage;
                       paddingHozScreen = 0;
                   }
                }
            }
        }else {
             widthCommentArea = widthViewPort - sizeVideo.widthPreview;
             paddingHozScreen = 0;
        }
        let paddingHorizontalScreen = totalPaddingHorizontalScreen / 2;
        if (paddingHozScreen > 0 && paddingHozScreen < paddingHorizontalScreen){
            let paddingNeeded = paddingHorizontalScreen - paddingHozScreen;
            widthCommentArea -= widthCommentArea - paddingNeeded;
            paddingHozScreen = paddingHorizontalScreen
        }
            $(this).find('video').css({
                'width': `100%`,
                'height': `100%`,
            })
        applyStyleSizePreviewPC($(this), sizeVideo, heightVariationsArea, widthCommentArea, paddingHozScreen)
    })
}

function applyStyleSizePreviewPC(preview, sizePreview, heightVariations, widthCommentArea = 0, paddingHozScreen = 0) {
    let widthPreview = sizePreview.widthPreview;
    let heightPreview = sizePreview.heightPreview;
    preview.attr({
        'data-preview-height': heightPreview,
        'data-preview-width': widthPreview,
        'data-max-height-variations': heightVariations,
        'data-width-comment': widthCommentArea,
        'data-padding-hoz-screen': paddingHozScreen,
    })
    preview.css({
        'width': widthPreview + 'px',
        'height': heightPreview + 'px',
    })
    preview.parent().css({
        'width': widthPreview + 'px',
        'height': heightPreview + 'px',
        'padding-bottom': 0
    })
    preview.parent().parent().parent().css({
        'height': heightPreview + 'px',
    })
}

function checkSizePreview(heightVariations, spaceHeightPreview, widthPreview, heightPreview, maxWidthViewPort) {
    let aspectRatioVideo = widthPreview / heightPreview;
    if (widthPreview / maxWidthViewPort > heightPreview / spaceHeightPreview) {
        widthPreview = maxWidthViewPort;
        heightPreview = widthPreview / aspectRatioVideo;
    } else {
        heightPreview = spaceHeightPreview;
        widthPreview = spaceHeightPreview * aspectRatioVideo;
    }
    return {
        'widthPreview': widthPreview,
        'heightPreview': heightPreview
    };
}

$(document).on('touchstart touchmove', function (event) {
    if (event.type === "touchmove" || event.type === "touchstart") {
        setPositionTooltipVariationTouchmove();
    }
});

function setPositionLineHeader(el) {
    const heightLineHeader = el.height();
    const positionLineHeader = $('.block-content-scene').position().top - heightLineHeader;
    el.css({
        'position': 'absolute',
        'left': 0,
        'top': positionLineHeader
    })
}

function setPositionTooltipVariationTouchmove() {
    if ($('.has-variations').length > 0) {
        let listVariation = $('.list-variation');
        let listToolTip = $('.variation-button-container');
        listToolTip.each(function (i, el) {
            calculatePositionTooltipVariation($(this), $(this).find('.variation-button-name-tooltip-container')[0]);
            $(this).find('.variation-button-name-tooltip-container').css({
                'transform': ''
            })
        })
        checkPositionTooltipVariation(listToolTip, listVariation);
    }
}

function hoverDropdownMessage() {
    let btnReplyMsg = $('.prdt .li-reply-message');
    let btnEditMsg = $('.prdt .li-edit-message');
    let btnResolveMsg = $('.prdt .li-resolve-message');
    btnReplyMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-reply .txt-reply-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-reply .img-reply-comment')[0].style.background = "url('/static/images/scene-detail/icon-reply-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-reply .txt-reply-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-reply .img-reply-comment')[0].style.background = "url('/static/images/scene-detail/icon-reply.svg')";
    });

    btnEditMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-edit .txt-edit-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-edit .img-edit-comment')[0].style.background = "url('/static/images/scene-detail/icon-edit-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-edit .txt-edit-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-edit .img-edit-comment')[0].style.background = "url('/static/images/scene-detail/icon-edit.svg')";
    });

    btnResolveMsg.hover(function (el) {
        el.currentTarget.querySelectorAll('.mmessage-resolve .txt-item-comment')[0].style.color = '#009ACE';
        el.currentTarget.querySelectorAll('.mmessage-resolve .img-resolve-comment')[0].style.background = "url('/static/images/scene-detail/icon-resolve-active.svg')";
    }, function (el2) {
        el2.currentTarget.querySelectorAll('.mmessage-resolve .txt-item-comment')[0].style.color = '#000';
        el2.currentTarget.querySelectorAll('.mmessage-resolve .img-resolve-comment')[0].style.background = "url('/static/images/scene-detail/icon-resolve.svg')";
    });
}

function hoverBtnActionMessage() {
    let listBtnAction = $('.dropdown.dropdown-comment-new').parent().parent();
    listBtnAction.hover(function (el) {
        $(this).find('.show-more-action-message:not(.show-action)').css('display', 'flex')
    }, function (el2) {
        $(this).find('.show-more-action-message:not(.show-action)').css('display', 'none')
    })
}

function clickBtnActionMessage() {
    $('.mmessage.clicked').on('click', function (event) {
        var positionMsgAct = 0;
        var positionMsgActPrnt = 0;
        if ($(this).find('.show-more-action-message').hasClass('show-action')) {
            positionMsgAct = $(this).find('.show-more-action-message').offset().top || 0;
            positionMsgActPrnt = $(this).find('.show-more-action-message').parent().offset().top || 0;
            $(this).find('.show-more-action-message').removeClass('show-action');
        } else {
            $(this).find('.show-more-action-message').addClass('show-action');
            positionMsgAct = $(this).find('.show-more-action-message').offset().top || 0;
            positionMsgActPrnt = $(this).find('.show-more-action-message').parent().offset().top || 0;
        }
        var dropdownMenuComment = $('.dropdown-menu-comment')
        var windowHeight = $(window).height();
        var elementHeight = $(this).outerHeight();
        var elementOffset = $(this).offset().top;
        var bottomThreshold = windowHeight - (windowHeight * 0.33); 

        if ((elementOffset + elementHeight) >= bottomThreshold) {        
            if (positionMsgAct.top - positionMsgActPrnt.top < dropdownMenuComment.height()) {
                dropdownMenuComment.css('top', `-${dropdownMenuComment.height()}px`);
            } else {
                dropdownMenuComment.css('top', `${positionMsgAct - positionMsgActPrnt - dropdownMenuComment.height() - 2}px`);
            }
        } else {
            dropdownMenuComment.css('top', '');
        }
    })
}

function isToolbarHidden() {
    // Calculate the difference between the viewport height and the window height
    var heightDifference = window.outerHeight - window.innerHeight;

    // If the height difference is greater than 0, it indicates the toolbar is hidden
    return heightDifference > 0;
}

$(document).ready(function () {
    let leftSidebar = $('#left-sidebar');
    let leftSidebarHeight = leftSidebar.outerHeight();
    let navigationBarBottom = $('.block-navigation-bar');
    var lastScrollTop = 0;
    let headerGlobal = $('.sheader')
    let topAppBarBanner = $('.new-banner-project')
    let navTopAppBar = $('.navigation-top-app-bar')
    let budgetLog = $('#budgetLogSidebar')
    let projectItemDetail = $('#projectItemDetail')
    let heightHeaderGlobal = headerGlobal.outerHeight();
    let heightTopAppBarBanner = topAppBarBanner.outerHeight();
    let heightNavTopAppBar = navTopAppBar.outerHeight();
    let heightNavigationBarBottom = 0;
    if (navigationBarBottom.length > 0 && !navigationBarBottom.hasClass('hide')) {
        heightNavigationBarBottom = navigationBarBottom.outerHeight();
    }
    let totalHeightBannerHeader = heightHeaderGlobal + heightTopAppBarBanner;
    let totalHeightTopWithoutHeader = heightTopAppBarBanner + heightNavTopAppBar;
    let totalHeightTop = heightHeaderGlobal + heightTopAppBarBanner + heightNavTopAppBar;
    let newHeightLeftSidebar = leftSidebarHeight - totalHeightBannerHeader;
    leftSidebar.css('height', `${newHeightLeftSidebar}px`);
    leftSidebar.css('border-bottom', `1px solid #f0f0f0`);
    $(document).on('click', '.pbanner-tabs .pbanner-tab', function (e) {
        closeNav();
        let inputComment = $('.prdt .mcomment-input textarea');
        if (inputComment.length > 0) {
            resetInputMessage($(document));
        }
        $(window).scrollTop(0);
    })
    const marginTopSpace = '40px';
    topAppBarBanner.css('transition', 'top 0.2s')
    headerGlobal.css('transition', 'top 0.2s')
    navigationBarBottom.css('transition', 'bottom 0.2s')
    leftSidebar.css('transition', 'bottom 0.2s')
    leftSidebar.css('transition', 'top 0.2s')
    budgetLog.css('transition', 'top 0.2s')
    $(window).scroll(function (event) {
        let st = $(this).scrollTop();
        if (st > lastScrollTop) {
                console.log('scroll page up')
                topAppBarBanner.css('top', '0')
                navigationBarBottom.css('bottom', `-${heightNavigationBarBottom}px`)
                navTopAppBar.css('top', `${heightTopAppBarBanner}px`);
                leftSidebar.css('top', `${heightTopAppBarBanner}px`);
                budgetLog.css('top', `${heightTopAppBarBanner}px`);
                if (!$('.pbanner-tab--exchange.active').length > 0 && !$('.pbanner-tab-message.active').length > 0) {
                    projectItemDetail.css('top', `${totalHeightTopWithoutHeader}px`);
                }
                headerGlobal.css('top', `-${heightHeaderGlobal}px`);
                if (isToolbarHidden()) {
                    leftSidebar.css('height', `${leftSidebarHeight - 100}px`)
                } else {
                    leftSidebar.css('height', `${newHeightLeftSidebar}px`)
                }
                $('.column-list-offer').css('margin-top', marginTopSpace)
                // $('.mcolumn--right').css('margin-top', marginTopSpace)
        } else {
                console.log('scroll page down')
                headerGlobal.css('top', '0')
                topAppBarBanner.css('top', `${heightHeaderGlobal}px`)
                navigationBarBottom.css('bottom', '0')
                navTopAppBar.css('top', `${totalHeightBannerHeader}px`);
                leftSidebar.css('top', `${totalHeightBannerHeader}px`);
                budgetLog.css('top', `${totalHeightBannerHeader}px`);
                if (!$('.pbanner-tab--exchange.active').length > 0 && !$('.pbanner-tab-message.active').length > 0) {
                    projectItemDetail.css('top', `${totalHeightTop}px`);
                }
                $('.column-list-offer').css('margin-top', '0')
                // $('.mcolumn--right').css('margin-top', '0')
                if (isToolbarHidden()) {
                    leftSidebar.css('height', `${newHeightLeftSidebar - 100}px`)
                } else {
                    leftSidebar.css('height', `${newHeightLeftSidebar}px`)
                }
        }
        setTimeout(function () {
            calcHeightCalendarModal();
        }, 300)
        lastScrollTop = st;
    });
})

function registerToastCloseAction() {
    toastr.options = {
        closeButton: false,
        debug: false,
        newestOnTop: false,
        progressBar: false,
        positionClass: "toast-bottom-center",
        preventDuplicates: true,
        onclick: null,
        timeOut: 5000,
        extendedTimeOut: 5000,
        showEasing: "swing",
        hideEasing: "linear",
        tapToDismiss: false,
    };

    $(document).on('click', '#toast-container .c-btn-tertiary', function() {
        $('.toast').remove()
    });

    $(document).on('click', '#toast-container #acceptance-button-action', function() {
        $('.toast').remove()
        $(".link-nav-item.pbanner-tab.pbanner-tab-message").click()
        setTimeout(function () {
            $("#modal-confirm-done-offer").modal("show")
        }, 1000)
    });

    $(document).on('click', '#toast-container #close-button-action', function() {
        $('.toast').remove()
        $.ajax({
            type: "POST",
            url: '/close_charged_product',
            data:
                {
                    'product_id': $(document).find('.project-item.active').attr('data-project-id')
                },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                toastr.info('Success')
            },
            error: function () {
                toastr.error('エラーが発生しました', 'シーン並び替え');
            }
        })
    })

    $(document).on('click', '#toast-container #calendar_button', function() {
        $('.toast').remove();
        $('.calendar-schedule').click();
    })

}
function showToastCloseButton() {
    let projectDataElement = $('.project-item')
    let doneSceneCount = parseInt(projectDataElement.attr('data-done-scene'));
    let maxScene = parseInt(projectDataElement.attr("data-max-scene"));
    let endTime = projectDataElement.attr("data-project-endtime")
    let mileStoneName = projectDataElement.attr("data-milestone-name")
    let mileStoneTime = projectDataElement.attr("data-milestone-time")


    const rate = Math.round((doneSceneCount / maxScene) * 100);
    toastr.options = {
        showDuration: 300,
        hideDuration: 500,
        closeButton: false,
        debug: false,
        newestOnTop: false,
        progressBar: false,
        positionClass: "toast-bottom-center",
        preventDuplicates: true,
        onclick: null,
        timeOut: 1000,
        extendedTimeOut: 3000,
        hideEasing: "linear",
        showEasing: 'swing',
        tapToDismiss: false,
    };

    if(isShowClosePopup) {
        return;
    }
    var remainingDays;
    var headerText = '';
    let today = moment().startOf('day')
    if(mileStoneTime) {
       remainingDays = moment(mileStoneTime).diff(today, 'days');
       headerText = `${mileStoneName||'マイルストーン'}まであと${remainingDays} 日`;
    } else if(today.isBefore(moment(endTime))) {
        remainingDays = moment(endTime).diff(today, 'days');
        headerText = `納期まであと${remainingDays}日`
    }
    let progressContent = `<div>
    <div class="u-row-between heading">${headerText}<span class="material-symbols-rounded" id="calendar_button">event_note</span></div>
    <progress value="${rate}" max="100"></progress>
    <div class="u-row u-gap16"><span class="bodytext-11">${rate}%</span><span class="bodytext-11">（${doneSceneCount}/${maxScene}）</span></div>
    </div>`;

    let producerContent = `<div>
      <p>このプロジェクトを完了しますか？</p>
      <p>全てのオファーを検収済みとし、余った予算をウィジェット残高へ払い戻します。</p>
      <hr>
      <div class="u-row u-justify-end u-gap16">
      <button class="c-btn-tertiary">キャンセル</button>
      <button class="c-btn-primary" id="close-button-action">完了</button>
      </div>
    </div>`;

    let masterClientContent = `<div>
      <p>おめでとうございます。</p>
      <p>すべてのチェックが完了しました。</p>
      <p>このプロジェクトを検収しますか？</p>
      <hr>
      <div class="u-row u-justify-end u-gap16">
      <button class="c-btn-tertiary">キャンセル</button>
      <button class="c-btn-primary" id="acceptance-button-action">検収</button>
      </div>
    </div>`;

    if((user_role === 'master_client' || project_position === 'owner' || project_position === 'producer') && maxScene !== doneSceneCount) {
        toastr.info(progressContent, '')
        isShowClosePopup = true;
    } else if (project_position === 'owner' && canBeDone) {
        toastr.info(masterClientContent, '')
        isShowClosePopup = true;
    } else if (project_position === 'producer' && isOwner && canBeDone) {
        toastr.info(producerContent, '')
        isShowClosePopup = true;
    }
}

function manageMemberNewVersion() {
    $('.project-list').on('click', '.sproject__user-btn, .pbanner__user-list, .pbanner__admin-list', function (e) {
        e.preventDefault();
        let projectId;
        let $modal_manage = $('#modal-member-manage');
        if ($(this).hasClass('sproject__user-btn')) {
            projectId = $(this).parents('.sprojects-item').attr('data-project');
        } else {
            projectId = $(this).parents('.project-item').attr('data-project-id');
        }
        // if ($(this).hasClass('pbanner__admin-list')) {
        //     $modal_manage.find('.member-manage__invite').addClass('hide');

            $.ajax({
                type: "GET",
                data: {projectId: projectId},
                url: "/top/project_admin_new",
                success: function (data) {
                   $('#modalUsersInProject').html(data.html);
                $('.member-manage__content').attr('data-product', data.product_id);
                    draggableMember();
                },
                error: function () {
                   $('.modalUsersInProject').html('');
                $('.member-manage__content').attr('data-product', '');
                }
            });
        // } else {
            // if ($(e.target).hasClass('pbanner__user-btn')) {
            //     return;
            // } else {
                // if ($(this).hasClass('pbanner__user-list') && $(this).find('.pbanner__user-btn').length) {
                //     $modal_manage.find('.member-manage__invite').removeClass('hide');
                // } else {
                //     $modal_manage.find('.member-manage__invite').addClass('hide');
                // }

                $.ajax({
                    type: "GET",
                    data: {projectId: projectId},
                    url: "/top/project_member_list",
                    success: function (data) {
                        $modal_manage.find('.member-manage__content').html(data.html);
                        $modal_manage.find('.member-manage__content').attr('data-product', data.product_id);
                        selectAction();
                        inviteUser();
                        draggableOwner();
                    },
                    error: function () {
                        $modal_manage.find('.member-manage__content').html('');
                        $modal_manage.find('.member-manage__content').attr('data-product', '');
                    }
                });
            // }
        // }
    });

    $(document).on('mouseenter mouseleave', '.member-manage__list-producer .member-item-container, .member-manage__list-director .member-item-container, .list-project-owner .member-item-container, .list-project-member .member-item-container', function (e) {
        if (!checkShowButtonProjectManage($(this))) {
            return
        }
        if (window.innerWidth > max_width_sp_device) {
            $(this).find('.member-item-action__button-container').toggleClass('show-button-action', e.type === 'mouseenter');
        }
    })

    $(document).on('click', '.member-manage__list-producer .member-item-container, .member-manage__list-director .member-item-container, .list-project-owner .member-item-container, .list-project-member .member-item-container', function (e) {
        if (!checkShowButtonProjectManage($(this))) {
            return
        }
        if($(this).find('.member-item-action__button-container').hasClass('show-button-action') && window.innerWidth < max_width_sp_device){
            $(this).find('.member-item-action__button-container').removeClass('show-button-action');
        } else if(!$(this).find('.member-item-action__button-container').hasClass('show-button-action') && window.innerWidth < max_width_sp_device) {
            $(this).find('.member-item-action__button-container').addClass('show-button-action');
        }
    })
}


function resizeCommentInput() {
    if ($(window).width() > max_width_sp_device) {
    setTimeout(function () {
            let main_block = $('.pd-section__content.main-talk-room')
        if (main_block.length < 1) {
            main_block = $('.offer-content-message')
        }
        if (main_block.length > 0) {
            let width_main_block = main_block.width();
            let left_main_block = main_block.offset().left;
            let footer_comment_block = $('.footer-comment-block');
            let width_footer_comment_block = footer_comment_block.width();
            let fixedBlockLeft = left_main_block + (width_main_block - width_footer_comment_block) / 2;
            footer_comment_block.css({
                'width': width_main_block + 'px',
                'left': fixedBlockLeft + 'px'
            });
        }
    })
    }
}

function calcHeightCalendarModal() {
    let header_global = $('.sheader');
    let sc_block = $('.sc-block');
    let sc_body = $('.sc-body');
    let height_header_global = header_global.outerHeight();
    let top_header_global = parseFloat(header_global.css('top'));
    let margin_top_sc_block = parseFloat(sc_block.css('padding-top'));
    const margin_top_modal = 8;
    if (top_header_global < 0) {
        // sc_block.css('height', `calc(100dvh - ${margin_top_modal}px)`)
        // sc_body.css('max-height', `calc(100dvh - ${margin_top_modal + margin_top_sc_block}px)`)
    } else {
        // sc_block.css('height', `calc(100dvh - ${height_header_global + margin_top_modal}px)`)
        // sc_body.css('max-height', `calc(100dvh - ${height_header_global + margin_top_modal + margin_top_sc_block}px)`)
    }
}

// function scrollListComment(el) {
//     let navigationBar = $('.block-navigation-bar');
//     let footerCommentBlockOffer = $('#footerCommentBlockOffer');
//     if (navigationBar.length > 0 && navigationBar.hasClass('hide') && footerCommentBlockOffer.length > 0) {
//         footerCommentBlockOffer.css('bottom', 0)
//     }
//     var lastScrollTop = 0;
//     let headerGlobal = $('.sheader')
//     let topAppBar = $('.new-banner-project')
//     let navTopAppBar = $('.navigation-top-app-bar')
//     let leftSidebar = $('#left-sidebar')
//     let budgetLog = $('#budgetLogSidebar')
//     let projectItemDetail = $('#projectItemDetail')
//     let footerComment = $('.footer-comment-block')
//     let btnAddOffer = $('.action-add-offer')
//     let mmessage_component = $('.mmessage-component')
//     let pdSection_file = $('.pd-section-file')
//     topAppBar.css('transition', 'top 0.2s')
//     headerGlobal.css('transition', 'top 0.2s')
//     navigationBar.css('transition', 'bottom 0.2s')
//     leftSidebar.css('transition', 'bottom 0.2s')
//     leftSidebar.css('transition', 'top 0.2s')
//     budgetLog.css('transition', 'top 0.2s')
//     let height_project_banner = topAppBar.outerHeight();
//     let height_sheader = headerGlobal.outerHeight();
//     let height_nav_top_app_bar = 0;
//     let height_bottom_nav_bar = navigationBar.outerHeight();
//     let height_footer_comment = footerComment.outerHeight();
//     let height_navbar_bottom = navigationBar.outerHeight();
//     let content_message = $('.project-tab.project-tab-messenger.active')
//     if (navTopAppBar.length > 0) {
//         height_nav_top_app_bar = navTopAppBar.outerHeight();
//     }
//     el.scroll(function (event) {
//         const {scrollHeight, scrollTop, clientHeight} = event.target;
//         event.preventDefault()
//         let st = $(this).scrollTop();
//         if (st > lastScrollTop) {
//             console.log('Scroll xuống message list');
//             headerGlobal.css('top', '-65px');
//             topAppBar.css('top', '0')
//             navigationBar.css('bottom', '-80px')
//             navTopAppBar.css('top', '75px');
//             leftSidebar.css('top', '75px');
//             footerComment.css('bottom', '0');
//             btnAddOffer.css('bottom', '0');
//             budgetLog.css('top', '74px');
//             projectItemDetail.css('top', `${height_project_banner}px`);
//             $('.column-list-offer').css('margin-top', '40px')
//             $('.mcolumn--right').css('margin-top', '40px')
//             content_message.css('top', '75px');
//             pdSection_file.css({
//                 'height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar}px)`,
//                 'max-height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar}px)`,
//             })
//             // calcInitHeightListComment()
//         } else {
//             console.log('Scroll len message list');
//             headerGlobal.css('top', '0')
//             topAppBar.css('top', '64px')
//             navigationBar.css('bottom', '0')
//             navTopAppBar.css('top', '139px');
//             leftSidebar.css('top', '140px');
//             btnAddOffer.css('bottom', '80px');
//             budgetLog.css('top', '140px');
//             if ($(window).width() < max_width_sp_device) {
//                 footerComment.css('bottom', `${height_footer_comment}px`)
//                 content_message.css('top', `${height_project_banner}px`);
//             } else {
//                 footerComment.css('bottom', '80px');
//                 content_message.css('top', '139px');
//             }
//             $('.prdt .mcolumn--right').css('margin-top', '40px')
//             $('.prdt .column-list-offer').css('margin-top', '40px')
//             pdSection_file.css({
//                 'height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar + height_bottom_nav_bar + height_sheader}px)`,
//                 'max-height': `calc(100vh - ${height_project_banner + height_nav_top_app_bar + height_bottom_nav_bar + height_sheader}px)`,
//             })
//             if (st === 0) {
//                 if ($(window).width() < max_width_sp_device) {
//                     footerComment.css('bottom', `${height_footer_comment + height_navbar_bottom}px`)
//                     content_message.css('top', `${height_sheader + height_project_banner}px`);
//                 } else {
//                     footerComment.css('bottom', '0')
//                     content_message.css('top', '179px');
//                 }
//                 $('.column-list-offer').css('margin-top', '0')
//                 $('.mcolumn--right').css('margin-top', '0')
//                 projectItemDetail.css('top', `${height_project_banner + height_sheader + height_nav_top_app_bar}px`);
//             } else {
//                 footerComment.css('bottom', '0')
//                 projectItemDetail.css('top', `${height_project_banner + height_sheader}px`);
//             }
//         }
//         if ($(window).width() > max_width_sp_device && $(window).width() < max_width_tablet_device) {
//             setTimeout(function () {
//                 calcHeightColumnRight()
//             }, 230)
//         }
//         // scroll max bottom
//         lastScrollTop = st;
//     });
// }
