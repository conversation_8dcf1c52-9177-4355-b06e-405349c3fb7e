import CommentBlock from "../components/block/CommentBlock.js";
import CommentReceivedContainer, { CommentReceivedBlock } from "../components/block/CommentReceivedBlock.js";
import CommentSentBlockContainer, { CommentSentBlock } from "../components/block/CommentSentBlock.js";
import FileItemBlock from "../components/block/FileItemBlock.js";
import ParentCommnetBlock from "../components/block/ParentCommentBlock.js";
import UserSeenMessage from "../components/block/UserSeenMessage.js";
import UserDownloadedAvatar from "../components/minor-block/file-info/UserDownLoadedAvatar.js";

let list_temp_folder_name = [];
let list_temp_folder_id = {};
let is_loading_product_comment = false;
let total_product_comment;
let current_load_product_comment = 0;
let prevProductID;
var wavesurfer_arr = [];
let is_pc_device = false;
export let dict_chatsocket = new Map();
let user_role;
export const dayOfWeek =  ["Sun", "Mon", "Tu<PERSON>", "Wed", "Thu", "Fri", "Sat"];

export const reloadLinkAudio = () => {
    let list_audio = $(".pd-scene").find(".s-audio");
    list_audio.each((index, audio) => {
        let wave_index = $(audio).attr("data-wavesurfer");
        let audio_source = $(audio).find(".s-audio-source");
        let wave_scene =  wavesurfer_arr[parseInt(wave_index)];
        let link = audio_source.attr("data-link");
        let peaks_string = audio_source.attr("data-peaks-loaded");
        if (!peaks_string){
            return;
        }
        if($(audio).parents(".slick-active").length){
            if ($('.cscene__version-horizontal').hasClass('active')) {
                let small_peaks = audio_source.attr("data-small-peaks");
                let array_small_peaks = small_peaks.split(" ").map(Number);
                wave_scene.load(link, array_small_peaks, 'none');
                wave_scene.loaded = false;
            } else {
                let array_peaks = peaks_string.split(" ").map(Number);
                wave_scene.load(link, array_peaks, 'none');
                wave_scene.loaded = false;
            }
        }
    });
}

export const parseRequestUrl = () => {
    const address = document.location.hash.slice(1).split("?")[0];
    const queryString =
        document.location.hash.slice(1).split("?").length === 2
            ? document.location.hash.slice(1).split("?")[1]
            : "";

    const url = address.toLowerCase() || "/";
    const r = url.split("/");
    const q = queryString.split("=");
    return {
        resource: r[1],
        id: r[2],
        verb: r[3],
        name: q[0],
        value: q[1],
    };
};

export const rerender = async (component) => {
    document.getElementById("main-container").innerHTML =
        await component.render();
    await component.after_render();
};

export const showLoading = () => {
    document.getElementById("loading-overlay").classList.add("active");
};

export const hideLoading = () => {
    document.getElementById("loading-overlay").classList.remove("active");
};
export const showMessage = (message, callback) => {
    document.getElementById("message-overlay").innerHTML = `
	<div>
		<div id="message-overlay-content">${message}</div>
		<button id="message-overlay-close-button">OK</button>
	</div>
	`;
    document.getElementById("message-overlay").classList.add("active");
    document
        .getElementById("message-overlay-close-button")
        .addEventListener("click", () => {
            document
                .getElementById("message-overlay")
                .classList.remove("active");
            if (callback) {
                callback();
            }
        });
};

export const redirectUser = () => {
    if (getCartItems().length !== 0) {
        document.location.hash = "/shipping";
    } else {
        document.location.hash = "/";
    }
};

export const seenComment = (type) => {
    let scene_title_id;
    let product_id;
    let data;
    if ($(".mmessage-list").hasClass("not-seen")) {
        if (type === "scene") {
            scene_title_id = $(".pd-scene-title-detail").attr("data-scene-title-id");
            data = {
                scene_title_id: scene_title_id,
                comment_type: "scene",
            };
        } else {
            product_id = $(".project-item.active").attr("data-project-id");
            data = {
                product_id: product_id,
                comment_type: "project",
            };
        }
        
        if (scene_title_id || product_id) {
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/api/seen_comment",
                data: data,
                success: function () {
                    // console.log('update seen successful');
                    $('.mmessage-list').removeClass('not-seen');
                }
            })
        }
    }
};

export const autoLoadMore = ({oldData}) => {
    if(oldData){
        let countCurrentProductComment =
            current_load_product_comment === 0
                ? MESSAGE_PER_LOAD
                : current_load_product_comment * MESSAGE_PER_LOAD;
        if (
            $(".mmessage").length - $(".mmessage .mmessage-resolved").length <
                countCurrentProductComment &&
            $(".project-tab.project-tab-product-comment").hasClass("active")
        ) {
            if (
                current_load_product_comment < total_product_comment &&
                !is_loading_product_comment
            ) {
                if(oldData){
                    ajaxLoadMoreProductComment({oldData});
                }
            }
        }
    }
};

export const initmCustomScrollbar = () => {
    $(".custom-scrollbar.custom-scrollbar--vertical").mCustomScrollbar({
        theme: "minimal-dark",
        axis: "y",
    });

    $(".custom-scrollbar.custom-scrollbar--horizontal").mCustomScrollbar({
        theme: "minimal-dark",
        axis: "x",
    });

    setTimeout(function () {
        if ($(".custom-scrollbar--bottom").length) {
            $(".custom-scrollbar--bottom").mCustomScrollbar(
                "scrollTo",
                "bottom",
                {
                    scrollEasing: "easeOut",
                }
            );
        }
    }, 500);
};

export const scrollListComment = (el) => {
    let navigationBar = $(".block-navigation-bar");

    let lastScrollTop = 0;
    let headerGlobal = $(".sheader");
    let topAppBar = $(".new-banner-project");
    let navTopAppBar = $(".navigation-top-app-bar");
    let leftSidebar = $("#left-sidebar");
    let budgetLog = $("#budgetLogSidebar");
    let pdSection_file = $(".pd-section-file");
    const space_margin_top = "40px";
    const space_margin_top_small = "2px";
    const height_input_box = 85;

    leftSidebar.css("transition", "bottom 0.2s");
    leftSidebar.css("transition", "top 0.2s");
    budgetLog.css("transition", "top 0.2s");
    let height_project_banner = topAppBar.outerHeight();
    let height_sheader = headerGlobal.outerHeight();
    let height_nav_top_app_bar = 0;
    let height_block_nav_bottom_bar = 0;
    let first_scroll_up = true;
    let first_scroll_down = true;
    if (navigationBar.length > 0 && !navigationBar.hasClass("hide")) {
        height_block_nav_bottom_bar = navigationBar.outerHeight();
    }
    if (navTopAppBar.length > 0) {
        height_nav_top_app_bar = navTopAppBar.outerHeight();
    }
    let total_height_banner_header = height_sheader + height_project_banner;
    let timeStamp_last_scroll = 0;
    el.scroll(function (e) {
        e.preventDefault();
        let st = $(this).scrollTop();
        let timeStamp_scroll = e.timeStamp;

        if (
            st > lastScrollTop &&
            lastScrollTop > 0 &&
            e.timeStamp - lastTimeScroll > 200 &&
            should_scroll
        ) {
            if (first_scroll_down) {
                first_scroll_down = false;
                first_scroll_up = true;
                lastTimeScroll = e.timeStamp;
                //setPositionTopAndBottomMenu(true)

                leftSidebar.css("top", `${height_project_banner}px`);
                $(".mmessage-component").removeClass(
                    "max-height-mmessage-component-up"
                );
                if (
                    !$(".mmessage-component").hasClass(
                        "max-height-mmessage-component-down"
                    )
                ) {
                    $(".mmessage-component").addClass(
                        "max-height-mmessage-component-down"
                    );
                }

                if ($(window).width() < max_width_sp_device) {
                    if (navigationBar.hasClass("hide")) {
                        $(".martist.role_master_client").css("margin-top", "0");
                    }
                }
            }
        } else {
            if (
                lastScrollTop > 0 &&
                e.timeStamp - lastTimeScroll > 200 &&
                should_scroll
            ) {
                first_scroll_up = false;
                first_scroll_down = true;
                lastTimeScroll = e.timeStamp;
                //setPositionTopAndBottomMenu(false)

                leftSidebar.css("top", `${total_height_banner_header}px`);
                $(".mmessage-component").removeClass(
                    "max-height-mmessage-component-down"
                );
                if (
                    !$(".mmessage-component").hasClass(
                        "max-height-mmessage-component-up"
                    )
                ) {
                    $(".mmessage-component").addClass(
                        "max-height-mmessage-component-up"
                    );
                }

                if ($(window).width() < max_width_sp_device) {
                    if (navigationBar.hasClass("hide")) {
                        $(".martist.role_master_client").css("margin-top", "0");
                    }
                }
            }
        }
        setTimeout(function () {
            calcHeightCalendarModal();
        }, 300);
        if (
            $(window).width() > max_width_sp_device &&
            $(window).width() < max_width_tablet_device
        ) {
            setTimeout(function () {
                calcHeightColumnRight();
            }, 230);
        }
        timeStamp_last_scroll = timeStamp_scroll;
        // scroll max bottom
        lastScrollTop = st;
    });
};

export const calcMoreActionComment = (el) => {
    if (el) {
        let listMessage = el;
        const heightShowMoreAction = 10;
        listMessage.each(function () {
            let heightMessageMain = $(this)
                .find(".mmessage-main")
                .outerHeight();
            let messageInfo = $(this).find(".mmessage-info");
            let dropdownCommentNew = $(this).find(".dropdown-comment-new");
            messageInfo.css("height", heightMessageMain);
            let messageInfoContainer = messageInfo.find(
                ".message-info-container"
            );
            if (
                heightMessageMain / 2 - heightShowMoreAction <=
                messageInfoContainer.height()
            ) {
                messageInfoContainer.css("height", "auto");
                dropdownCommentNew.css(
                    "height",
                    `${heightMessageMain - messageInfoContainer.height()}`
                );
                if (
                    heightMessageMain - messageInfoContainer.height() >=
                    heightMessageMain / 2 + heightShowMoreAction / 2
                ) {
                    dropdownCommentNew.css("height", "calc(50% + 5px)");
                    messageInfoContainer.css("height", "calc(50% - 5px)");
                }
            } else {
                dropdownCommentNew.css("height", "calc(50% + 5px)");
                messageInfoContainer.css("height", "calc(50% - 5px)");
            }
        });
    }
};

export const scrollCommentBar = () => {
    let lastScrollTop = 0;
    let footerComment = $(".footer-comment-block");
    let btnAddOffer = $(".action-add-offer");
    let navigationBar = $(".block-navigation-bar");
    let newBannerProject = $(".new-banner-project");
    let sheader = $(".sheader");
    let navigationTopAppBar = $(".navigation-top-app-bar");
    let heightNavigationBar = 0;
    if (navigationBar.length > 0 && !navigationBar.hasClass("hide")) {
        heightNavigationBar = navigationBar.outerHeight();
    }
    let heightNewBannerProject = newBannerProject.outerHeight();
    let heightNavigationTopAppBar = navigationTopAppBar.outerHeight();
    let heightSheader = sheader.outerHeight();
    let totalHeightBannerTopNav =
        heightNewBannerProject + heightNavigationTopAppBar;
    let totalHeightSpaceTop =
        heightNewBannerProject + heightNavigationTopAppBar + heightSheader;
    const space_margin_top = "40px";
    navigationBar.css("transition", "bottom 0.2s");
    footerComment.css("transition", "bottom 0.2s");
    btnAddOffer.css("transition", "bottom 0.2s");
    $(window).scroll(function (event) {
        let messengerContent = $(".prdt .messenger-detail");
        let st = $(this).scrollTop();
        if (st > lastScrollTop) {
            // console.log("Scroll xuống scrollCommentBar");
            messengerContent.css(
                "height",
                `calc(100vh - ${totalHeightBannerTopNav}px)`
            );
            if (navigationBar.length > 0 && !navigationBar.hasClass("hide")) {
                footerComment.css("bottom", "0");
                btnAddOffer.css("bottom", "0");
                $("#mColumnWrap .mcolumn-content").css("height", "82vh");
            }
            navigationBar.css("bottom", `-${heightNavigationBar}px`);
            $(".column-list-offer").css("margin-top", space_margin_top);
            $(".mcolumn--right").css("margin-top", space_margin_top);
        } else {
            // console.log("Scroll lên scrollCommentBar");
            if (navigationBar.length > 0 && !navigationBar.hasClass("hide")) {
                footerComment.css("bottom", `${heightNavigationBar}px`);
                btnAddOffer.css("bottom", `${heightNavigationBar}px`);
                // $('#mColumnWrap .mcolumn-content').css('height', '65vh')
            }
            navigationBar.css("bottom", "0");
            messengerContent.css(
                "height",
                `calc(100vh - ${totalHeightSpaceTop}px)`
            );
            $(".column-list-offer").css("margin-top", "0");
            $(".mcolumn--right").css("margin-top", "0");
        }
        lastScrollTop = st;
    });
};

export const calcPositionDropdownComment2 = () => {
    let listMessageSent = $(".prdt .mmessage--sent");
    let listMessageReceived = $(".prdt .mmessage--received");
    const widthDropdownCmt = 180;
    const paddingDropdown = 34;
    const spaceLeftScreen = 50;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find(".mmessage-main").outerWidth();
        let widthMessageInfo = $(this).find(".mmessage-info").outerWidth();
        let resultLeftDropdown =
            widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        let numberLeftDropdown = 0;
        if (resultLeftDropdown > 0) {
            numberLeftDropdown = -resultLeftDropdown - paddingDropdown;
        }
        let dropdownMenu = $(this).find(
            ".mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment"
        );
        dropdownMenu.css("left", `${numberLeftDropdown}px`);
    });
    const messageListWidth = $(".pd-comment").width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find(".mmessage-main").outerWidth();
        let widthMessageInfo = $(this).find(".mmessage-info").outerWidth();
        let resultLeftDropdown =
            widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find(
                ".mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment"
            );
            let left_dropdown = resultLeftDropdown - paddingDropdown;
            if (left_dropdown > spaceLeftScreen) {
                left_dropdown = spaceLeftScreen;
            }
            dropdownMenu.css("left", `-${left_dropdown}px`);
        } else {
            let resultLongReceivedCmt =
                messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find(
                    ".mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment"
                );
                dropdownMenuReceived.css(
                    "left",
                    `-${widthDropdownCmt - resultLongReceivedCmt}px`
                );
            }
        }
    });
};

export const clickBtnActionMessage = () => {
    $(".mmessage.clicked").on("click", function (event) {
        var positionMsgAct = 0;
        var positionMsgActPrnt = 0;
        if ($(this).find(".show-more-action-message").hasClass("show-action")) {
            positionMsgAct =
                $(this).find(".show-more-action-message").offset().top || 0;
            positionMsgActPrnt =
                $(this).find(".show-more-action-message").parent().offset()
                    .top || 0;
            $(this)
                .find(".show-more-action-message")
                .removeClass("show-action");
        } else {
            $(this).find(".show-more-action-message").addClass("show-action");
            positionMsgAct =
                $(this).find(".show-more-action-message").offset().top || 0;
            positionMsgActPrnt =
                $(this).find(".show-more-action-message").parent().offset()
                    .top || 0;
        }
        var dropdownMenuComment = $(".dropdown-menu-comment");
        var windowHeight = $(window).height();
        var elementHeight = $(this).outerHeight();
        var elementOffset = $(this).offset().top;
        var bottomThreshold = windowHeight - windowHeight * 0.33;

        if (elementOffset + elementHeight >= bottomThreshold) {
            if (
                positionMsgAct.top - positionMsgActPrnt.top <
                dropdownMenuComment.height()
            ) {
                dropdownMenuComment.css(
                    "top",
                    `-${dropdownMenuComment.height()}px`
                );
            } else {
                dropdownMenuComment.css(
                    "top",
                    `${
                        positionMsgAct -
                        positionMsgActPrnt -
                        dropdownMenuComment.height() -
                        2
                    }px`
                );
            }
        } else {
            dropdownMenuComment.css("top", "");
        }
    });
};

export const sScrollbarBottom = () => {
    $(".mscrollbar--bottom").each(function () {
        let $this = $(this);

        setTimeout(function () {
            $this.scrollTop($this[0].scrollHeight);
        }, 500);
    });
};

export const initProjectComment = (target) => {
    initButtonSendProjectComment(target);

    let current_length = wavesurfer_arr.length;
    target.find(".video-comment-audio-wave").each(function (i, item) {
        if (
            !$(item).find("wave").length &&
            !wavesurfer_arr[i + current_length]
        ) {
            var audio_url = $(this).data("audio");

            var wavesurfer = WaveSurfer.create({
                container: item,
                waveColor: "#a7a8a9",
                progressColor: "#36aac4",
                cursorColor: "rgba(0,157,196,0.29)",
                barWidth: 3,
                barRadius: 3,
                cursorWidth: 3,
                barGap: 3,
                mediaControls: false,
                height: 50,
                responsive: true,
                hideScrollbar: true,
                partialRender: true,
                backend: "MediaElement",
            });

            wavesurfer_arr[current_length + i] = wavesurfer;
            let cmt_container = $(item).parents(".video-comment-item-reply");
            let cmt_id = cmt_container.attr("data-cmt-id");
            let peaks_loaded = cmt_container.attr("data-peaks-loaded");

            if (peaks_loaded) {
                let array_peaks = peaks_loaded.split(" ");
                wavesurfer.load(audio_url, array_peaks, "none");
            } else {
                wavesurfer.load(audio_url);
                wavesurfer.on("waveform-ready", function () {
                    let peaks = wavesurfer.backend.getPeaks(32);
                    let peaks_string = "";
                    for (let i = 0; i < peaks.length; i++) {
                        peaks_string +=
                            String(
                                Math.round(peaks[i] * Math.pow(10, 8)) /
                                    Math.pow(10, 8)
                            ) + " ";
                    }
                    var values = {
                        comment_id: cmt_id,
                        peaks: peaks_string,
                        type: "project",
                    };
                    $.ajax({
                        type: "POST",
                        url: "/top/update_comment",
                        data: values,
                        dataType: "json",
                        success: function (data) {
                            console.log("success");
                        },
                        error: function (e) {
                            console.log(e);
                        },
                    });
                });
            }

            $(this)
                .siblings(".video-comment-audio-title, .video-pin-time")
                .on("click", function () {
                    stop_video_audio();
                    let is_play = false;
                    let target_pin = $(this)
                        .siblings(".video-comment-audio-wave")
                        .siblings(".video-pin-time");
                    if (
                        $(this).siblings(".video-comment-audio-wave").length !==
                        0
                    ) {
                        $("video").each((i, e) => e.pause());
                        target.find(".video-pin-time").each(function () {
                            $(this).removeClass("playing");
                        });
                        for (i = 0; i < wavesurfer_arr.length; i++) {
                            if (wavesurfer_arr[i]) {
                                if (wavesurfer_arr[i].isPlaying()) {
                                    if (wavesurfer_arr[i] === wavesurfer) {
                                        is_play = true;
                                    }
                                    wavesurfer_arr[i].playPause();
                                }
                            }
                        }
                        if (!is_play) {
                            target_pin.addClass("playing");
                            wavesurfer.play();
                            wavesurfer.on("pause", function () {
                                if (
                                    wavesurfer.getDuration() ===
                                    wavesurfer.getCurrentTime()
                                ) {
                                    target_pin.removeClass("playing");
                                }
                                target_pin.removeClass("playing");
                            });
                        }
                    }
                });
        }
    });

    window.URL = window.URL || window.webkitURL;
    target.find('input[id^="messenger-attach-"]').on("change", function (e) {
        let context = this;
        let files = this.files;

        if (files.length) {
            let video = document.createElement("video");
            video.preload = "metadata";
            video.onloadedmetadata = function () {
                window.URL.revokeObjectURL(video.src);
                let duration = video.duration;
                $(context).attr("data-duration", Math.floor(duration));
            };
            video.src = URL.createObjectURL(files[0]);
        } else {
            $(context).attr("data-duration", -1);
        }

        let fileName = files[0].name;
        let clear_file_dom = "clear_" + $(e.target)[0].id;
        let comment_box = $(this).closest(".video-comment-item.comment-form");
        if (comment_box.find(".comment__textarea-file").length > 0) {
            comment_box.find(".comment__textarea-file span").text(fileName);
        } else {
            comment_box
                .find(".video-comment-message")
                .prepend(
                    '<div class="comment__textarea-file"><span>' +
                        fileName +
                        '</span><button type="button" class="clear_file close ' +
                        clear_file_dom +
                        '" aria-hidden="true">×</button></div>'
                );
        }

        comment_box
            .find(".clear_file")
            .off()
            .on("click", function () {
                let target_input = $(this)
                    .parents(".video-comment-message")
                    .find("input.video-comment-input-attach");
                $(this)
                    .parents(".video-comment-item.comment-form")
                    .find(".comment__textarea-file")
                    .remove();
                target_input.attr("data-duration", -1);
                target_input.val("");
            });
    });

    let messages = target.find(".s-text, s-filetext, .s-filedisable");
    $.each(messages, function (i, v) {
        let regex =
            /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/gim;
        v.innerHTML = v.innerHTML.replace(
            regex,
            "<a target='_blank' href=$&>$&</a>"
        );
    });
    if (
        target
            .parents(".project-video-item.show-comment")
            .find(".video-item-comment").length
    ) {
        target
            .parents(".project-video-item.show-comment")
            .find(".video-item-comment")
            .mCustomScrollbar("scrollTo", "bottom");
    }

    $(".fa-download, .comment__download-icon-down").on("click", function (e) {
        e.stopPropagation();
        let comment_id = $(this)
            .parents(".video-comment-item-reply")
            .attr("data-cmt-id");
        $.ajax({
            type: "GET",
            url: "/top/get_file_download_link",
            data: {
                comment_id: comment_id,
                type: "project",
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                window.location.href = response.url;
            },
            fail: function (response) {
                toastr.error("エラーが発生しました", "ファイルをダウンロード");
            },
        });
    });
};

export function commentInput() {
    const mcomment = $(".mcommment");
    if (mcomment.length > 0) {
        for (let i = 0;i< mcomment.length; i++){
            var comment_input = mcomment[i];
            var comment_id = comment_input.id;

            var mattach = $(".mattach:not(.mattach-form)")[0];
            var mattach_id = mattach.id;

            Dropzone.autoDiscover = false;

            var previewNode = comment_input.getElementsByClassName("mattach-template")[0];

            var previewTemplate = previewNode?.parentElement ? previewNode?.cloneNode(true).outerHTML : "";
            if (comment_input && comment_input.getElementsByClassName("mattach-template").length > 0) {
                comment_input.getElementsByClassName("mattach-template")[0].parentElement.innerHTML = "";
            }

            window.addEventListener(
                "dragover",
                function (e) {
                    e = e || event;
                    e.preventDefault();
                },
                false
            );
            window.addEventListener(
                "drop",
                function (e) {
                    e = e || event;
                    e.preventDefault();
                },
                false
            );

            if(mattach?.getElementsByClassName("mattach-drop").length > 0){
                mattach.getElementsByClassName("mattach-drop")[0].style.display = "none";
                mattach.getElementsByClassName("mattach-overlay")[0].style.display = "none";
            }

            var comment_count = document.getElementsByClassName("mcommment").length;
            if (comment_count > 1) {
                mattach.closest(".maction").on("dragover", function (e) {
                    var dt = e.originalEvent.dataTransfer;
                    console.log("dragover");
                    if (
                        dt.types &&
                        (dt.types.indexOf
                            ? dt.types.indexOf("Files") != -1
                            : dt.types.contains("Files"))
                    ) {
                        mattach.getElementsByClassName("mattach-overlay")[0].style.display = "block";
                        mattach.getElementsByClassName("mattach-drop")[0].style.display = "block";
                    }
                });

                mattach.closest(".maction").on("drop", function (e) {
                    mattach.getElementsByClassName("mattach-overlay")[0].style.display = "none";
                    mattach.getElementsByClassName("mattach-drop")[0].style.display = "none";
                    console.log("drop");
                });

                mattach.closest(".maction").on("dragleave", function (e) {
                    console.log("drag leave");
                });
            } else {
                $(window).on("dragover", function (e) {
                    if (!$("#modal-edit-offer").hasClass("in")) {
                        var dt = e.originalEvent.dataTransfer;
                        if (
                            dt.types &&
                            (dt.types.indexOf
                                ? dt.types.indexOf("Files") != -1
                                : dt.types.contains("Files"))
                        ) {
                            if (!$(".modal.in").length) {
                                mattach.style.display = "block";
                                mattach.getElementsByClassName("mattach-overlay")[0].style.display = "block";
                                mattach.getElementsByClassName("mattach-drop")[0].style.display = "block";
                            }
                        }
                    }
                });

                $(window).on("drop", function (e) {
                    mattach.style.display = "none";
                    mattach.getElementsByClassName("mattach-overlay")[0].style.display = "none";
                    mattach.getElementsByClassName("mattach-drop")[0].style.display = "none";
                });
            }
            mattach.getElementsByClassName("mattach-overlay")[0].addEventListener("click", function (e) {
                mattach.style.display = "none";
                mattach.getElementsByClassName("mattach-overlay")[0].style.display = "none";
                mattach.getElementsByClassName("mattach-drop")[0].style.display = "none";
            });

            $("#" + mattach_id + "-form").append(csrf);
            mzdrop = new Dropzone("#" + mattach_id + "-form", {
                autoDiscover: false,
                previewTemplate: previewTemplate,
                maxFiles: 10,
                maxFilesize: 4500,
                timeout: 900000,
                params: { "list_id": list_file_id },
                previewsContainer: "#" + comment_id + " .mattach-previews",
                clickable: "#" + comment_id + " .mattach-label",
                autoProcessQueue: false,
                autoQueue: false,
            });

            mzdrop.on("addedfile", function (file, e) {
                document.getElementsByClassName("mcomment-send")[0].classList.add("active");
                let file_names = [];
                mzdrop["sended"] = false;
                let key_file = "";
                let file_dom = $(file.previewElement);
                let path = "";
                if (is_exceeded_length) {
                    if (!mzdrop.printed_err) {
                        toastr.error("Folder'name is too long.");
                    }
                    let page = getPage(file_dom);
                    let folder_ids = Object.values(list_temp_folder_id);
                    for (let folder_id of folder_ids) {
                        delete_folder(folder_id, page);
                    }
                    file.not_created = true;
                    mzdrop.removeFile(file);
                    list_temp_folder_id = {};
                    list_files_folders = {};
                    list_temp_folder_name = [];
                } else {
                    list_folder_id = {...list_folder_id, ...list_temp_folder_id};
                    list_folder_name.concat(list_temp_folder_name);
                    
                    if (!jQuery.isEmptyObject(list_files_folders)) {
                        for (const key in list_files_folders) {
                            if (list_files_folders[key].includes(file.name)) {
                                path = key;
                                let index = list_files_folders[key].indexOf(
                                    file.name
                                );
                                list_files_folders[key].splice(index, 1);
                                if (!list_files_folders[key].length) {
                                    delete list_files_folders[key];
                                }
                                break;
                            }
                        }
                        if (jQuery.isEmptyObject(list_files_folders)) {
                            list_files_folders = {};
                            list_temp_folder_name = [];
                            list_temp_folder_id = {};
                        }
                    }
                    let file_preview = $(".mattach-preview-container").find(
                        ".mcommment-file__name"
                    );
                    for (let i = 0; i < file_preview.length; i++) {
                        if ($(file_preview[i]).text() == file.name) {
                            let real_path =
                                path.substring(0, path.indexOf("---")) +
                                path.slice(path.indexOf("/"));
                            $(file_preview[i]).text(real_path + file.name);
                            break;
                        }
                    }
                    if (path === "") {
                        list_file_name.push(file.name);
                    }
                    uploadFileS3(file, file_dom, path);
                }
            });

            mzdrop.on("dragenter", function () {
                mattach.getElementsByClassName("mattach-file")[0].classList.add("active");
            });

            mzdrop.on("dragover", function () {
                mattach.getElementsByClassName("mattach-file")[0].classList.add("active");
            });

            mzdrop.on("dragleave", function () {
                mattach.getElementsByClassName("mattach-file")[0].classList.add("active");
            });

            mzdrop.on("drop", function (e) {
                is_exceeded_length = false;
                mzdrop.printed_err = false;
                document.getElementsByClassName("mattach-file")[0].classList.remove("active");
                mattach.style.display = "none";
                mattach.getElementsByClassName("mattach-overlay")[0].style.display = "none";
                mattach.getElementsByClassName("mattach-drop")[0].style.display = "none";
                const element = document.getElementsByClassName("mcomment-bottom")[0];
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                  });
                  element.dispatchEvent(event); 
                let items = e.dataTransfer.items;
                let today = new Date();
                let epoch = Math.floor(today / 1000);
                let dateTime = "---" + epoch;
                let page = getPage(mattach);

                for (let i = 0; i < items.length; i++) {
                    var item = items[i].webkitGetAsEntry();
                    if (item.isFile) {
                        //list_file_name.push(item.name);
                        traverseFileTree(item, "", 0, "", page);
                    } else {
                        list_temp_folder_name.push(item.name);
                        traverseFileTree(
                            item,
                            item.name + dateTime + "/",
                            0,
                            "",
                            page
                        );
                    }
                }
            });

            mzdrop.on("error", function (file) {
                console.log("error");
            });

            mzdrop.on("removedfile", function (file) {
                mzdrop.printed_err = true;
                if (file.not_create) {
                    return;
                }
                if (
                    mzdrop.files.length == 0 &&
                    !comment_input.getElementsByClassName("mcomment-input-text")[0].value
                ) {
                    const mcommentElement = mattach.closest('.mcommment');
                    const mcommentSendElement = mcommentElement?.getElementsByClassName("mcomment-send")[0];
                    if (mcommentSendElement) {
                        mcommentSendElement.classList.remove("active");
                    }
                }
                if (!file["not_created"] && !mzdrop["sended"]) {
                    let file_id = Object.keys(list_file_id).find(
                        (key) => list_file_id[key] === file.name
                    );
                    if (file.name.indexOf(list_file_name) >= 0) {
                        list_file_name.pop(file.name.indexOf(list_file_name));
                    }
                    if (file_id) {
                        delete list_file_id[file_id];
                        $.ajax({
                            type: "POST",
                            data: {
                                file_id: file_id,
                                message_type: getPage(mattach),
                            },
                            url: "/upload/remove_file",
                            success: function (data) {
                                let list_folder_removed =
                                    data.removed_folder_id?.split(",");
                                for (let key in list_folder_id) {
                                    if (
                                        list_folder_removed.includes(
                                            list_folder_id[key]
                                        )
                                    ) {
                                        delete list_folder_id[key];
                                    }
                                }
                                $(".mcomment-input-text").attr(
                                    "type_input",
                                    "input"
                                );
                                setTimeout(async () => {
                                    if (
                                        $(document).find(
                                            "textarea.mcomment-input-text"
                                        ).length
                                    ) {
                                        await doneTyping(
                                            $(document)
                                                .find(
                                                    "textarea.mcomment-input-text"
                                                )
                                                .val()
                                        );
                                    }
                                }, 2000);
                            },
                        });
                    }
                }
            });

            $(document).add(
                "click",
                ".mcommment-file__delete",
                async function () {
                    let offer_id = document.getElementsByClassName("mitem.mactive")[0]
                        .data-offer;
                        console.log(offer_id);
                    currentOffer = offer_id;
                    if (
                        window.location.href
                            .toString()
                            .includes("tab=messenger") ||
                        window.location.href
                            .toString()
                            .includes(`offer=${offer_id}`)
                    ) {
                        let file_id = $(this)
                            .parents(".mattach-template")
                            .attr("data-file-id");
                        list_file_remove.push(file_id);
                        let file_name = $(this)
                            .parent()
                            .find(".mcommment-file__name")
                            .html();
                        file_name =
                            file_name.split("/")[
                                file_name.split("/").length - 1
                            ];
                        file_id = Object.keys(list_file_id).find(
                            (key) => list_file_id[key] === file_name
                        );
                        $(this).parents(".mattach-template").remove();
                        if (file_id) {
                            delete list_file_id[file_id];
                            $.ajax({
                                type: "POST",
                                data: {
                                    file_id: file_id,
                                    message_type: getPage(mattach),
                                },
                                url: "/upload/remove_file",
                                success: function (data) {
                                    let list_folder_removed =
                                        data.removed_folder_id.split(",");
                                    for (let key in list_folder_id) {
                                        if (
                                            list_folder_removed.includes(
                                                list_folder_id[key]
                                            )
                                        ) {
                                            delete list_folder_id[key];
                                        }
                                    }
                                },
                            });
                            $(document)
                                .find(".mcomment-input-text")
                                .attr("type_input", "input");
                            if (
                                $(document).find("textarea.mcomment-input-text")
                                    .length
                            ) {
                                await doneTyping(
                                    $(document)
                                        .find("textarea.mcomment-input-text")
                                        .val()
                                );
                            }
                        }
                    }
                }
            );

            mattach
				.closest(".mcommment")
                ?.getElementsByClassName(".mcomment-send")[0]
                .addEventListener("click", function (e) {
                    e.preventDefault();
                });

            // // https://codepen.io/vsync/pen/frudD
            $(document).on(
                "input.mcomment-input-text",
                "textarea.mcomment-input-text",
                function () {
                    calculateHeightCommentInput(this);
                    $(this).attr("type_input", "input");
                    clearTimeout(typingTimer);
                    typingTimer = setTimeout(async () => {
                        if (
                            $(document).find("textarea.mcomment-input-text")
                                .length
                        ) {
                            await doneTyping(
                                $("textarea.mcomment-input-text").val()
                            );
                        }
                    }, doneTypingInterval);
                }
            );
        };
    }
};

const getPage = (target) => {
    let page = ''
    if ($(target).parents('.project-tab-product-comment').length) {
        page = 'product_comment'
    } else if ($(target).parents('.pd-scene-title-detail').length) {
        page = 'scene_comment'
    } else if (user_role !== 'admin') {
        page = 'message_owner'
    } else if ($(target).parents('.martist').length) {
        page = 'message'
    }
    return page
}

export const getDownloadFileScene = () => {
    $(document).off('click', '.block-download-file .scene-file-download').on('click', '.block-download-file .scene-file-download', function (e) {
        let data = new FormData();
        e.stopPropagation();
        let file_id;
        if ($(this).parents('.tfile-producttion-file').length > 0) {
            data.append('production_file', 'production_file');
            file_id = $(this).parents('.tfile-producttion-file').attr('data-scene-title-id')
        } else {
            file_id = $(this).parents('.block-download-file').attr('data-file-id');
        }
        data.append('file_id', file_id);
        downloadFile(data, this)
    })

    $(document).on('click', '.tfile-info.btn-download-file', function (e) {
        let data = new FormData();
        e.stopPropagation();
        let file_id;
        if ($(this).parents('.tfile-infor').length > 0) {
            file_id = $(this).parents('.tfile-infor').attr('data-file-id');
        }
        data.append('file_id', file_id);
        downloadFile(data, this)
    })
}

export const getDownloadAudio = () => {
    $(document)
        .off('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")')
        .on('click', '.s-audio-name .icon--sicon-download:not(".icon--sicon-folder-download"), .icon--sicon-download:not(".icon--sicon-folder-download")',
            function (e) {
                let data = new FormData();
                e.stopPropagation();
                let file_id;
                if ($(this).parents(".tfile-producttion-file").length > 0) {
                    data.append("production_file", "production_file");
                    file_id = $(this).parents(".tfile-producttion-file").attr("data-scene-title-id");
                } else if ($(this).parents(".mfolder__sub").length) {
                    file_id = $(this).parents(".mfolder__sub").data("file-id");
                } else if ($(this).parents("#folderModal").length) {
                    file_id = $(this).parents(".list-group-item").attr("data-file-id");
                } else {
                    file_id = $(this).parents(".mmessage").attr("data-file-id");
                    if (!file_id) {
                        file_id = $(this).parents(".minfo-file_info").attr("data-file-id");
                        if (!file_id) {
                            file_id = $(this).parents(".tfile-infor").attr("data-file-id");
                        }
                    }
                    if (!file_id) {
                        file_id = $(this).parents(".tfile-infor").attr("data-scene-id");
                        data.append("type_infor", "scene");
                    }
                }

                if (file_id) {
                    data.append("file_id", file_id);
                    downloadFile(data, this);
                }
            }
        );

    $(document)
        .off("click", ".icon--sicon-folder-download")
        .on("click", ".icon--sicon-folder-download", function (e) {
            e.stopPropagation();
            let folder = $(this).parent(".list-group-item");
            if (!folder.length) {
                folder = $(this).parent(".parent-folder");
            }
            if (!folder.length) {
                folder = $(this).parent(".mfolder__sub");
            }
            let folder_id = folder.find(".hasSub").attr("data-folder-id");
            DownloadFolder(folder_id, this);
        });
};

export const createMessage = (active_offer) => {
    let url_page = "";
    if (messenger_page === "messenger_artist" || (messenger_page === "top_page" && $(".pbanner-tab.active[data-show=messenger]").length)) {
        url_page = "/offer_message/create";
    } else if (messenger_page === "top_page") {
        url_page = "/api/create-and-get-data-comment";
    }
    active_offer
        .off("click")
        .on("click", ".mcomment-send.active:not(.input-editing)", function (e) {
            e.stopPropagation();
            e.preventDefault();
            let button_send = $(this);
            if (!$(this).hasClass("input-editing")) {
                if (!$(this).hasClass("is-sending")) {
                    $(this).addClass("is-sending");
                    $(".mcomment-bottom").addClass("disabled-mcomment");
                    sScrollbarBottom();
                    let offer_id = $(this).parents(".maction").data("offer");
                    let last_message_id = "";
                    if ($(".mmessage-list").find(".mmessage:not(.mmessage-confirm)").length) {
                        if (!$(".mmessage-list").find(".mmessage:not(.mmessage-confirm)").last().hasClass("mmessage-system")) {
                            last_message_id = $(".mmessage-list").find(".mmessage:not(.mmessage-confirm, .mmessage-system)").last().data("message-id");
                        }
                    }
                    let active_offer = $(".offer-" + offer_id);
                    let scene_id = "";
                    let pin_time = "";
                    let scene_title_id = "";
                    let parent_id = "";
                    let has_pin = false;
                    if (!offer_id) {
                        scene_title_id = $(this).parents(".pd-scene-title-detail").attr("data-scene-title-id");
                        active_offer = $(".pd-scene-title-detail[data-scene-title-id=" + scene_title_id + "]");
                        if (active_offer.find(".mcomment-input").hasClass("is-reply")) {
                            parent_id = active_offer.find(".mcomment-input.is-reply").attr("data-parent-id");
                        }

                        if ($(this).parents(".mcommment").find(".mcomment-pin").hasClass("active")) {
                            has_pin = true;
                            scene_id = $(this)
                                .parents(".pd-scene-title-detail").find(".cscene__variation.slick-current .cscene__version.slick-current.slick-active")
                                .attr("data-scene-id");
                            pin_time = $(this).parents(".mcommment").find(".mcomment-input-title").eq(0).text();
                            if (!pin_time.length) {
                                if ($(this).parents(".pd-scene-title-detail").find(".cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe")) {
                                    pin_time = "";
                                } else {
                                    pin_time = "00:00";
                                }
                            }
                        }
                    }
                    if ($(".pd-section--detail.pd-product-comment").length > 0) {
                        active_offer = $(".pd-section--detail.pd-product-comment");
                    }
                    let input_message = $(this).closest(".mcommment");
                    let messageContent = active_offer.find(".mcomment-input-text.mcomment-autoExpand").val();
                    if (messageContent.trim() === "") {
                        messageContent = "";
                    }

                    let data = new FormData();
                    data.append("offer_id", offer_id);
                    data.append("message", messageContent);
                    data.append("before_message_id", last_message_id);
                    data.append("scene_id", scene_id);
                    data.append("pin_time", pin_time);
                    data.append("scene_title_id", scene_title_id);
                    data.append("has_pin", has_pin);
                    if ($(this).parents(".pd-product-comment").length > 0) {
                        data.append("type", "project");
                        let project_id = $(".project-item.active").attr(
                            "data-project-id"
                        );
                        parent_id = active_offer
                            .find(".mcomment-input.is-reply")
                            .attr("data-parent-id");
                        data.append("project_id", project_id);
                        data.append("parent_id", parent_id);
                    } else {
                        data.append("type", "scene");
                        data.append("parent_id", parent_id);
                    }

                    if (scene_id !== "") {
                        let html_folder = "";

                        for (folder in list_folder_name) {
                            if (!folder.includes("/")) {
                                html_folder += `
                                    <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                            <div class="s-audio-control video-pin-time">
                                                <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                                    play_circle
                                                </span>
                                                <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                            </div>
                                            <div class="s-audio-time video-pin-start">${pin_time}</div>
                                        </div>
                                        <div class="s-audio-text s-audio-file">
                                            <div class="mmessenger mmessenger--file mmessenger--black">
                                                <div class="messenger-content">
                                                    <div class="s-file s-file--file s-file--black">
                                                        <i class="icon icon icon--sicon-storage"></i>${list_folder_name[folder]}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>`;
                            }
                        }

                        for (let file_name in list_file_name) {
                            html_folder += `
                                <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                    <div style="display: flex">
                                        <div class="s-audio-control video-pin-time">
                                            <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                                play_circle
                                            </span>
                                            <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                        </div>
                                         <div class="s-audio-time video-pin-start">${pin_time}</div>
                                    </div>
                                    <div class="s-audio-text s-audio-file">
                                        <div class="mmessenger mmessenger--file mmessenger--black">
                                            <div class="messenger-content">
                                                <div class="s-file s-file--file s-file--black">
                                                    <i class="icon icon icon--sicon-clip"></i>${list_file_name[file_name]}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>`;
                        }

                        let html_content = "";
                        if (messageContent !== "") {
                            html_content = `
                                <div class="mmessenger mmessenger--text mmessenger--black">
                                <div class="messenger-content">
                                    <div class="s-audio s-audio--audio s-audio--black" data-scene-id="${scene_id}">
                                        <div style="display: flex">
                                            <div class="s-audio-control video-pin-time">
                                                <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                                                    play_circle
                                                </span>
                                                <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                                            </div>
                                            <div class="s-audio-time video-pin-start">${pin_time}
                                            </div>
                                        </div>
                                        <div class="s-audio-text">${messageContent}
                                        </div>
                                    </div>
                                </div>
                            </div>`;
                        }

                        let message_html = `
                            <div class="mmessage mmessage--sent clicked new-message">
                                <div class="mmessage-main">
                                    <div class="mmessage-content"> 
                                        ${html_folder} 
                                        ${html_content}
                                    </div>
                                </div>
                            </div>`;

                        $(message_html).insertBefore(
                            $(".pd-section--detail").find(".mlast__content")
                        );
                    } else {
                        if ($(".owner-top").hasClass("scene-detail-page")) {
                            scene_id = $(this).parents(".pd-scene-title-detail")
                                .find(".cscene__variation.slick-current .cscene__version.slick-current.slick-active")
                                .attr("data-scene-id");
                            data.append("scene_id", scene_id);
                        }
                    }

                    if (mzdrop.files.length > 0 && mzdrop.files[0]) {
                        let file_loaded = Object.values(list_file_id);
                        let file_loading =
                            mzdrop.files.length - file_loaded.length;

                        if (file_loading) {
                            $(".upload-button-wrapper").css("display", "flex");
                            $(".upload-button-wrapper").addClass("clicked");
                            $(".upload-button-wrapper .fill .process").css(
                                "width",
                                "2%"
                            );
                            var waiting_file_loading = setInterval(function () {
                                let current_file_loaded =
                                    Object.values(list_file_id);
                                let current_file_loading =
                                    mzdrop.files.length -
                                    current_file_loaded.length;
                                input_message.find(".mcomment-top").show();
                                let progress = getProgressUploaded();
                                $(".upload-button-wrapper .fill .process").css(
                                    "width",
                                    progress + "%"
                                );
                                if (!current_file_loading) {
                                    data.append(
                                        "list_file_id",
                                        Object.keys(list_file_id)
                                    );
                                    data.append(
                                        "list_folder_id",
                                        Object.values(list_folder_id)
                                    );
                                    clearInterval(waiting_file_loading);
                                    $.ajax({
                                        type: "POST",
                                        contentType: false,
                                        processData: false,
                                        cache: false,
                                        data: data,
                                        url: url_page,
                                        beforeSend: function (data) {
                                            // toastr.info('アップロード中…');
                                        },
                                        success: function (data) {
                                            sScrollbarBottom();

                                            $(
                                                ".upload-button-wrapper .fill .process"
                                            ).css("width", "100%");
                                            if ($(".prdt").length < 1) {
                                                input_message
                                                    .find(".mcomment-top")
                                                    .hide();
                                            }
                                            setTimeout(function () {
                                                // toastr.success(data.success_message);
                                                $(".upload-button-wrapper")
                                                    .removeClass("clicked")
                                                    .addClass("success");
                                            }, 1000);
                                            setTimeout(function () {
                                                $(".upload-button-wrapper")
                                                    .removeClass("success")
                                                    .css("display", "none");
                                                $(
                                                    ".upload-button-wrapper .fill .process"
                                                ).css("width", "0");
                                            }, 2000);
                                        },
                                        complete: function () {
                                            newWavesurferInit();
                                            mzdrop["sended"] = true;
                                            mzdrop.removeAllFiles();
                                            list_file_id = {};
                                            list_files_folders = {};
                                            list_folder_id = [];
                                            list_folder_name = [];
                                            list_file_name = [];
                                            button_send.removeClass(
                                                "is-sending"
                                            );
                                            $(".mcomment-bottom").removeClass(
                                                "disabled-mcomment"
                                            );
                                            resetInputMessage(active_offer);
                                            if ($(".scene-style").length > 0) {
                                                calcMoreActionComment(
                                                    $(".scene-style .mmessage")
                                                );
                                            } else if (
                                                $(".main-talk-room").length > 0
                                            ) {
                                                calcMoreActionComment(
                                                    $(
                                                        ".main-talk-room .mmessage"
                                                    )
                                                );
                                            }
                                            calcPositionDropdownComment();
                                            calcPositionDropdownComment2();
                                            calcMoreActionComment(
                                                $(".prdt .mmessage")
                                            );
                                            hoverDropdownMessage();
                                            hoverBtnActionMessage();
                                            clickBtnActionMessage();
                                        },
                                    });
                                }
                            }, 100);
                        } else {
                            data.append(
                                "list_file_id",
                                Object.keys(list_file_id)
                            );
                            data.append(
                                "list_folder_id",
                                Object.values(list_folder_id)
                            );
                            $.ajax({
                                type: "POST",
                                contentType: false,
                                processData: false,
                                cache: false,
                                data: data,
                                url: url_page,
                                beforeSend: function (data) {
                                    // toastr.info('アップロード中…');
                                },
                                success: function (data) {
                                    // toastr.success("完了しました。");

                                    sScrollbarBottom();
                                },
                                complete: function () {
                                    newWavesurferInit();
                                    mzdrop["sended"] = true;
                                    mzdrop.removeAllFiles();
                                    list_file_id = {};
                                    list_files_folders = {};
                                    list_folder_id = [];
                                    button_send.removeClass("is-sending");
                                    resetInputMessage(active_offer);
                                    list_folder_name = [];
                                    list_file_name = [];
                                    $(".mcomment-bottom").removeClass(
                                        "disabled-mcomment"
                                    );
                                    showLastCommentSceneDetailSP();
                                    if ($(".scene-style").length > 0) {
                                        calcMoreActionComment(
                                            $(".scene-style .mmessage")
                                        );
                                    } else if (
                                        $(".main-talk-room").length > 0
                                    ) {
                                        calcMoreActionComment(
                                            $(".main-talk-room .mmessage")
                                        );
                                    }
                                    calcPositionDropdownComment();
                                    calcPositionDropdownComment2();
                                    calcMoreActionComment($(".prdt .mmessage"));
                                    hoverDropdownMessage();
                                    hoverBtnActionMessage();
                                    clickBtnActionMessage();
                                },
                            });
                        }
                    } else if (Object.keys(list_file_id).length) {
                        data.append("list_file_id", Object.keys(list_file_id));
                        data.append(
                            "list_folder_id",
                            Object.values(list_folder_id)
                        );
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            data: data,
                            url: url_page,
                            beforeSend: function (data) {
                                // toastr.info('アップロード中…');
                            },
                            success: function (data) {
                                // toastr.success("完了しました。");

                                sScrollbarBottom();
                                $(document).find(".mattach-previews").empty();
                            },
                            complete: function () {
                                newWavesurferInit();
                                mzdrop["sended"] = true;
                                mzdrop.removeAllFiles();
                                list_file_id = {};
                                list_files_folders = {};
                                list_folder_id = [];
                                button_send.removeClass("is-sending");
                                resetInputMessage(active_offer);
                                list_folder_name = [];
                                list_file_name = [];
                                $(".mcomment-bottom").removeClass(
                                    "disabled-mcomment"
                                );
                                if ($(".scene-style").length > 0) {
                                    calcMoreActionComment(
                                        $(".scene-style .mmessage")
                                    );
                                } else if ($(".main-talk-room").length > 0) {
                                    calcMoreActionComment(
                                        $(".main-talk-room .mmessage")
                                    );
                                }
                                calcPositionDropdownComment();
                                calcPositionDropdownComment2();
                                calcMoreActionComment($(".prdt .mmessage"));
                                hoverDropdownMessage();
                                hoverBtnActionMessage();
                                clickBtnActionMessage();
                            },
                        });
                    } else if (messageContent.trim() !== "") {
                        $.ajax({
                            type: "POST",
                            contentType: false,
                            processData: false,
                            cache: false,
                            data: data,
                            url: url_page,
                            success: function (data) {
                                sScrollbarBottom();
                            },
                            complete: function () {
                                newWavesurferInit();
                                button_send.removeClass("is-sending");
                                resetInputMessage(active_offer);
                                list_folder_name = [];
                                list_file_name = [];
                                $(".mcomment-bottom").removeClass(
                                    "disabled-mcomment"
                                );
                                if ($(".scene-style").length > 0) {
                                    calcMoreActionComment(
                                        $(".scene-style .mmessage")
                                    );
                                } else if ($(".main-talk-room").length > 0) {
                                    calcMoreActionComment(
                                        $(".main-talk-room .mmessage")
                                    );
                                }
                                calcPositionDropdownComment();
                                calcPositionDropdownComment2();
                                calcMoreActionComment($(".prdt .mmessage"));
                                hoverDropdownMessage();
                                hoverBtnActionMessage();
                                clickBtnActionMessage();
                            },
                        });
                    } else if (messageContent.trim() === "") {
                        button_send.removeClass("is-sending");
                        resetInputMessage(active_offer);
                        list_folder_name = [];
                        list_file_name = [];
                        $(".mcomment-bottom").removeClass("disabled-mcomment");
                    }
                }
                $(document)
                    .find(".mcomment-input-text")
                    .attr("type_input", "input");
                list_file_id = {};
                list_folder_id = {};
                valInput = "";
                doneTyping("");
                setTimeout(function () {
                    active_offer.find(".mcomment-input-placeholder").show();

                    if (!$(".mcomment-top").hasClass("comment-top-area")) {
                        active_offer.find(".mcomment-top").hide();
                    }
                    if (!!$(".btn-tutorial-sp")) {
                        $(".btn-tutorial-sp").css(
                            "bottom",
                            `${
                                (2 *
                                    Math.max(
                                        document.documentElement.clientWidth,
                                        window.innerWidth || 0
                                    )) /
                                100
                            }px`
                        );
                    }
                }, 100);
                if (
                    messenger_page === "messenger_artist" ||
                    messenger_page === "top_page"
                ) {
                    $("html, body").animate(
                        { scrollTop: $(".mcommment").height() + 200 },
                        1000
                    );
                }
                should_scroll = false;
                $(".mcomment-input-text.mcomment-autoExpand").css("height", "");
            }
        });
};

export const editMessage = (active_offer) => {
    let url_page = "";
    active_offer.on(
        "click",
        ".mcomment-send.active.input-editing",
        function (e) {
            e.stopPropagation();
            e.preventDefault();
            let button_dom = $(this);
            should_scroll = false;
            if (!$(this).hasClass("is-sending")) {
                button_dom.addClass("is-sending");
                let offer_id = $(this).parents(".maction").data("offer");
                let message_id = active_offer
                    .find(".mmessage.editing")
                    .attr("data-message-id");
                if (message_id) {
                    let message_content = active_offer
                        .find(".mcommment .mcomment-input-text")
                        .val();
                    if (message_content.trim() === "") {
                        message_content = "";
                    }
                    let pin_time = "";
                    let scene_title_id = "";
                    let scene_id = "";
                    let has_pin = false;

                    if (!offer_id) {
                        scene_title_id = $(this)
                            .parents(".pd-scene-title-detail")
                            .attr("data-scene-title-id");
                        active_offer = $(
                            ".pd-scene-title-detail[data-scene-title-id=" +
                                scene_title_id +
                                "]"
                        );

                        if (
                            $(this)
                                .parents(".mcommment")
                                .find(".mcomment-pin")
                                .hasClass("active")
                        ) {
                            has_pin = true;
                            scene_id = $(this).parents(".pd-scene-title-detail")
                                .find(".cscene__variation.slick-current .cscene__version.slick-current.slick-active")
                                .attr("data-scene-id");
                            pin_time = $(this).parents(".mcommment")
                                .find(".mcomment-input-title")
                                .eq(0)
                                .text();
                            if (!pin_time.length) {
                                if ($(this).parents(".pd-scene-title-detail")
                                    .find(".cscene__variation.slick-current .cscene__version.slick-current.slick-active iframe")) {
                                    pin_time = "";
                                } else {
                                    pin_time = "00:00";
                                }
                            }
                        }
                    }

                    if ($(".project-tab-product-comment").hasClass("active")) {
                        active_offer = $(".pd-section--detail.pd-product-comment");
                    }

                    let data = new FormData();
                    if (
                        messenger_page === "messenger_artist" ||
                        (messenger_page === "top_page" && $(".pbanner-tab.active[data-show=messenger]").length)
                    ) {
                        url_page = "/messenger/update_offer_message";
                        data.append(
                            "offer_id",
                            $(".maction").attr("data-offer")
                        );
                    } else if (messenger_page === "top_page") {
                        url_page = "/api/update-content-comment";

                        if (
                            $(".project-tab-product-comment").hasClass("active")
                        ) {
                            data.append("type", "project");
                            let project_id = $(".project-item.active").attr(
                                "data-project-id"
                            );
                            data.append("project_id", project_id);
                        } else {
                            data.append("pin_time", pin_time);
                            data.append("scene_title_id", scene_title_id);
                            data.append("scene_id", scene_id);
                            data.append("has_pin", has_pin);
                        }
                    }
                    data.append("message_id", message_id);
                    data.append("message_content", message_content);
                    data.append("file", mzdrop.files[0]);
                    data.append("list_file_remove", list_file_remove);
                    let file_loaded = Object.values(list_file_id);
                    let file_loading = mzdrop.files.length - file_loaded.length;
                    let number_files = $(this)
                        .parents(".mcomment-message")
                        .find(".mattach-template").length;
                    if (!number_files && !message_content) {
                        $(`.mmessage--sent[data-message-id='${message_id}']`)
                            .find(".mmessage-delete")
                            .trigger("click");
                        button_dom.removeClass("is-sending");
                    } else {
                        if (file_loading) {
                            $(".upload-button-wrapper").css("display", "flex");
                            $(".upload-button-wrapper").addClass("clicked");
                            $(".upload-button-wrapper .fill .process").css(
                                "width",
                                "2%"
                            );
                            var waiting_file_loading = setInterval(function () {
                                let current_file_loaded =
                                    Object.values(list_file_id);
                                let current_file_loading =
                                    mzdrop.files.length -
                                    current_file_loaded.length;
                                let progress = getProgressUploaded();
                                $(".upload-button-wrapper .fill .process").css(
                                    "width",
                                    progress + "%"
                                );
                                if (!current_file_loading) {
                                    data.append(
                                        "list_file_id",
                                        Object.keys(list_file_id)
                                    );
                                    data.append(
                                        "list_folder_id",
                                        Object.values(list_folder_id)
                                    );
                                    clearInterval(waiting_file_loading);
                                    active_offer
                                            .find(".mcomment-top")
                                            .hide();
                                    $.ajax({
                                        type: "POST",
                                        contentType: false,
                                        processData: false,
                                        cache: false,
                                        url: url_page,
                                        async: false,
                                        data: data,
                                        success: function (data) {
                                            active_offer
                                            .find(".mcomment-top")
                                            .show();
                                            $(
                                                ".upload-button-wrapper .fill .process"
                                            ).css("width", "100%");
                                            setTimeout(function () {
                                                // toastr.success(data.success_message);
                                                $(".upload-button-wrapper")
                                                    .removeClass("clicked")
                                                    .addClass("success");
                                            }, 1000);
                                            setTimeout(function () {
                                                $(".upload-button-wrapper")
                                                    .removeClass("success")
                                                    .css("display", "none");
                                                $(
                                                    ".upload-button-wrapper .fill .process"
                                                ).css("width", "0");
                                            }, 2000);
                                        },
                                        complete: function () {
                                            mzdrop["sended"] = true;
                                            mzdrop.removeAllFiles();
                                            list_file_id = {};
                                            list_files_folders = {};
                                            list_folder_id = [];
                                            resetInputMessage(active_offer);
                                            button_dom.removeClass(
                                                "is-sending"
                                            );
                                            resetFormContract();
                                            clickBtnActionMessage();
                                        },
                                    });
                                    $("html, body").animate(
                                        {
                                            scrollTop:
                                                $(".mcommment").height() + 200,
                                        },
                                        1000
                                    );
                                    setTimeout(function () {
                                        active_offer
                                            .find(".mcomment-input-placeholder")
                                            .show();
                                    }, 100);
                                }
                            }, 100);
                        } else {
                            data.append("list_file_id", Object.keys(list_file_id));
                            data.append(
                                "list_folder_id",
                                Object.values(list_folder_id)
                            );
                            $.ajax({
                                type: "POST",
                                contentType: false,
                                processData: false,
                                cache: false,
                                url: url_page,
                                async: false,
                                data: data,
                                success: function (data) {},
                                complete: function () {
                                    mzdrop["sended"] = true;
                                    mzdrop.removeAllFiles();
                                    list_file_id = {};
                                    list_folder_id = [];
                                    list_files_folders = {};
                                    resetInputMessage(active_offer);
                                    button_dom.removeClass("is-sending");
                                    resetFormContract();
                                    clickBtnActionMessage();
                                },
                            });
                            if (
                                messenger_page === "messenger_artist" ||
                                (messenger_page === "top_page" &&
                                    $(
                                        ".pbanner-tab.active[data-show=messenger]"
                                    ).length)
                            ) {
                                $("html, body").animate(
                                    {
                                        scrollTop:
                                            $(".mcommment").height() + 200,
                                    },
                                    1000
                                );
                            }
                            setTimeout(function () {
                                active_offer
                                    .find(".mcomment-input-placeholder")
                                    .show();
                                if (!$(".prdt").length > 0) {
                                    active_offer.find(".mcomment-top").hide();
                                    active_offer
                                        .find(".mcomment-attached")
                                        .css("padding", 0);
                                }
                            }, 100);
                        }
                    }
                }
                setTimeout(() => {
                    should_scroll = true;
                }, 1500);
            }
            $(".mcomment-input-text.mcomment-autoExpand").css("height", "");
            clickBtnActionMessage();
            calcMoreActionComment($(".prdt .mmessage"));
            calcPositionDropdownComment2();
        }
    );
};

export const resolveComment = (target) => {
    target
        .find(".video-comment-resolve")
        .off()
        .on("click", function () {
            let target = $(this).parents(".video-comment-item-reply");
            let comment_id = target.attr("data-cmt-id");

            //hide/show
            while (target.is(".sub-item")) {
                target = target.prev();
            }

            let resolved = target.is(".resolved");
            if (target.is(".resolved")) {
                show_unresolved_comment(target);
            } else {
                hide_resolved_comment(target);
            }
            $.ajax({
                type: "POST",
                datatype: "json",
                url: "/top/resolve_comment",
                data: {
                    comment_id: comment_id,
                    resolved: resolved,
                    type: "project",
                },
                fail: (data) => {
                    let text = target.is(".resolved") ? "再開" : "解決";
                    toastr.error("エラーが発生しました", "コメント" + text);
                    if (resolved) {
                        hide_resolved_comment(target);
                    } else {
                        show_unresolved_comment(target, true);
                    }
                },
            });
        });
};

export const projectToggleResolved = () => {
    $(".pd-scene-title-detail, .pd-product-comment").on(
        "change",
        "#switch-checkbox-comment, #order-monthy",
        function () {
            const checked = $(this).is(":checked");
            actionShowIconResolved();
            calcMoreActionComment(
                $(".main-talk-room .mmessage.resolved.clicked")
            );
            calcMoreActionComment($(".scene-style .mmessage"));
        }
    );
};

export const removeDuplicatedDate = () => {
    let last_date;
    let date;
    let list_item = [];
    let count = 0;
    $(".tfile-item-time").removeClass("hide");
    if ($(".pd-scene-title-detail").length) {
        $(".tfile-item-time").each(function (i, e) {
            if ($(this).hasClass("item-scene")) {
                count = $(
                    '.item-scene[data-time="' + e.innerHTML + '"]'
                ).length;
            }
            if (count > 0) {
                if (last_date === e.innerHTML) {
                    list_item.push($(this));
                }
                if (list_item.length >= count) {
                    let sort_by_variation = function (a, b) {
                        let a_value = a.parentElement.getAttribute(
                            "data-variation-index"
                        );
                        let b_value = b.parentElement.getAttribute(
                            "data-variation-index"
                        );
                        if (a_value === b_value) {
                            return -1;
                        } else {
                            return a_value.localeCompare(b_value);
                        }
                    };
                    let list = $(
                        '.item-scene[data-time="' + e.innerHTML + '"]'
                    ).get();
                    list.sort(sort_by_variation);

                    for (let i = 1; i <= list.length - 1; i++) {
                        console.log(
                            i +
                                " " +
                                list[i].parentElement.getAttribute(
                                    "data-variation-index"
                                )
                        );
                        $(list[i].parentNode).insertBefore(
                            $(list[i - 1].parentNode)
                        );
                    }
                    list_item = [];
                } else if (list_item.length === 0) {
                    list_item.push(e);
                }
                last_date = e.innerHTML;
            }
        });
    }

    $(".tfile-item-time").each(function (i, e) {
        if (date === e.innerHTML) {
            $(e).addClass("hide");
        }
        date = e.innerHTML;
    });
    $(".tfile-item-offer-white").find(".s-file").removeClass("s-file--gray");
    $(".tfile-item-offer-gray").find(".s-file").addClass("s-file--gray");
};

export const scrollProductComment = ({totalProductComment, oldData}) => {
    total_product_comment = totalProductComment;
    $(".project-tab.project-tab-product-comment.active .pd-product-comment .mscrollbar--bottom").scroll(function () {
        if ($(this).scrollTop() < 500 && !is_loading_product_comment) {
            if(oldData){
                ajaxLoadMoreProductComment({oldData: oldData});
            }
        }
    });
};

export const addLoadingAnimation = () => {
    $('#loading_animation').empty()
  
    $('#loading_animation').append(`<svg class='loader-example' viewBox='0 0 100 100'>
      <defs>
          <filter id='goo'>
              <feGaussianBlur in='SourceGraphic' stdDeviation='8' result='blur' />
              <feColorMatrix in='blur' mode='matrix' values='1 0 0 0 0
                                                            0 1 0 0 0
                                                            0 0 1 0 0
                                                            0 0 0 25 -8' result='goo' />
              <feBlend in='SourceGraphic' in2='goo' />
          </filter>
      </defs>
      <g filter='url(#goo)' fill='#f0f0f0' stroke='#fcfcfc'>
          <g transform='translate(50, 50)'>
              <g class='circle -a'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='25' cy='50' r='9' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -b'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='25' r='8'  />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -c'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='75' cy='50' r='7' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -d'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='75' r='6' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -e'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='25' cy='50' r='5' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -f'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='25' r='4' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -g'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='75' cy='50' r='3' />
                  </g>
              </g>
          </g>
          <g transform='translate(50, 50)'>
              <g class='circle -h'>
                  <g transform='translate(-50, -50)'>
                      <circle cx='50' cy='75' r='2' />
                  </g>
              </g>
          </g>
      </g>
    </svg>`)
    animationLoading(0)
    $('#loading_animation').removeClass('hide')
}

//default function
const ajaxLoadMoreProductComment = ({oldData}) => {
    if (
        current_load_product_comment < total_product_comment - 1  &&
        !is_loading_product_comment
    ) {
        current_load_product_comment++;
        let last_message_id = $(".load-lasted-message").attr("data-message-id");
        is_loading_product_comment = true;
        should_scroll = false;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/api/load-more-comments",
            data: {
                project_id: project_id,
                type: "project",
                offset: current_load_product_comment,
                last_message_id: last_message_id,
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $(".project-tab-product-comment .mmessage-list").prepend(
                    `<div id="loading_animation" class="loading_animation_container"></div>`
                );
                addLoadingAnimation();
            },
            success: function (data) {
                is_loading_product_comment = false;
                let target = $(
                    ".project-tab.project-tab-product-comment .mmessage-list"
                );

                const currentUserId = oldData?.current_user_id;
                const comments = data?.comments?.map((comment) => {
                    return {
                        ...comment,
                        ...data?.list_same_role?.find(
                            (valueData) =>
                                valueData.comment_id ===
                                comment.comment_id
                        ),
                        fileList: oldData?.comment_files
                            ?.map((file) => {
                                let extraInfo =
                                    data?.list_extra_file_info?.find(
                                        (dataFile) =>
                                            dataFile?.file_id ==
                                            file.file_id
                                    );
                                if (
                                    file.message_id ===
                                    comment.comment_id
                                ) {
                                    return {
                                        ...file,
                                        ...extraInfo,
                                    };
                                } else {
                                    return false;
                                }
                            })
                            .filter(Boolean),
                        folders: oldData?.folder
                            ?.map((folder) => {
                                if (
                                    folder.message_id ===
                                    comment.comment_id
                                ) {
                                    let fileList =
                                        oldData?.comment_files
                                            ?.map((file) => {
                                                let extraInfo =
                                                    data?.list_extra_file_info?.find(
                                                        (
                                                            dataFile
                                                        ) =>
                                                            dataFile?.file_id ==
                                                            file.file_id
                                                    );
                                                if (
                                                    file.folder_id ===
                                                    folder.folder_id
                                                ) {
                                                    return {
                                                        ...file,
                                                        ...extraInfo,
                                                    };
                                                } else {
                                                    return false;
                                                }
                                            })
                                            .filter(Boolean);
                                    return {
                                        ...folder,
                                        children: fileList,
                                    };
                                } else {
                                    return false;
                                }
                            })
                            .filter(Boolean),
                        parentComment: {
                            ...data?.parent_comments?.find(
                                (valueData) =>
                                    valueData.comment_id ===
                                    comment.parent_id
                            ),
                        },
                    };
                });
                const is_pc_device = oldData?.is_pc_device;

                const viewLoadMore = comments
                    ?.map((comment, index) => {
                        return CommentBlock({
                            comment: {
                                ...comment,
                                type: comments.type,
                            },
                            currentUserId,
                            isLast:
                                index ===
                                comments.length - 1,
                            is_pc_device,
                        });
                    })
                    .join("");
                $(".mmessage-list-container.refactor").prepend(viewLoadMore);

                if (
                    target.scrollTop() === 0 &&
                    current_load_product_comment < total_product_comment
                ) {
                    target.animate({ scrollTop: 200 });
                }
                actionShowIconResolved();
                $('.loading_animation_container').remove();
                autoLoadMore(oldData);
            },
            complete: function () {
                calcPositionDropdownComment2();
                calcMoreActionComment($(".prdt .mmessage"));
                clickBtnActionMessage();
                setTimeout(() => {
                    should_scroll = true;
                }, 1500);
            },
        });
    }
};
const initButtonSendProjectComment = (target) => {
    target
        .find(".video-comment-input-text.cs-textarea")
        .off("keyup")
        .keyup(function () {
            let comment_content = $(this).val();
            if (!comment_content) {
                let button = $(this)
                    .parents(".video-comment-message")
                    .find(".button.button--text.button--text-primary");
                button.addClass("button--disabled");
                $(this)
                    .parents(".video-comment-message")
                    .find(".video-comment-input-label")
                    .addClass("button--disabled");
                $(this).parents("cs-textarea-wrapper").height("36px");
            } else {
                let button = $(this)
                    .parents(".video-comment-message")
                    .find(
                        ".button.button--text.button--text-primary.button--disabled"
                    );
                button.removeClass("button--disabled");
                $(this)
                    .parents(".video-comment-message")
                    .find(".video-comment-input-label.button--disabled")
                    .removeClass("button--disabled");
            }
            autoResize(this);
        });

    target
        .find(".video-comment-button-send")
        .off("click")
        .on("click", function () {
            if (!$(this).find("a.button--disabled").length) {
                autoResize(
                    $(this).siblings(".video-comment-input").find("textarea")[0]
                );
                $(this).find("a").addClass("button--disabled");
                let target = $(this);
                let project_id = target
                    .parents(".project-item.active")
                    .attr("data-project-id");
                let comment = $(this)
                    .parents(".video-comment-message")
                    .find(".video-comment-input-text.cs-textarea")
                    .val();
                let parent_id = $(this)
                    .parents(".video-comment-item")
                    .data("parent-id");
                let messenger_attach_element = $(this)
                    .parents(".video-comment-item")
                    .find("input.video-comment-input-attach")
                    .first();
                let messenger_attach = messenger_attach_element.get(0).files[0];
                let duration = messenger_attach_element
                    .first()
                    .attr("data-duration");
                let data = new FormData();
                data.append("file", messenger_attach);
                data.append("duration", duration);
                data.append("type", "project");

                let right =
                    parent_id &&
                    !$(this).parents(".video-comment-item").prev().is(".right")
                        ? ""
                        : " right";
                data.append("right", right);

                let values = {
                    project_id: project_id,
                    comment: comment,
                    parent_id: parent_id,
                };

                for (const property in values) {
                    data.append(property, values[property]);
                }

                $(append_placeholder_comment(!parent_id, false, right, data))
                    .insertBefore(target.parents(".video-comment-item"))
                    .hide()
                    .slideDown(300);
                $(this)
                    .parents(".video-comment-message")
                    .find(".video-comment-input-text.cs-textarea")
                    .val("");
                $(this)
                    .parents(".video-comment-item")
                    .find("input.video-comment-input-attach")
                    .val("");
                $(
                    '[for^="' +
                        $(this)
                            .parents(".video-comment-item")
                            .find("input.video-comment-input-attach")[0].id +
                        '"]'
                ).addClass("button--disabled");
                $(this)
                    .parents(".video-comment-item")
                    .find(".comment__textarea-file")
                    .remove();
                $.ajax({
                    type: "POST",
                    url: "/api/create-and-get-data-comment",
                    data: data,
                    cache: false,
                    processData: false,
                    contentType: false,
                    xhr: function () {
                        var xhr = new window.XMLHttpRequest();
                        if (messenger_attach) {
                            xhr.upload.addEventListener(
                                "progress",
                                function (evt) {
                                    if (evt.lengthComputable) {
                                        let percentComplete =
                                            (evt.loaded / evt.total) * 70;
                                        $(
                                            ".popover .upload-button-wrapper .fill .process"
                                        ).css("width", percentComplete + "%");
                                    }
                                },
                                false
                            );
                        }
                        return xhr;
                    },
                    beforeSend: function (data) {
                        if (messenger_attach) {
                            $(".popover .upload-button-wrapper").css(
                                "display",
                                "flex"
                            );
                            $(".popover .upload-button-wrapper").addClass(
                                "clicked"
                            );
                            $(
                                ".popover .upload-button-wrapper .fill .process"
                            ).css("width", "2%");
                        } else {
                            // toastr.info('コメント作成しています。');
                        }
                    },
                    success: function (data) {
                        if (data.status === "200") {
                            if (data.file) {
                                $(
                                    ".popover .upload-button-wrapper .fill .process"
                                ).css("width", "100%");
                                setTimeout(function () {
                                    $(".popover .upload-button-wrapper")
                                        .removeClass("clicked")
                                        .addClass("success");
                                }, 1000);
                                setTimeout(function () {
                                    $(".popover .upload-button-wrapper")
                                        .removeClass("success")
                                        .css("display", "none");
                                    $(
                                        ".popover .upload-button-wrapper .fill .process"
                                    ).css("width", "0");
                                }, 2000);
                            } else {
                            }
                            let target_dom = target
                                .parents(".video-comment-item")
                                .prev();
                            $(data.comment).insertBefore(
                                target.parents(".video-comment-item")
                            );
                            target_dom.remove();
                            target_dom = target.parents(
                                ".popover.project-video-item"
                            );
                            initProjectComment(target_dom);
                        }
                    },
                    error: function (data) {
                        toastr.error("エラーが発生しました");
                        $(".popover .upload-button-wrapper").removeClass(
                            "clicked"
                        );
                        $(".popover .upload-button-wrapper").css(
                            "display",
                            "none"
                        );
                        $(".popover .upload-button-wrapper .fill .process").css(
                            "width",
                            "0"
                        );
                        let target_dom = target
                            .parents(".video-comment-item")
                            .prev();
                        target_dom.remove();
                        target_dom = target.parents(
                            ".popover.project-video-item"
                        );
                        initProjectComment(target_dom);
                    },
                });
            }
        });
};

function uploadFileS3(file, file_dom, path){
    file_dom.find('.determinate').css('width', '0%');
    let page = getPage(file_dom);
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/get_presigned_url",
        data: {
            'file_name': "storage" + path + "/" + file.name,
            'file_type': file.type,
        },
        success: function (data) {
            let url = data.presigned_post.url;
            let key_file = data.presigned_post.fields["key"];
            var xhr = new XMLHttpRequest();
            xhr.open("POST", url);
            let postData = new FormData();
            for(let key in data.presigned_post.fields){
                postData.append(key, data.presigned_post.fields[key]);
            }
            postData.append('file', file);
            xhr.upload.addEventListener("progress", function (evt) {
                if (evt.lengthComputable) {
                    let percentComplete = (evt.loaded / evt.total) * 70 + '%';
                    file_dom.find('.determinate').css('transition', '0');
                    file_dom.find('.determinate').css('transition', '1s')
                    file_dom.find('.determinate').css('width', percentComplete);
                    if(file_dom.length){
                        if(!file_dom.attr("data-total")){
                            file_dom.attr("data-total", evt.total);
                        }
                        file_dom.attr("data-loaded", evt.loaded);
                    }
                }
            }, false);
            xhr.onreadystatechange = function() {
                if(xhr.readyState === 4){
                    if(xhr.status === 200 || xhr.status === 204){
                        let data = new FormData();
                        let folder_id = "";
                        if(path){
                            folder_id = list_folder_id[path];
                            if(path.includes("/")){
                                path = path.slice(0, path.indexOf("---")) + path.slice(path.indexOf("/"));
                            }else{
                                 path = path.slice(0, path.indexOf("---"));
                            }
                            path += "/";
                        }
                        data.append('file', file);
                        data.append('message_type', page);
                        data.append('key_file', key_file);
                        data.append('file_name', file.name);
                        data.append('folder_id', folder_id);
                        if ($('.mitem.mactive').length) {
                            data.append('offer_id', $('.mitem.mactive').attr('data-offer'));
                        }
                        $.ajax({
                            type: "POST",
                            data: data,
                            contentType: false,
                            processData: false,
                            url: '/upload/create_file',
                            success: function (data) {
                                Object.assign(list_file_id, data);
                                file_dom.find('.determinate').css('width', '100%');
                                file_dom.find('.determinate').attr('data');
                                $('.mcomment-input-text').attr('type_input', 'input');
                                setTimeout(async () => {
                                    if($(document).find('textarea.mcomment-input-text').length){
                                        await doneTyping($(document).find('textarea.mcomment-input-text').val());
                                    }
                                }, 2000);
                                if (data.status === 'error') {
                                    toastr.error('ファイルをアップロード失敗しました。');
                                }
                            },
                            error: function (e) {
                                toastr.error('ファイルをアップロード失敗しました。');
                            }
                        });
                    }
                    else{
                        alert("Could not upload file.");
                    }
                }
            };
            xhr.send(postData);
        }
    })
}

const traverseFileTree = (item, name, flag, folder_id, page) => {
    if (item.isFile && name) {
        if (list_files_folders[name]) {
            list_files_folders[name].push(item.name);
        } else {
            list_files_folders[name] = [item.name]
        }
    } else if (item.isDirectory) {
        let current_path = name;
        if (item.name.trim().length > 128){
            is_exceeded_length = true;
        }
        if (flag) {
            current_path +=  item.name + '/';
        }
        var dirReader = item.createReader();
        let offer_id;
        if ($('.mitem.mactive').length) {
            offer_id = $('.mitem.mactive').attr('data-offer')
        }
        $.ajax({
            type: "POST",
            data: {
                'parent_id': folder_id,
                'name': item.name,
                'full_path': 'storage/' + current_path,
                'message_type': page,
                'offer_id': offer_id
            },
            async: false,
            url: '/upload/create_folder',
            success: function (data) {
                console.log('folder created')
                let folder_pk = data.id;
                list_temp_folder_id[current_path] = folder_pk;
                dirReader.readEntries(function (entries) {
                    for (let i = 0; i < entries.length; i++) {
                        traverseFileTree(entries[i], current_path, 1, folder_pk, page);
                    }
                });
            },
        });
    }
}

const downloadFile = (data, target = 0, typeAction = "") => {
    let type_file;
    if (messenger_page === "messenger_artist" || (messenger_page === "top_page" && $(".pbanner-tab.active[data-show=messenger]").length)) {
        if (user_role !== "admin") {
            type_file = "message_owner";
        } else {
            type_file = "message";
        }
    } else if (messenger_page === "top_page") {
        if ($(".project-tab-product-comment").hasClass("active")) {
            type_file = "project";
        } else {
            type_file = "comment";
        }
    }
    data.append("type_file", type_file);

    if (target) {
        $(target).addClass("loading");
    }
    let offer_id;
    if ($(".mitem.mactive").length) {
        offer_id = $(".mitem.mactive").attr("data-offer");
        data.append("offer_id", offer_id);
    }

    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/api/download-file",
        data: data,
        beforeSend: function (data) {
            if (!typeAction) {
                // toastr.info('ダウンロードのリンクを作成しています。');
            }
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $(target).removeClass("loading").addClass("done");
            if (!typeAction) {
                let a_tag = document.createElement("a");
                a_tag.href = response.url;
                document.body.appendChild(a_tag);
                a_tag.click();
                // toastr.success('ダウンロードを開始しました');
            }
        },
        error: function () {
            if (!typeAction) {
                toastr.error("エラーが発生しました");
                $(target).removeClass("loading done");
            }
        },
    });
};

const DownloadFolder = (folder_id, target) => {
    let data = new FormData();
    data.append("folder_id", folder_id);
    let href = window.location.href;
    let folderDom = $(target).parents(".parent-folder").length
        ? $(target).parent(".parent-folder").parents(".mfolder")
        : $(target).parent(".list-group-item");
    var list_icon_download = folderDom.find(
        ".icon--sicon-download:not(.icon--sicon-folder-download)"
    );
    list_icon_download = list_icon_download.toArray();
    if (!list_icon_download.length) {
        folderDom = folderDom.parents(".group-tree-modal");
        list_icon_download = folderDom.find(
            ".icon--sicon-download:not(.icon--sicon-folder-download)"
        );
        list_icon_download = list_icon_download.toArray();
    }
    let page;
    if (href.includes("/scene/")) {
        page = "scene-comment";
    } else if (href.includes("tab=product-comment")) {
        page = "project-comment";
    } else if (user_role !== "admin") {
        page = "messenger_owner";
    } else {
        page = "messenger";
    }
    data.append("page", page);
    let offer_id;
    if ($(".mitem.mactive").length) {
        offer_id = $(".mitem.mactive").attr("data-offer");
        data.append("offer_id", offer_id);
    }
    $(target).prop("disabled", true);
    $.ajax({
        type: "POST",
        datatype: "json",
        contentType: false,
        processData: false,
        cache: false,
        url: "/get_link_download_folder",
        data: data,
        beforeSend: function (data) {
            // toastr.info('ダウンロードのリンクを作成しています。');
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let links = response.links;
            links = links.split(",");
            var interval = setInterval(function () {
                let a_tag = document.createElement("a");
                a_tag.href = links[0];
                document.body.appendChild(a_tag);
                $(list_icon_download[0]).addClass("done");
                list_icon_download.shift();
                a_tag.click();
                links.shift();
                if (!links.length) {
                    clearInterval(interval);
                    $(target).addClass("done");
                    $(target).prop("disabled", false);
                    // toastr.success('ダウンロードを開始しました');
                }
            }, 1500);
        },
        error: function () {
            toastr.error("エラーが発生しました");
        },
    });
};

const resetInputMessage = (active_offer) => {
    list_file_remove = [];
    mzdrop["sended"] = true;
    mzdrop.removeAllFiles();
    list_file_id = {};
    list_folder_name = [];
    list_file_name = [];
    active_offer.find(".mcommment .mcomment-input-text").val("");
    // active_offer.find('.mcommment .mcomment-input-text').height('20px');
    $(".maction .mcommment-file").remove();
    active_offer.find(".mcomment-send.active").removeClass("active");
    active_offer.find(".mmessage").removeClass("editing");
    active_offer.find(".mmessage").removeClass("reply");
    active_offer.find(".mcomment-send").removeClass("input-editing");
    active_offer.find(".mcommment").removeClass("border-editing");
    active_offer.find(".btn-remove-msg").removeClass("d-block");
    active_offer.find(".block-remove-msg-editing").addClass("d-none");
    if ($(document).width() > maxWidthIpadDevice) {
        $(".prdt .mmessage-list").removeClass("pd-main-message");
    }
    active_offer.find(".mmessage-reply.active").removeClass("active");
    active_offer.find(".mcomment-pin").removeClass("active hide");
    active_offer
        .find(".mcomment-input")
        .removeClass("is-reply")
        .removeClass("is-pin");
    $(".cscene-vertical").removeClass("active");
    if ($(".prdt").length > 0) {
        let commentInputPrdt = active_offer.find(
            ".mcommment .mcomment-input-text"
        );
        commentInputPrdt.height("30px");
        commentInputPrdt.css("overflow", "hidden");
    }
};

const calcPositionDropdownComment = () => {
    let listMessageSent = $(".scene-style .mmessage--sent");
    let listMessageReceived = $(".scene-style .mmessage--received");
    const widthDropdownCmt = 200;
    listMessageSent.each(function (el) {
        let widthMessageMain = $(this).find(".mmessage-main").outerWidth();
        let widthMessageInfo = $(this).find(".mmessage-info").outerWidth();
        let resultLeftDropdown =
            widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find(
                ".mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment"
            );
            dropdownMenu.css("left", `-${resultLeftDropdown + 10}px`);
        }
    });
    const messageListWidth = $(".pd-comment").width();
    listMessageReceived.each(function (el) {
        let widthMessageMain = $(this).find(".mmessage-main").outerWidth();
        let widthMessageInfo = $(this).find(".mmessage-info").outerWidth();
        let resultLeftDropdown =
            widthDropdownCmt - (widthMessageMain + widthMessageInfo);
        if (resultLeftDropdown > 0) {
            let dropdownMenu = $(this).find(
                ".mmessage-info .dropdown-comment-new.dropdown-comment .dropdown-menu.dropdown-menu-comment"
            );
            dropdownMenu.css("left", `-${resultLeftDropdown + 10}px`);
        } else {
            let resultLongReceivedCmt =
                messageListWidth - (widthMessageMain + widthMessageInfo);
            if (resultLongReceivedCmt < widthDropdownCmt) {
                let dropdownMenuReceived = $(this).find(
                    ".mmessage-info .dropdown.dropdown-comment-new.dropdown-comment-received .dropdown-menu.dropdown-menu-comment"
                );
                dropdownMenuReceived.css(
                    "left",
                    `-${widthDropdownCmt - resultLongReceivedCmt}px`
                );
            }
        }
    });
};

const hoverDropdownMessage = () => {
    let btnReplyMsg = $(".prdt .li-reply-message");
    let btnEditMsg = $(".prdt .li-edit-message");
    let btnResolveMsg = $(".prdt .li-resolve-message");
    btnReplyMsg.hover(
        function (el) {
            el.currentTarget.querySelectorAll(
                ".mmessage-reply .txt-reply-comment"
            )[0].style.color = "#009ACE";
            el.currentTarget.querySelectorAll(
                ".mmessage-reply .img-reply-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-reply-active.svg')";
        },
        function (el2) {
            el2.currentTarget.querySelectorAll(
                ".mmessage-reply .txt-reply-comment"
            )[0].style.color = "#000";
            el2.currentTarget.querySelectorAll(
                ".mmessage-reply .img-reply-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-reply.svg')";
        }
    );

    btnEditMsg.hover(
        function (el) {
            el.currentTarget.querySelectorAll(
                ".mmessage-edit .txt-edit-comment"
            )[0].style.color = "#009ACE";
            el.currentTarget.querySelectorAll(
                ".mmessage-edit .img-edit-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-edit-active.svg')";
        },
        function (el2) {
            el2.currentTarget.querySelectorAll(
                ".mmessage-edit .txt-edit-comment"
            )[0].style.color = "#000";
            el2.currentTarget.querySelectorAll(
                ".mmessage-edit .img-edit-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-edit.svg')";
        }
    );

    btnResolveMsg.hover(
        function (el) {
            el.currentTarget.querySelectorAll(
                ".mmessage-resolve .txt-item-comment"
            )[0].style.color = "#009ACE";
            el.currentTarget.querySelectorAll(
                ".mmessage-resolve .img-resolve-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-resolve-active.svg')";
        },
        function (el2) {
            el2.currentTarget.querySelectorAll(
                ".mmessage-resolve .txt-item-comment"
            )[0].style.color = "#000";
            el2.currentTarget.querySelectorAll(
                ".mmessage-resolve .img-resolve-comment"
            )[0].style.background =
                "url('/static/images/scene-detail/icon-resolve.svg')";
        }
    );
};

const hoverBtnActionMessage = () => {
    let listBtnAction = $(".dropdown.dropdown-comment-new").parent().parent();
    listBtnAction.hover(
        function (el) {
            $(this)
                .find(".show-more-action-message:not(.show-action)")
                .css("display", "flex");
        },
        function (el2) {
            $(this)
                .find(".show-more-action-message:not(.show-action)")
                .css("display", "none");
        }
    );
};

const resetFormContract = (open_modal = false) => {
    file_offer = (function () {
        return;
    })();
    real_name_offer = "";
    let productId = $(".project-item.active").data("project-id");
    $.ajax({
        type: "GET",
        url: "/form_upload_and_plans/get_current_form_upload_and_plan",
        data: {
            product_id: productId,
        },
        datatype: "json",
        success: function (data) {
            appendCurrentFormContractAndPlan(data, data.file_type);
        },
        error: function (data) {
            console.log(data);
        },
    });
};

const show_unresolved_comment = (target) => {
    let target_parents = target.parents(".video-item-comment-content");
    let resolve_switch = target_parents
        .siblings(".video-item-comment-top")
        .find(".button-switch input");
    let animate = !resolve_switch.is(":checked");

    if (animate) {
        target.slideDown(300);
    }
    target.removeClass("resolved");
    target.find(".video-comment-resolve")[0].innerText = "解決";
    while (target.next().is(".sub-item")) {
        target = target.next();
        if (animate) {
            target.slideDown(300);
        }
        target.find(".video-comment-resolve")[0].innerText = "解決";
        target.removeClass("resolved");
    }
};

const hide_resolved_comment = (target) => {
    let target_parents = target.parents(".video-item-comment-content");
    let resolve_switch = target_parents
        .siblings(".video-item-comment-top")
        .find(".button-switch input");
    let animate = !resolve_switch.is(":checked");

    if (animate) {
        target.hide(300);
    }

    target.addClass("resolved");
    target.find(".video-comment-resolve")[0].innerText = "再開";
    while (target.next().is(".sub-item")) {
        target = target.next();
        if (animate) {
            target.hide(300);
        }
        target.find(".video-comment-resolve")[0].innerText = "再開";
        target.addClass("resolved");
    }
};

const actionShowIconResolved = () => {
    let toggleDom = $(".pd-product-comment #order-monthy");
    if (!toggleDom.length) {
        toggleDom = $(".pd-scene-title-detail #switch-checkbox-comment");
    }
    let checked = toggleDom.is(":checked");

    let received_comments = $(".mmessage--received");
    let received_status = received_comments.find(".mmessage-status");
    let received_action = received_comments.find(".mmessage-action");
    if (checked) {
        $(".pd-scene-title-detail")
            .removeClass("show-comment-unresolved")
            .addClass("show-comment-all");
        if (
            !toggleDom
                .parents(".pd-section__content")
                .find(".mmessage-list")
                .hasClass("view_only")
        ) {
            received_comments.each(function () {
                let message_active = $(this);
                if (message_active.height() <= 75) {
                    message_active.find(".mmessage-status").addClass("hide");
                } else {
                    message_active.find(".mmessage-info").css({
                        height: message_active.find(".mmessage-main").height(),
                    });
                }
            });
            received_action.removeClass("hide");
        }
    } else {
        $(".mmessage.resolved .s-audio-control.active").each(function () {
            $(this).trigger("click");
        });
        received_status.removeClass("hide");
        received_action.addClass("hide");
        $(".pd-scene-title-detail")
            .removeClass("show-comment-all")
            .addClass("show-comment-unresolved");
    }
};

export const newWavesurferInit = () => {
    let current_length = wavesurfer_arr.length;
    let list_audio = $('.s-audio-waveform');
    if (list_audio.length > 0) {
        if (!$('.pd-section-file').hasClass('active')) {
            $('.cscene__version-horizontal').addClass('active')
        }
        let step = 0;
        for (let i = 0; i < list_audio.length ; i++) {
            const this_wave = $(list_audio[i]);
            if (this_wave.find('wave').length < 1) {
                const $mplayer = this_wave.closest('.s-audio');
                if ($mplayer.length > 0 && $mplayer.find('.s-audio-source').length > 0) {
                    const link = $mplayer.find('.s-audio-source').attr('data-link');
                    const waveColor = $mplayer.find('.s-audio-source').attr('data-waveColor');
                    const progressColor = $mplayer.find('.s-audio-source').attr('data-progressColor');
                    const peaks_loaded = $mplayer.find('.s-audio-source').attr('data-peaks-loaded');
                    let file_id = $mplayer.parents('.mmessage').find('.mmessenger--audio-wave').attr('data-file-id');
                    if (!file_id) {
                        file_id = $mplayer.parents('.minfo-file_info').attr('data-file-id');
                    }
                    const wavesurfer = WaveSurfer.create({
                        container: list_audio[i],
                        waveColor: "#a7a8a9",
                        progressColor: '#009ace',
                        cursorColor: "#009ace",
                        barWidth: 2,
                        barRadius: 3,
                        cursorWidth: 1,
                        barGap: 1,
                        barHeight: 0.6,
                        pixelRatio: 1,
                        mediaControls: false,
                        height: 64,
                        backend: 'MediaElement',
                        fillParent: true,
                        hideScrollbar: true,
                        splitChannels: true,
                        plugins: [
                            WaveSurfer.cursor.create({
                                showTime: true,
                                opacity: 1,
                                customShowTimeStyle: {
                                    'background-color': '#000',
                                    color: '#fff',
                                    padding: '2px',
                                    'font-size': '10px'
                                },
                                formatTimeCallback: formatTimeCallback,
                            })
                        ]
                    });

                    wavesurfer_arr[current_length + i] = wavesurfer;

                    let cmt_container = this_wave.parents(".s-audio");
                    cmt_container.attr('data-wavesurfer', current_length + i);
                    let array_peaks = [];
                    if ($mplayer.hasClass('s-audio--white')){
                        let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                        if (peaks_loaded && small_peaks.length > 1) {
                            if (wavesurfer.backend?.buffer?.numberOfChannels < 2){
                                array_peaks = peaks_loaded.split(" ").map(Number);
                                wavesurfer.load(link, array_peaks, 'none');
                                wavesurfer.loaded = false;
                            }else{
                                wavesurfer.load(link);
                                updatePeaksWave(wavesurfer);    
                            }
                        } else {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer);
                        }
                    }else{
                        if (peaks_loaded) {
                            if (wavesurfer.backend?.buffer?.numberOfChannels < 2){
                                array_peaks = peaks_loaded.split(" ").map(Number);
                                wavesurfer.load(link, array_peaks, 'none');
                                wavesurfer.loaded = false;
                            }else{
                                wavesurfer.load(link);
                                updatePeaksWave(wavesurfer);    
                            }
                        } else {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer);
                        }
                    }

                    // Play on audio load

                    $mplayer.find('.s-audio-control:not(.video-pin-time)').off().on('click', function (e) {
                        const peaks_loaded = $mplayer.find('.s-audio-source').attr('data-peaks-loaded');
                        const array_peaks = peaks_loaded.split(" ").map(Number);
                        if (!array_peaks.length) {
                            return;
                        }
                        if (!$(this).hasClass('video-pin-time')) {
                            if (!wavesurfer.loaded) {
                                if ($mplayer.hasClass('s-audio--white') && $('.cscene__version-horizontal').hasClass('active')){
                                    let small_peaks = $mplayer.find('.s-audio-source').attr('data-small-peaks');
                                    if (small_peaks.length > 1){
                                        let array_small_peaks = small_peaks.split(" ").map(Number);
                                        wavesurfer.load(link, array_small_peaks);
                                    }
                                }else{
                                    wavesurfer.load(link, array_peaks);
                                }
                            }
                            let playing = false;
                            if(wavesurfer.isPlaying()) {
                                playing = true;
                            }
                            stop_video_audio();
                            if(!playing) {
                                wavesurfer.playPause();
                            }
                        }
                    });

                    wavesurfer.on('waveform-ready', function () {
                        if (!wavesurfer.loaded) {
                            wavesurfer.loaded = true;
                        }
                        var duration = wavesurfer.getDuration();
                        $mplayer.find('.s-audio-time').text('00:00');
                    });

                    // Display player time
                    wavesurfer.on('audioprocess', function () {
                        if (wavesurfer.getCurrentTime() > 0) {
                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));
                        } else {
                            $mplayer.find('.s-audio-time').text('00:00');
                        }
                    });

                    // Check Playpause button
                    wavesurfer.on('pause', function () {
                        $mplayer.removeClass('active');
                        $mplayer.find('.s-audio-control').removeClass('active');

                        let activeAudio = parseInt($($mplayer[0]).attr("data-wavesurfer"));
                        if($('.mmessage-reply').hasClass('active') || $('.mmessage-edit').hasClass('active')) {
                            setColorActive(wavesurfer_arr[activeAudio]);
                        }
                        else {
                            setColorInActive(wavesurfer_arr[activeAudio]);
                        }
                        updatePinAudio(wavesurfer);
                    });

                    wavesurfer.on('play', function () {
                        $mplayer.addClass('active');
                        $mplayer.find('.s-audio-control').addClass('active');

                        let activeAudio = parseInt($($mplayer[0]).attr("data-wavesurfer"));
                        if (!$mplayer.hasClass('s-audio--white')) {
                            setColorActive(wavesurfer_arr[activeAudio]);
                        }

                        if($mplayer.find('.s-audio-control.pin-time-audio').hasClass('active')) {
                            document.onkeyup = function(e) {
                                if (!$(".mcomment-input-text.mcomment-autoExpand").is(":focus")) {
                                    const keyCode = e.which;
                                    let startTime = 0;
                                    let currentTime = parseInt(wavesurfer.getCurrentTime());
                                    let durationTime = parseInt(wavesurfer.getDuration());
                                    let now = startTime;

                                    if (keyCode === 37) {
                                        now = currentTime - 5;
                                        if(now < 0) {
                                            wavesurfer.playPause();
                                            wavesurfer.setCurrentTime(0);
                                        }
                                        else {
                                            wavesurfer.setCurrentTime(now);
                                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));

                                        }
                                    }
                                    else if (keyCode === 39) {
                                        now = currentTime + 5;
                                        if (now > durationTime) {
                                            wavesurfer.playPause();
                                            wavesurfer.setCurrentTime(0);
                                        }
                                        else {
                                            wavesurfer.setCurrentTime(now);
                                            $mplayer.find('.s-audio-time').text(convertSecondsToTime(wavesurfer.getCurrentTime()));
                                        }
                                    }
                                    else if (keyCode === 32) {
                                        wavesurfer.playPause();
                                    }
                                }
                            }
                        }
                    });

                    wavesurfer.on('seek', function () {
                        if (!wavesurfer.loaded) {
                            wavesurfer.load(link, array_peaks);
                        }
                        updatePinAudio(wavesurfer)
                    });

                    hoverWavesurfer();

                    // backward audio 5s
                    $mplayer.find('.backward-audio').on('click', function (e){
                        wavesurfer.skip(-5)
                    });

                    // forward audio 5s
                    $mplayer.find('.forward-audio').on('click', function (e){
                        wavesurfer.skip(5)
                    });
                }
            }
        }
    }
}

const updatePeaksWave = (wavesurfer) => {
    wavesurfer.on('waveform-ready', function () {
        let peaks = wavesurfer.backend.mergedPeaks;
        let peaks_string = "";
        if (peaks) {
            peaks_string = peaks.join(" ");
        }
        let values = {};
        let wave_index = wavesurfer_arr.indexOf(wavesurfer).toString();
        let dom_container = $('.s-audio[data-wavesurfer^=' + wave_index + ']');
        if (dom_container.length > 0) {
            if (dom_container.hasClass('s-audio--white')) {
                let scene_id = dom_container.attr("data-scene-id");
                let small_peaks = wavesurfer.backend.getPeaks(64);
                small_peaks = small_peaks.join(" ");
                let wave_element = $(wavesurfer.container).parent().find(".s-audio-source");
                wave_element.attr("data-peaks-loaded", peaks_string);
                wave_element.attr("data-small-peaks", small_peaks);
                values = {
                    "scene_id": scene_id,
                    "peaks": peaks_string,
                    "small_peaks": small_peaks,
                    'type': 'variation'
                }
            } else {
                let cmt_id = dom_container.parents('.mmessenger--audio-wave').attr("data-file-id");
                if (dom_container.parents('.pd-product-comment').length) {
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'project'
                    };
                } else  if (dom_container.parents('.pd-scene-title-detail').length){
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'scene'
                    };
                } else if (user_role === 'admin') {
                    values = {
                        "comment_id": cmt_id,
                        "peaks": peaks_string,
                        'type': 'messenger_owner'
                    };
                }
            }
            $.ajax({
                type: "POST",
                url: "/top/update_comment",
                data: values,
                dataType: 'json',
                success: function (data) {
                    console.log("success");
                    let message_dom;
                    if (data.file_id) {
                        message_dom = $('.mmessenger--audio-wave[data-file-id^=' + data.file_id + ']');
                    } else {
                        message_dom = $('.s-audio--white[data-scene-id^=' + data.scene_id + ']');
                    }
                    if (message_dom.length) {
                        message_dom.find('.s-audio-source').attr('data-peaks-loaded', data.peaks_loaded)
                    }
                },
                error: function (e) {
                    console.log(e);
                }
            });
        }

    });
}

const stop_video_audio = () => {
    $("video").each((i, e) => e.pause());
    $(".s-audio-control").each(function () {
        $(this).removeClass("active");
        $(this)
            .closest(".messenger-content")
            .find(".s-audio")
            .removeClass("active");
        $(this)
            .closest(".mmessage-content")
            .find(".s-audio")
            .removeClass("active");
    });
    let ws_arr;
    ws_arr = wavesurfer_arr;
    for (let i = 0; i < ws_arr.length; i++) {
        if (ws_arr[i]) {
            if (ws_arr[i].isPlaying()) {
                ws_arr[i].playPause();
            }
        }
    }
};

const setColorActive = (wavesurfer) => {
    if (wavesurfer) {
        // 白い背景でも見えるように青系の色に変更
        wavesurfer.setWaveColor("rgba(0, 154, 206, 0.3)"); // #009ace の30%透明度
        wavesurfer.setProgressColor("#009ace"); // 青色
    }
};

const setColorInActive = (wavesurfer) => {
    if (wavesurfer) {
        wavesurfer.setWaveColor("rgba(83, 86, 90, 0.3)");
        wavesurfer.setProgressColor("#53565A");
    }
};

const updatePinAudio = (wavesurfer) => {
    let index_wave = wavesurfer_arr.indexOf(wavesurfer);
    let parent_wave = $(".s-audio[data-wavesurfer^=" + index_wave + "]");
    if (parent_wave.hasClass("s-audio--white")) {
        let pins = $(".pd-comment__main .mmessage-component .mcomment-pin");
        pins.each(function (i, e) {
            if ($(e).is(".active")) {
                $(e)
                    .find(".pin-icon-time")
                    .html(msToTime(wavesurfer.getCurrentTime()));
                $(".mcomment-input-title").html(
                    '<i class="icon icon--sicon-pin"></i>' +
                        "<span>" +
                        msToTime(wavesurfer.getCurrentTime()) +
                        "</span>"
                );
            }
        });
    }
};

const convertSecondsToTime = (seconds) => {
    if (!isNaN(seconds)) {
        var time;
        time = new Date(seconds * 1000).toISOString().substr(11, 8);

        var time_arr = time.split(":");

        if (time_arr.length > 2 && time_arr[0] == "00") {
            time = time_arr[1] + ":" + time_arr[2];
        }

        return time;
    }
};

const hoverWavesurfer = () => {
    $(document).on("mouseenter mouseleave", ".s-audio-waveform", function (e) {
        if (e.type === "mouseenter") {
            let index_wave = parseInt(
                $(this).parents(".s-audio--audio-wave").attr("data-wavesurfer")
            );
            let wavesurfer = wavesurfer_arr[index_wave];
            if (wavesurfer && !wavesurfer.loaded) {
                let link = $(this)
                    .parents(".s-audio--audio-wave")
                    .find(".s-audio-source")
                    .attr("data-link");
                let peaks_loaded = $(this)
                    .parents(".s-audio--audio-wave")
                    .find(".s-audio-source")
                    .attr("data-peaks-loaded");
                let array_peaks = [];
                let $mplayer = $(this).parents(".s-audio");
                if ($mplayer.hasClass("s-audio--white")) {
                    if (!peaks_loaded) {
                        return;
                    }
                    if ($(".cscene__version-horizontal").hasClass("active")) {
                        let small_peaks = $mplayer
                            .find(".s-audio-source")
                            .attr("data-small-peaks");
                        if (small_peaks) {
                            let array_small_peaks = small_peaks
                                .split(" ")
                                .map(Number);
                            wavesurfer.load(link, array_small_peaks);
                        }
                    } else {
                        let array_peaks = peaks_loaded.split(" ").map(Number);
                        wavesurfer.load(link, array_peaks);
                    }
                } else {
                    if (peaks_loaded && !wavesurfer.loaded) {
                        array_peaks = peaks_loaded.split(" ").map(Number);
                        if (peaks_loaded.length < 1024) {
                            wavesurfer.load(link);
                            updatePeaksWave(wavesurfer);
                        } else {
                            wavesurfer.load(link, array_peaks);
                        }
                    } else if (!wavesurfer.loaded) {
                        wavesurfer.load(link);
                        updatePeaksWave(wavesurfer);
                    }
                }
            }
        }
    });
};

const autoResize = (el) => {
    setTimeout(function () {
        let height;
        el.style.cssText = "height:auto; padding:0";
        height = el.scrollHeight < 200 ? el.scrollHeight : 200;
        el.style.cssText = "height:" + height + "px";
    }, 0);
};

export const getWeekNumber = (date) => {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

export const initSocket = (socket_id, userRole, is_pc) => {
    is_pc_device = is_pc;
    user_role = userRole
    let current_chatsocket = dict_chatsocket.get(socket_id);
    let checkExist = false;
    if (!current_chatsocket || current_chatsocket.readyState === '3') {
        current_chatsocket = new WebSocket(
            socket_url + '/ws/messenger/' + socket_id);
        dict_chatsocket.set(socket_id, current_chatsocket);

        current_chatsocket.onmessage = function (e) {
            if (!checkExist){
                let data = JSON.parse(e.data);
                let offer_id = data.event.offer_id;
                let target_offer;
                let role = user_role;
                let get_offer = $('.offer-' + offer_id);
                let type_message = data.event.type_message;
                let thread_name = data.event.thread_name;
                if (thread_name) {
                    thread_name = escapeHtml(thread_name)
                }
                let sender_id = data.event.sender_id;
    
                let current_receiver = '';
                if (offer_id) {
                    if ($('.mitem.mactive').length > 0) {
                        current_receiver = $('.mitem.mactive').data('offer');
                    }
    
                    target_offer = $('.mitem[data-offer=' + offer_id + ']');
    
                } else if (type_message === 'project') {
                    offer_id = data.event.product_id;
                    current_receiver = $('.project-item.active').find('.pd-product-comment').attr('data-product-id');
                } else {
                    offer_id = data.event.scene_title_id;
                    current_receiver = $('.pd-scene-title-detail').attr('data-scene-title-id');
                }
    
                if (current_receiver) {
                    current_chatsocket = current_chatsocket.toString();
                }
                let infor_offer = $('.offer-' + offer_id + '_infor');
                let offer_socket = $('.mscene.mitem[data-offer=' + offer_id + ']');
                if (offer_socket.length) {
                    offer_role = offer_socket.attr('data-role-offer');
                }
    
                let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                switch (data.event.action) {
                    case 'new_message':
                        removeSystemMessage(data);
                        socketNewMessage(target_offer, get_offer, current_receiver, offer_id, data, socket_id, regex);
                        updateBatchNumberStorage(data);
                        if ($('.owner').hasClass('prdt')) {
                            calcMoreActionComment($('.prdt .mmessage'));
                        } else {
                            calcMoreActionComment($('.scene-style .mmessage'));
                        }
                        clickBtnActionMessage();
                        break;
                    case 'seen_comment':
                        socketSeenComment(data, current_receiver, offer_id);
                        if ($('.scene-style').length > 0) {
                            calcMoreActionComment($('.scene-style .mmessage'));
                        } else if ($('.main-talk-room').length > 0) {
                            calcMoreActionComment($('.main-talk-room .mmessage'));
                        }
                        break;
                    case 'update_message':
                        socketUpdateMessage(data, regex, socket_id, infor_offer, get_offer);
                        removeDuplicatedDate();
                        updateBatchNumberStorage(data)
                        if ($('.owner').hasClass('prdt')) {
                            calcMoreActionComment($('.prdt .mmessage'));
                        } else {
                            calcMoreActionComment($('.scene-style .mmessage'));
                        }
                        clickBtnActionMessage();
                        break;
                    case 'delete_message':
                        socketDeleteMessage(get_offer, offer_id, data, infor_offer);
                        removeDuplicatedDate();
                        updateBatchNumberStorage(data);
                        removeSystemMessage(data);
                        break;
                    case 'resolve_message':
                        if (data.event.type_message === 'project') {
                            get_offer = $('.pd-product-comment[data-product-id=' + data.event.project_id + ']');
                        } else {
                            get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');
                        }
                        if (get_offer.length > 0) {
                            let message_id = data.event.message_id;
                            let message = get_offer.find('.mmessage[data-message-id=' + message_id + ']');
                            // let message_child = get_offer.find('.mmessage[data-parent-id=' + message_id + ']');
                            if (data.event.resolved) {
                                if (get_offer.hasClass('show-comment-unresolved')) {
                                    if (message.find('.s-audio-control.active').length) {
                                        message.find('.s-audio-control.active').trigger('click')
                                    }
                                    // if (message_child.find('.s-audio-control.active').length) {
                                    //     message_child.find('.s-audio-control.active').trigger('click')
                                    // }
                                    message.fadeOut('slow');
                                    // message_child.fadeOut('slow');
                                }

                                setTimeout(function () {
                                    message.addClass('resolved');
                                    // message_child.addClass('resolved');
                                }, 300);

                                message.find('.mmessage-resolve ').addClass('mmessage-resolved');
                                // message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');

                            } else {
                                message.css('display', 'flex');
                                // message_child.css('display', 'flex');
                                message.find('.mmessage-resolve ').removeClass('mmessage-resolved');
                                // message_child.find('.mmessage-resolve ').removeClass('mmessage-resolved');
                                message.removeClass('resolved');
                                // message_child.removeClass('resolved');
                            }

                        }
                        break;
                    case 'download_file':
                        socketDownloadFile(get_offer, data);
                        break; 
                    case 'draft_message':
                        list_file_id = JSON.parse(data.event.list_file_id);
                        list_folder_id = JSON.parse(data.event.list_folder_id);
                        if(data.event.id_time === id_time || data.event.offer_id !== $(document).find('.mitem.mactive').attr('data-offer')){
                            return;
                        }
                        valInput = data.event.message;
                        if(!data.event.message && !Object.keys(list_file_id).length && !Object.keys(list_folder_id).length){
                            $(document).find('.mcomment-top').hide();
                            $(document).find('.mcomment-input-text').attr('type_input', '');
                            $(document).find('.mcomment-send.active').removeClass('active');
                            return;
                        }
                        $(document).find('.mcomment-bottom').trigger('click');
                        $(document).find('.mcomment-input-text').val(data.event.message);
                        $(document).find('.mcomment-input-text').focus();
                        var file_conatiner_dom = $(document).find('.mattach-previews');
                        file_conatiner_dom.empty();
                        for(var i = 0; i < data.event.file_name.length; i++ ){
                            file_conatiner_dom.append(`<div class="mattach-template collection-item item-template" data-total="" data-loaded="">
                            <div class="mattach-info" data-dz-thumbnail=""><div class="mcommment-file"><div class="progress"><div class="determinate" style="width: 100%; transition: all 1s ease 0s;" data-dz-uploadprogress=""></div></div>
                            <div class="mcommment-file__name" data-dz-name="">${data.event.file_name[i]}</div>
                            <div class="mcommment-file__delete" href="#!" data-dz-remove=""><span class="progress-text"></span><i class="icon icon--sicon-close"></i></div></div></div></div>`)
                        }
                        $(document).find('.mcomment-input-text').attr('type_input', '');
                        $(document).find('.mcomment-send').addClass('active');
                        break;
                    case 'acr_result':
                        console.log('i got it', data.event.file);
                        let target = $('.tfile-infor.tfile-type.tfile-type--file[data-file-id=' + data.event.file + '] .acr-result-icon, .mfolder__sub[data-file-id=' + data.event.file + '] .acr-result-icon')
                        target.removeClass('hide deactive')
                        target.addClass(data.event.acr_class)
                        break;  
                    case 'seen':
                        socketSeenMessage(get_offer, offer_id, data);
                        break;
    
                    // case 'update_review':
                    //     socketReviewOffer(socket_id, data);
                    //     break;
    
    
                //     case 'offer_accept':
                //         updateListOffer(data);
                //         let offerDom = $('.mitem[data-offer^=' + offer_id + ']');
                //         offerDom.find('.mscene__label .slabel').remove();
                //         get_offer.find('.mmessage-confirm').remove();
                //         offerDom.addClass('mprogress');
                //         infor_offer.find('.minfo-contract').html(data.event.infor_html);
                //         offer_role = offerDom.hasClass('offer-admin') ? ROLE_ADMIN : ROLE_CREATOR;
                //         if (offer_role === ROLE_ADMIN) {
                //             // toastr.success(thread_name + 'というオファーが請けました。');
                //             if (offerDom.hasClass('mactive')) {
                //                 $('.button-delete_offer').remove()
                //             }
                //         }
                //         updateOfferStatusInfo(data);
                //         $('.messenger-file-component-container .messenger-file-component-content.open-preview-artist-contract-approve-modal[data-file-id='+ data.event.offer_creator_id +']').parents('.messenger-file-component-container').remove()
    
                //         break;
    
                //     case 'checked_offer':
                //         socketCheckedOffer(data, offer_id, infor_offer, thread_name);
                //         break;
    
                //     case 'offer_closed':
                //         socketClosedOffer(data, offer_id, infor_offer);
                //         break;
    
                //     case 'create_offer':
                //         var project_id = $('.project-item.active').attr('data-project-id');
                //         if (data.event.project_id !== project_id) {
                //             return
                //         }
                //         let budget = data.event.budget_or_reward;
                //         let offer_side = data.event.offer_side;
    
                //         let item_html = data.event.item_html;
                //         let listItems = $(`.mlist .list--offers-project .mitem[data-type-offer="messenger_artist"][data-offer-side=${offer_side}]`)
                //         if (listItems.length > 0) {
                //             let lastItem = listItems.last()
                //             if (lastItem.data('reward') > budget) {
                //                 $(item_html).insertAfter(lastItem)
                //             } else {
                //                 listItems.each(function (index, element) {
                //                     var itemBudgetOrReward = $(element).data('reward')
    
                //                     if (budget >= itemBudgetOrReward) {
                //                         $(item_html).insertBefore($(element));
                //                         return false;
                //                     }
    
                //                 });
                //             }
                //         } else {
                //             if(offer_side === 'creator')
                //                 if($(`.mlist .list--offers-project .mitem[data-type-offer="messenger_owner"]`).length > 0)
                //                     $(item_html).insertAfter($(`.mlist .list--offers-project .mitem[data-type-offer="messenger_owner"]`).last())
                //                 else
                //                     $('.mlist .list--offers-project').prepend(item_html);
                //             else
                //                 $('.mlist .list--offers-project').append(item_html);
                //         }
                //         if (socket_id === data.event.creator_id) {
                //             // toastr.success(thread_name + 'という新しいオファーが届いています。');
                //         }
                //         updateCountUnreadOfferComment(data.event, target_offer, get_offer);
                //         break;
    
                //     case 'edit_offer':
                //         $('.mitem[data-offer^=' + offer_id + ']').find('.mscene__name .thread-name').html(thread_name);
    
                //         let offer = $('.offer-' + offer_id + '_infor');
                //         updateListOffer(data);
    
                //         if (offer.length > 0) {
                //             offer.find('.infor-offer').html(data.event.infor_offer);
    
                //             let mess_id = data.event.message_id;
                //             let message_dom = $('.mmessage[data-message-id=' + mess_id + ']');
                //             if (message_dom.length > 0) {
                //                 if (message_dom.hasClass('mmessage--received')) {
                //                     message_dom.html(data.event.message_received_html);
                //                 } else {
                //                     message_dom.html(data.event.message_send_html);
                //                 }
                //                 if (message_dom.find('.s-text').length > 0) {
                //                     let mess = $('.mmessage[data-message-id=' + mess_id + ']').find('.s-text').html();
                //                     let message_regex = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
                //                     $('.mmessage[data-message-id=' + mess_id + ']').find('.s-text').html(message_regex);
                //                 }
                //                 updateFileInMessage(data, mess_id);
                //                 removeDuplicatedDate();
                //             }
                //         }
    
                //         if(data.event.offer_creator_id) {
                //            let button_contract =  $('.messenger-file-component-container .messenger-file-component-content[data-file-id='+ data.event.offer_creator_id +']').not('.btn-accept-offer').parent('.messenger-file-component-container')
                //            let pinned_button_contract = $('.maction.maction-new .messenger-file-component-container .messenger-file-component-content[data-file-id='+ data.event.offer_creator_id +']').parent('.messenger-file-component-container')
                //            if(button_contract.length) {
                //                 $(data.event.contract_html).insertBefore(button_contract);
                //                 button_contract.remove();
                //            }
                //            if(pinned_button_contract.length) {
                //                 $(data.event.btn_contract_html).insertBefore(pinned_button_contract);
                //                 pinned_button_contract.remove();
                //            }
                //         }
    
                //         break;
                //     case 'update_rating':
                //         let updated_date = data.event.message;
                //         for (let [key, value] of Object.entries(updated_date)) {
                //             let product_scene = $(`[data-product-scene-id="${key}"]`);
                //             value = value.split(",");
                //             if(product_scene.length){
                //                 let rating_text = product_scene.find(".pd-chapter__rating");
                //                 rating_text.html(`${value[0]}(${value[1]})`);
                //                 // update star
                //                 let star = product_scene.find(".pd-chapter__title .average-star");
                //                 if(parseInt(value[0])){
                //                     star.addClass("selected");
                //                     let number_star = "star-" + String(parseInt(value[0]));
                //                     star.find("a").removeClass("active");
                //                     star.find("." + number_star).addClass("active");
                //                     star.attr("data-rating", parseInt(value[0]));
                //                 }else{
                //                     star.removeClass("selected");
                //                     star.find("a").removeClass("active");
                //                 }
                //             }else{
                //                 let scene = $(`.project-delivery-item-content[data-scene-title-id="${key}"]`);
                //                 if(scene.length){
                //                     let star = scene.find(".stars");
                //                     if(parseInt(value[0])){
                //                         star.addClass("selected");
                //                         let number_star = "star-" + String(parseInt(value[0]));
                //                         star.find("a").removeClass("active");
                //                         star.find("." + number_star).addClass("active");
                //                         star.attr("data-rating", parseInt(value[0]));
                //                     }else{
                //                         star.removeClass("selected");
                //                         star.find("a").removeClass("active");
                //                     }
                //                 }
                //             }
                //         }
    
                //         updateProjectVideoWattingFeedback(data.event);
                //         break;
    
                //     case 'offer_reject':
                //         sub_msg = offer_role === ROLE_CREATOR ? 'を削除されました。' : 'を削除しました。';
                //         // toastr.success('オファー' + thread_name + sub_msg);
                //         socketRejectOffer(data);
                //         break;
                //     case 'update_scene':
                //         updateProjectVideoWattingFeedback(data.event);
                //         break;
                //     case 'checked_plan':
                //         checkedContractOffer(data);
                //         break;
                //     case 'checked_contract':
                //         checkedContractOffer(data);
                //         break;
                //     case 'checked_done_offer':
                //         checkedDoneOffer(data);
                //         removeSystemMessage(data);
                //         break;
                //     case 'upload_file_offer':
                //         socketUploadFileOffer(data);
                //         removeDuplicatedDate();
                //         removeSystemMessage(data);
                //         break;
                //     case 'delete_offer_product':
                //         socketDeleteOfferProduct(data);
                //         break;
                //     case 'payment_offer':
                //         socketPaymentOffer(data);
                //         break;
                //     case 'show_menu':
                //         updateMenuProjectDetail(data.event);
                //         break;
                //     case 'add_first_producer':
                //         updateMessageWhenAddFirstProducer(data.event);
                //         break;
                //     case 'delete_system_message':
                //         removeSystemMessage(data);
                //         break;
                //     default:
                //         break;
                }
            }
        };
        current_chatsocket.onclose = function (e) {
            console.error('Chat socket closed!');
            dict_chatsocket.delete(socket_id);
            current_chatsocket = undefined;
            // initSocket(socket_id)
        };
    }
}

const socketDownloadFile = (get_offer, data) => {
    var project_id = $('.project-item.active').attr('data-project-id');
    if (data.event.project_id !== project_id) {
        return
    }
    updateBatchNumberStorage(data);

    let type_file_download = data.event.type_file_download;
    if (type_file_download === 'project') {
        get_offer = $('.pd-product-comment[data-product-id^=' + data.event.project_id + ']');
    } else if (['scene', 'scene_comment'].includes(type_file_download)) {
        get_offer = $('.pd-scene-title-detail[data-scene-title-id^=' + data.event.scene_title_id + ']');
    } else if (['message_owner', 'message'].includes(type_file_download)) {
        get_offer = $('.offer-' + data.event.offer_id + '_infor')
    }
    if (get_offer.length > 0) {
        let file_dom;
        let file_dom2;
        if (['project', 'scene_comment', 'message', 'message_owner'].includes(type_file_download)) {
            file_dom = get_offer.find('.tfile-infor[data-file-id=' + data.event.file_id + ']');
            if ($('.prdt').length > 0) {
                if (['message_owner', 'message'].includes(type_file_download)){
                    file_dom2 = $('.DM-box-container.dm-block-message').find('.block-download-file[data-file-id=' + data.event.file_id + ']');
                }else {
                    file_dom2 = get_offer.find('.block-download-file[data-file-id=' + data.event.file_id + ']');
                }
            }
            if (file_dom.length < 1) {
                file_dom = get_offer.find('.mfolder__sub[data-file-id=' + data.event.file_id + ']');
                if (file_dom.length < 1 && $('.scene-detail-page').length > 0) {
                    file_dom = get_offer.find('.block-download-file[data-file-id=' + data.event.file_id + ']')
                }
            }
        } else if (type_file_download === 'scene') {
            file_dom = get_offer.find('.tfile-infor[data-scene-id=' + data.event.file_id + ']');
        }
        let file_modal = $('#folderModal').find('.list-group-item[data-file-id=' + data.event.file_id + ']');
        if (file_dom.length > 0 || file_modal.length > 0) {
            addIconDownload(file_dom, data);
            // addIconDownload(file_modal, data);
        }
        if (file_dom2.length > 0 || file_modal.length > 0) {
            addIconDownload(file_dom2, data);
            // addIconDownload(file_modal, data);
        }
    }
}

const addIconDownload = (fileDom, data) => {
    let user_seen = fileDom.find('.sview-user-seen').length;
    let maxLoop = is_pc_device ? 6 : 3;
    let count_show = '';
    if (fileDom.find('.notification--outline-gray').length) {
        let user_count = fileDom.find('.notification--outline-gray').attr('data-value');
        user_count++;
        if (user_count >= 10) {
            count_show = '+9+'
        } else {
            count_show = '+' + user_count
        }
        fileDom.find('.notification--outline-gray').attr('data-value', user_count);
        fileDom.find('.notification--outline-gray').text(count_show);
        fileDom.addClass('has-user-seen');
    }
    if (user_seen < maxLoop) {
        $(UserDownloadedAvatar({display_name: data?.event?.display_name, avatar: data?.event?.avatar})).insertBefore(fileDom.find('.notification.notification--outline-gray'));
    } else {
        fileDom.find('.notification--outline-gray').removeClass('hide');
    }
}

const socketSeenComment = (data, current_receiver, offer_id) => {
    let mscene_target = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    let get_offer = $('.offer-' + offer_id);
    get_offer.find(('.messenger-director__item-seen ')).addClass('hide');
    get_offer.find(('.messenger-director__item-seen ')).last().removeClass('hide');
    if (data.event) {
        updateCountUnreadOfferComment(data.event, mscene_target, get_offer)
    }
    if (offer_id == current_receiver) {
        let comment_id = data.event.comment_id;
        for (let id of comment_id) {
            let message_dom = $('.mmessage[data-message-id=' + id + ']');
            if (message_dom.length > 0) {
                let user_seen = message_dom.find('.mmessage-user .mmessage-user-seen').length;
                if (message_dom.find('.notification--outline-gray').length) {
                    let user_count = message_dom.find('.notification--outline-gray').attr('data-value');
                    user_count++;
                    let count_show = '';
                    if (user_count >= 10) {
                        count_show = '+9+'
                    } else {
                        count_show = '+' + user_count
                    }
                    message_dom.find('.notification--outline-gray').attr('data-value', user_count);
                    message_dom.find('.notification--outline-gray').text(count_show);
                }
                if (user_seen < 3) {
                    const view = UserSeenMessage({avatar: data?.event?.url});
                    
                    if (message_dom.hasClass('mmessage--received')) {
                        message_dom.find('.mmessage-user').prepend(view);

                    } else {
                        message_dom.find('.mmessage-user').append(view);
                    }
                } else {
                    message_dom.find('.notification--outline-gray').removeClass('hide');
                }
            }
        }
    }
}

const updateBatchNumberStorage = (data) => {
    var project_id = $('.project-item.active').attr('data-project-id');
    let socketProject = data.event.project_id;
    if (!socketProject) {
        socketProject = data.event.product_id;
    }
    if (socketProject !== project_id) {
        return
    }
    let undownloadFile = data.event.count_file_un_download;
    if (!undownloadFile && undownloadFile  != 0) {
        return
    }
    $('.pbanner-tab--exchange .number-notification').attr('value', undownloadFile);
    $('.pbanner-tab--exchange .number-notification').text(undownloadFile);
}

const socketNewMessage = (target_offer, get_offer, current_receiver, offer_id, data, socket_id, regex) => {
    let message;
    if (data.event.message) {
        message = JSON.parse(data.event.message)[0].fields;
    }
    let comment;

    if (message) {
        updateCountUnreadOfferComment(data.event, target_offer, get_offer);
        updateListOffer(data);
    } else {
        comment = JSON.parse(data.event.comment)[0].fields;
        get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');
    }
    if (data.event.type_message === 'project') {
        get_offer = $('.project-item.active').find('.pd-product-comment');
    }

    if ($('.project-item.active[data-project-id=' + data.event.project_id + ']').length) {
        if (!$('.mitem[data-offer=' + data.event.offer_id + ']').length) {
            $('.mlist .list--offers-project').prepend(data.event.item_scene_html);
        }
    }
    updateOfferStatusInfo(data);
    if (current_receiver == offer_id) {

        if (data.event.type_message === 'system_message') {
            $(data.event.message_system_html).insertBefore(get_offer.find('.mlast__content'));
            sScrollbarBottom();
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(data.event.infor_html)
        } 
        else {
            let before_id = data.event.before_id;
            if (before_id) {
                let before_dom = $('.mmessage[data-message-id=' + before_id + ']');
                before_dom.addClass('mmessage-near');
                before_dom.find('.avatar--image:not(.avatar-seen)').remove();
            }

            let message_id = data.event.message_id;
            let sender_role = data.event.sender_role;
            let sender_id = data.event.sender_id;
            let sender_role_arr = data.event.sender_role_arr;

            if (message) {
                let form_edit_type = ['edit-plan', 'edit-contract', 'edit-bill']
                if(form_edit_type.includes(data.event.form_type)) {
            //         if (data.event.old_message_file_id) {
            //             let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
            //             let old_button_message = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').parents('.messenger-file-component-container')
            //             if(old_message.length) {
            //                 if (data.event.received_role) {
            //                     if (data.event.received_role === sender_role) {
            //                         $(data.event.message_send_html).insertBefore(old_message);
            //                         $(data.event.btn_message_send_html).insertBefore(old_button_message);
            //                     } else {
            //                         $(data.event.message_received_html).insertBefore(old_message);
            //                         $(data.event.btn_message_received_html).insertBefore(old_button_message);
            //                     }
            //                 } else {
            //                     if (sender_role_arr.includes(user_role)) {
            //                         $(data.event.message_send_html).insertBefore(old_message);
            //                         $(data.event.btn_message_send_html).insertBefore(old_button_message);
            //                     } else {
            //                         $(data.event.message_received_html).insertBefore(old_message);
            //                         $(data.event.btn_message_received_html).insertBefore(old_button_message);
            //                     }
            //                 }
            //                 old_message.remove()
            //                 old_button_message.remove()
            //             }
            //         }
                } else {
                    if (data.event.received_role) {
            //             if (data.event.received_role === sender_role) {
            //                 $(data.event.message_send_html).insertBefore(get_offer.find('.mlast__content'));
            //                 $(data.event.btn_message_send_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
            //             } else {
            //                 $(data.event.message_received_html).insertBefore(get_offer.find('.mlast__content'));
            //                 $(data.event.btn_message_received_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
            //             }
                    } else {
                        if (sender_role_arr.includes(user_role)) {
            //                 $(data.event.message_send_html).insertBefore(get_offer.find('.mlast__content'));
            //                 $(data.event.btn_message_send_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        } else {
                            $(data.event.message_received_html).insertBefore(get_offer.find('.mlast__content'));
                            $(data.event.btn_message_received_html).insertBefore(get_offer.find('.mcommment.mcomment-new'));
                        }
                    }
    
                    if (data.event.old_file_message_receiver_id) {
            //             let old_message = $('.messenger-file-component-content[data-file-id='+ data.event.old_file_message_receiver_id +']').not('.btn-plan-contract').parents('.messenger-file-component-container')
            //             let old_button_message = $('.maction.maction-new .messenger-file-component-content[data-file-id='+ data.event.old_message_file_id +']').parents('.messenger-file-component-container')
            //             if(old_message.length) {
            //                 if (data.event.received_role) {
            //                     if (data.event.received_role === sender_role) {
            //                         $(data.event.old_file_message_send_html).insertBefore(old_message);
            //                         // $(data.event.btn_old_file_message_send_html).insertBefore(old_button_message);
            //                     } else {
            //                         $(data.event.old_file_message_receiver_html).insertBefore(old_message);
            //                         // $(data.event.btn_old_file_message_receiver_html).insertBefore(old_button_message);
            //                     }
            //                 } else {
            //                     if (sender_role_arr.includes(user_role)) {
            //                         $(data.event.old_file_message_send_html).insertBefore(old_message);
            //                         // $(data.event.btn_old_file_message_send_html).insertBefore(old_button_message);
            //                     } else {
            //                         $(data.event.old_file_message_receiver_html).insertBefore(old_message);
            //                         // $(data.event.btn_old_file_message_receiver_html).insertBefore(old_button_message);
            //                     }
            //                 }
            //                 old_message.remove()
            //                 old_button_message.remove()
            //             }
                    }
                }
                

                if (sender_id !== socket_id) {
                    get_offer.find('.mmessage-list').addClass('not-seen');
                }
                if (message.has_file) {
                    let infor_file_html = data.event.file_infor_html;
                    $(infor_file_html).insertAfter($('.mlast__file'));
                    newWavesurferInit();
                }
            } else {
                const commentJson = JSON.parse(data.event.comment)
                const name = data.event.name
                const real_name = data.event.real_name

                const dataComment = 
                {
                    ...{
                    ...commentJson[0].fields, 
                    comment_id: commentJson[0].pk
                    },
                    parentComment: {
                        ...JSON.parse(data.event?.parent_comments)[0],
                        name: name ? name : (real_name ? real_name : ''),
                    },
                    fileList: JSON.parse(data.event?.listFile)?.map((file) => {
                        let extraInfo = JSON.parse(data?.event?.list_extra_file_info)?.find((dataFile) => dataFile?.file_id == file.file_id);
                        return {...file, ...extraInfo,};

                    }).filter(Boolean), 
                    folders: JSON.parse(data.event?.listFolder),
                    user_id: socket_id,
                    is_same_role: sender_role === user_role,
                    user_avatar: data.event?.user_avatar,
                }
                const fileListUpdateView = FileItemBlock({comments: [dataComment], fileList: [...JSON.parse(data.event?.listFile)], folderList: JSON.parse(data.event?.listFolder)} )
                $(".pd-file-content.mscrollbar").prepend(fileListUpdateView)
                if (!message && sender_role === user_role) {
                    $('.new-message').remove();
                    if (sender_id === socket_id) {
                        $('.new-message').remove();
                        commentJson.map((comment) => {
                            $(".mmessage-list-container.refactor").append(CommentSentBlockContainer({comment: dataComment, currentUserId: comment?.fields?.user, is_pc_device: comment?.is_pc_device}));
                        })

                    } else {
                        commentJson.map((comment) => {
                            $(".mmessage-list-container.refactor").append(CommentSentBlockContainer({comment: dataComment, currentUserId: comment?.fields?.user, is_pc_device: comment?.is_pc_device}));
                        })
                        get_offer.find('.mmessage-list').addClass('not-seen');
                    }
                } else {  
                    commentJson.map((comment) => {
                        $(".mmessage-list-container.refactor").append(CommentReceivedContainer({comment: dataComment, currentUserId: comment?.fields?.user, is_pc_device: comment?.is_pc_device}));
                    })
                    get_offer.find('.mmessage-list').addClass('not-seen');
                }
                if (comment.has_file) {
                    newWavesurferInit();

                    // Initialize HLS videos for new messages
                    if (typeof window.initializeAllHLSVideos === 'function') {
                        setTimeout(function() {
                            window.initializeAllHLSVideos();
                        }, 100);
                    }
                }
            }
            // showLastCommentSceneDetailSP();
            // actionShowIconResolved();
            // sScrollbarBottom();
            // let message_content_dom = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-audio-text, .s-filetext');
            // if (message_content_dom.length > 0) {
            //     let message_content = message_content_dom.html();
            //     let message_regex = message_content.replace(regex, "<a target='_blank' href=$&>$&</a>");
            //     $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-audio-text, .s-filetext').each(function () {
            //         if (!$(this).find('.icon')) {
            //             $(this).html(message_regex);
            //         }
            //     })
            //     let mess = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html();
            //     let message_regex_text = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
            //     $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html(message_regex_text);
            // }
        }
    }
}

const socketUpdateMessage = (data, regex, socket_id, infor_offer, get_offer) => {
    const name = data.event.name;
    const real_name = data.event.real_name;

    // if (data.event.message) {
    //     message = JSON.parse(data.event.message)[0].fields;
    // } else {
    //     comment = JSON.parse(data.event.comment)[0].fields;
    // }

    let message_id = data.event.message_id;
    let commentData = {
        ...JSON.parse(data.event.comment)[0].fields, 
        comment_id: data.event.message_id, 
        fileList: JSON.parse(data.event?.fileList)?.map((file) => {
                        let extraInfo = JSON.parse(data?.event?.list_extra_file_info)?.find((dataFile) => dataFile?.file_id == file.file_id);
                        if(file.folder_id === "None"){
                            file.folder_id = null;
                        }
                        return {...file, ...extraInfo};

                    }).filter(Boolean),
        folders: [
            ...JSON.parse(data.event.folders).map((folder) => {
                return {
                    ...folder, 
                    children: JSON.parse(data.event?.fileList)?.map((file) => {
                        if(file.folder_id === folder.folder_id){
                            let extraInfo = JSON.parse(data?.event?.list_extra_file_info)?.find((dataFile) => dataFile?.file_id == file.file_id);
                            return {...file, ...extraInfo,};
                        }
                    }).filter(Boolean)
                }
            }),     
        ],
        is_same_role: data.event.sender_role === user_role && data.event.sender_id === socket_id,
        user_id: socket_id,
        parentComment: data.event.parent_comments ? {
            ...JSON.parse(data.event?.parent_comments)[0],
            name: name ? name : (real_name ? real_name : ''),
        } : {},
        list_user_seen: JSON.parse(data.event?.listUserAvatar), 
        user_avatar: data.event?.user_avatar,
    };
    let message_dom = $('.mmessage[data-message-id=' + message_id + ']');
    message_dom.empty()
    const targetElement = $(`.mmessage.mmessage--sent[data-parent-id="${message_id}"]`);
    if(targetElement?.length){
        targetElement.map((child) => {
            $(targetElement[child]).find(".s-filedisable.s-filedisable--filedisable").remove();
            $(targetElement[child]).find(".s-filedisable-wrap").prepend(ParentCommnetBlock({comment: JSON.parse(data.event.comment)[0].fields, folders: JSON.parse(data.event.folders), files: JSON.parse(data.event?.fileList)}))
        })
    }

    if(socket_id === commentData.user || data.event.sender_role === user_role) {
        message_dom.append(CommentSentBlock({comment:commentData, currentUserId: socket_id, is_pc_device: data.event.is_pc_device }));
    } else {
        message_dom.append(CommentReceivedBlock({comment:commentData, currentUserId: socket_id, is_pc_device: data.event.is_pc_device }));
    }
    newWavesurferInit();

    // Initialize HLS videos for updated messages
    if (typeof window.initializeAllHLSVideos === 'function') {
        setTimeout(function() {
            window.initializeAllHLSVideos();
        }, 100);
    }

    // if (message_dom.length > 0) {
    //     message_dom.addClass('clicked');
    //     if (message) {
    //         if (message_dom.hasClass('mmessage--received')) {
    //             message_dom.html(data.event.received_message_html);
    //         } else {
    //             message_dom.html(data.event.send_message_html);
    //         }

    //         updateFileInMessage(data, message_id)
    //     } else {
    //         if (message_dom.hasClass('mmessage--received')) {
    //             message_dom.html(data.event.received_message_html);
    //         } else {
    //             if (data.event.sender_id == socket_id) {
    //                 message_dom.html(data.event.send_message_html);
    //             } else {
    //                 message_dom.html(data.event.message_send_by_role_html);
    //             }
    //         }

    //         if (data.event.file_infor_html && data.event.file_infor_html !== '') {
    //             $('.tfile-infor[data-message-id=' + message_id + ']').remove();
    //             if (data.event.has_file) {
    //                 let infor_file_html = data.event.file_infor_html;
    //                 $(infor_file_html).insertAfter($('.mlast__file'));
    //             }
    //             newWavesurferInit();
    //         }
    //     }
    //     if (message_dom.find('.s-text').length > 0) {
    //         let mess = $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html();
    //         let message_regex = mess.replace(regex, "<a target='_blank' href=$&>$&</a>");
    //         $('.mmessage[data-message-id=' + message_id + ']').find('.s-text, .s-filetext').html(message_regex);
    //     }
    // }
    // if (get_offer.length > 0 && data.event.offer_condition) {
    //     infor_offer.find('.minfo-contract').remove();
    //     infor_offer.find('.minfo--action').append(data.event.infor_html);
    //     get_offer.find('.floating-button-container').empty()
    //     get_offer.find('.floating-button-container').append(data.event.infor_html)
    //     if (data.event.user_role === 'master_client') {
    //         if (data.event.message_infor_html !== undefined) {
    //             get_offer.find('.mmessage-confirm').remove();
    //             $(data.event.message_infor_html).insertBefore(get_offer.find('.mlast__content'));
    //         }
    //         if (data.event.offer_condition === '1' || data.event.offer_condition === '8') {
    //             infor_offer.find('.infor-offer').addClass('hide');
    //             let offer_dom = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    //             if (offer_dom) {
    //                 offer_dom.removeClass('mprogress');
    //             }
    //         }
    //     }
    // }
}

function socketDeleteMessage(get_offer, offer_id, data, infor_offer) {
    let offer_message = true;
    let mscene_target = $('.mitem[data-offer^=' + offer_id + ']');
    if (mscene_target.length > 0) {
        if (parseInt(mscene_target.find('.notification--round').html()) > 1) {
            let count_message = parseInt(mscene_target.find('.notification--round').html()) - 1;
            mscene_target.find('.notification--round').html(count_message)
        } else {
            get_offer.find('.mmessage-list').removeClass('not-seen');
            mscene_target.find('.notification--round').addClass('hide');
            mscene_target.find('.notification--round').html('0')
        }
    }

    if (data.event.unread_message_count !== undefined) {
        updateCountUnreadOfferComment(data.event, mscene_target, get_offer);
    }
    if (get_offer.length < 1) {
        if (data.event.type_message === 'project') {
            get_offer = $('.project-item').find('.pd-product-comment');
        } else {
            get_offer = $('.pd-scene-title-detail[data-scene-title-id=' + data.event.scene_title_id + ']');

        }
        offer_message = false;
    }
    if (get_offer.length > 0) {
        let message_id = data.event.message_id;
        let messageDom = get_offer.find('.mmessage[data-message-id=' + message_id + ']');
        if(messageDom.hasClass('load-lasted-message')) {
            $('.load-lasted-message').prev().addClass('load-lasted-message')
        }
        let parentMessageDom = get_offer.find('.mmessage[data-parent-id=' + message_id + ']');

        messageDom.remove();
        if (parentMessageDom.hasClass('load-lasted-message')) {
            $('.load-lasted-message').prev().addClass('load-lasted-message');
        }
        parentMessageDom.find('a.s-filedisable').addClass('hide');
        removeMessage(message_id, data.event.deleted_file_ids);
        if (infor_offer && data.event.offer_condition) {
            infor_offer.find('.minfo-contract').remove();
            infor_offer.find('.minfo--action').append(data.event.infor_html);
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(data.event.infor_html)
            if (data.event.message_infor_html !== undefined) {
                get_offer.find('.mmessage-confirm').remove();
                $(data.event.message_infor_html).insertBefore(get_offer.find('.mlast__content'));
            }
            if (data.event.offer_condition === '1' || data.event.offer_condition === '8') {
                infor_offer.find('.infor-offer').addClass('hide');
                let offer_dom = $('.mitem[data-offer^=' + data.event.offer_id + ']');
                if (offer_dom) {
                    offer_dom.removeClass('mprogress');
                }
            }
        }
    }
}

const updateFileInMessage = (data, message_id) => {
    if (data.event.file_infor_html && data.event.file_infor_html !== '') {
        removeMessage(message_id);
        if (data.event.has_file) {
            let infor_file_html = data.event.file_infor_html;
            $(infor_file_html).insertAfter($('.mlast__file'));
        }
        newWavesurferInit();
    }
}

const removeMessage = (message_id, deleted_file_ids=[]) => {
    deleted_file_ids.forEach(function(id) {
        $('.tfile-item .tfile-infor[data-file-id=' + id + ']').parents('.tfile-item').first().remove();
    })
    let file_dom = $('.tfile-infor[data-message-id=' + message_id + ']');
    let folder_dom = $('.sfolder[data-message-id=' + message_id + ']');
    file_dom.parents('.tfile-item').remove();
    folder_dom.parents('.tfile-item-content').prev().first().remove();
    folder_dom.remove();
}

const removeSystemMessage = (data) => {
    let event = data.event;
    var project_id = $('.project-item.active').attr('data-project-id');
    if (event.project_id !== project_id) {
        return
    }
    let offer_id = event.offer_id;
    let get_offer = $('.offer-' + offer_id);
    let offer_dom = $('.mitem.mactive[data-offer^=' + offer_id + ']');
    if (offer_dom.length) {
        if (data.event.action === 'checked_done_offer' || data.event.action === 'delete_system_message') {
            $('.mmessage-system .mmessage-system__content:contains("全てのシーンの納品が完了しました。検収をお願いします。")').parents('.mmessage-system ').remove();
            get_offer.find('.floating-button-container').empty()
            get_offer.find('.floating-button-container').append(event.infor_html)
        } else if (data.event.action === 'upload_file_offer' || (data.event.action === 'new_message' && data.event.action_check === 'upload_file_plan_contract_offer')) {
            $('.mmessage-system .mmessage-system__content:contains("見積もり作成中..。今しばらくお待ちください。")').parents('.mmessage-system ').remove();
        }
    }
}

const updateCountUnreadOfferComment = (event, target_offer, get_offer) => {
    var project_id = $('.project-item.active').attr('data-project-id');
    let new_item = $('.mitem[data-offer^=' + event.offer_id + ']');
    if(event.project_id !== project_id) {
        return
    }
    if (target_offer) {
        let countNewMessage = event.unread_message_offer >= 100 ? "99+" : event.unread_message_offer;
        new_item.find('.notification--round').html(countNewMessage);
        if (countNewMessage <= 0) {
            new_item.find('.notification--round').addClass('hide');
             get_offer.find('.mmessage-list').removeClass('not-seen');
        } else {
            new_item.find('.notification--round').removeClass('hide')
        }
    }

    var unreadMessageCount = event.unread_message_count;
    var iconMessageElm = $('.pbanner-tab-message');
    var numberCountMessElm = iconMessageElm.find('.number-notification')

    if(unreadMessageCount <= 0) {
        numberCountMessElm.remove()
    } else {
        countMessageText = unreadMessageCount >= 100 ? "99+" : unreadMessageCount;
        if(numberCountMessElm.length > 0) {
            numberCountMessElm.text(countMessageText);
            numberCountMessElm.val(unreadMessageCount)
        }
        else {
            iconMessageElm.find('.icon-with-badge').append(`<span class="number-notification unread-offer-comment" value="${unreadMessageCount}">${countMessageText}</span>`);
        }
    }
}

const updateListOffer = (data) => {
    let new_item = $('.list--offers-project .mitem[data-offer^=' + data.event.offer_id + ']');
    let new_item_search = $('.list--offers-search .mitem[data-offer^=' + data.event.offer_id + ']');
    let reward = data.event.reward;
    if(new_item) {
        new_item_search.insertAfter($('.list--offers-search .count--list-offers-search'));
        new_item.find('.mscene__date').text(moment(new Date().toLocaleString("ja", {timeZone: "Asia/Tokyo"})).format('HH:mm'));
        let old_reward = new_item.data('reward');
        let created = new_item.data('created');
        let offer_side = new_item.data('offer-side');
        if(old_reward != reward) {
            if(!reward) {
                return
            }
            let listItems = $(`.mlist .list--offers-project .mitem[data-type-offer="messenger_artist"][data-offer-side=${offer_side}]`)
            new_item.data('reward', reward)
            listItems.each(function (index, element) {
                if (new_item.data('offer') !== $(element).data('offer')) {
                    var itemBudgetOrReward = $(element).data('reward')
                    var itemCreated = $(element).data('created')
                    if (reward > itemBudgetOrReward || (reward == itemBudgetOrReward && created >= itemCreated)) {
                        $(new_item).insertBefore($(element));
                        return false;
                    } else if (index === listItems.length - 1) {
                        $(new_item).insertAfter($(element));
                        return false;
                    }
                }
            })
        }
    }
}

const updateOfferStatusInfo = (data) => {
    let mscene_target = $('.mitem[data-offer^=' + data.event.offer_id + ']');
    if(data.event.offer_status_info && mscene_target) {
        let offer_status_info = JSON.parse(data.event.offer_status_info)
        var color = offer_status_info.color
        var text = offer_status_info.text
        if(color)
            mscene_target.find('.progress-offer').css("background-color", color)
        if(text)
            mscene_target.find('.description-content-offer').text(text)
    }
}

export const messengerActionEdit = () => {
    let $main_target = $(document);

    $main_target.off('click', '.mcomment-bottom').on('click', '.mcomment-bottom', function () {
        $(this).find('.mcomment-input-placeholder').hide();
        $(this).closest('.mcommment').find('.mcomment-top').show(200);
        $(this).closest('.mcommment').find('.mcomment-input-text').focus();
        //$('.prdt .DM-box-container .mmessage-list').css('padding-bottom', '120px')
    });

    showInforMessage();

    // edit
    $main_target.off('click', '.mmessage-edit').on('click', '.mmessage-edit', function (e) {
        should_scroll = false;
        e.stopPropagation();
        e.preventDefault();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        list_file_name = [];
        list_folder_name = [];
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.toggleClass('editing');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-pin').removeClass('active hide');
        $main_target.find('.mmessage.reply').removeClass('active reply');
        $main_target.find('.mcomment-input').removeClass('is-reply is-pin');
        $('.cscene-vertical').removeClass('active');
        $message_target.removeClass('reply');
        $('.mmessage[data-message-id!=' + message_id + ']').removeClass('editing');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').addClass('pd-main-message')
        }
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $message_target.find('.mmessage-edit').removeClass('active');
            $(this).addClass('active');
        }

        if ($(this).parents('.mmessage').hasClass('editing')) {
            let pin_dom = $message_target.find('.video-pin-time');
            if (pin_dom.length > 0) {
                $main_target.find('.mcomment-pin').trigger('click');
                let current_time = $message_target.find('.video-pin-start').text();
                $main_target.find('.mcomment-input-title span').text(current_time);
                let scene_id = pin_dom.parents('.s-audio').attr('data-scene-id');
                goToSceneActive(scene_id);
            }

            $main_target.find('.mcommment').addClass('border-editing');
            $main_target.find('.btn-remove-msg').addClass('d-block')
            $main_target.find('.block-remove-msg-editing').removeClass('d-none')
            $main_target.find('.mcomment-send').addClass('input-editing');
            let content = '';
            let $message = $(this).parents('.mmessage');
            
            // 各要素を個別にチェックして、最初に見つかった非空のテキストを使用
            // 1. まず返信のテキストをチェック（s-filetext）
            let content_dom = $message.find('.s-filetext').first();
            if (content_dom.length > 0 && content_dom.text().trim()) {
                content = content_dom.text();
            }
            
            // 2. 音声メッセージのテキストをチェック
            if (!content) {
                content_dom = $message.find('.s-audio-text').first();
                if (content_dom.length > 0 && content_dom.text().trim()) {
                    content = content_dom.text();
                }
            }
            
            // 3. 通常のテキスト（DM用）をチェック
            if (!content) {
                content_dom = $message.find('.s-text').first();
                if (content_dom.length > 0 && content_dom.text().trim()) {
                    content = content_dom.text();
                }
            }
            
            // 4. トークルームのテキストをチェック
            if (!content) {
                content_dom = $message.find('.bodytext-13').first();
                if (content_dom.length > 0 && content_dom.text().trim()) {
                    content = content_dom.text();
                }
            }
            
            $main_target.find('.mcommment .mcomment-bottom').click();
            $main_target.find('.mcommment .mcomment-input-text').val(content);
            calculateHeightCommentInput($main_target.find('.mcommment .mcomment-input-text')[0])
            let files_dom = $(this).parents('.mmessage').find('.s-file, .s-audio-source');
            let files_uploaded_html = '';
            if (files_dom) {
                $(this).parents('.mmessage').find('.s-file, .block-name-action-audio').each(function () {
                    let file_name = $(this)[0].innerText.split("\n")[0];
                    let file_id = $(this).parents('.mmessenger--file').attr('data-file-id');
                    // console.log($(this), $(this).parents('.mmessenger--audio-wave'));
                    if(!file_id){
                        file_id = $(this).parents('.info-item-audio-comment').attr('data-file-id');
                    }
                    if (file_name) {
                        if (file_id === file_contract || file_id === file_bill) {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        } else {
                            files_uploaded_html += '<div class="mattach-template file-item-deleted collection-item item-template" data-file-id="' + file_id + '">' +
                                '<div class="mattach-info" data-dz-thumbnail="">' +
                                '<div class="mcommment-file">' +
                                '<div class="determinate" style="width:0" data-dz-uploadprogress=""></div>' +
                                '<div class="mcommment-file__name" data-dz-name="">' + file_name + '</div>' +
                                '<div class="mcommment-file__delete" data-dz-remove="">' +
                                '<i class="icon icon--sicon-close"></i>' +
                                '</div>' +
                                '</div>' +
                                '</div>' +
                                '</div>';
                        }

                    }
                })
            }
            $('.maction .mattach-previews.collection').empty().append(files_uploaded_html);

            $main_target.find('.mcommment-file__delete').on('click', function () {
                let file_id = $(this).parents('.mattach-template').attr('data-file-id');
                list_file_remove.push(file_id);
                $(this).parents('.mattach-template').remove();
            });

            $main_target.find('.mcomment-send').addClass('active');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
        setTimeout(() => {
            should_scroll = true; 
        }, 1500);
    });


    $main_target.off('click', '.mmessage-delete').on('click', '.mmessage-delete', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        $('.mcomment-message .mcommment-file').remove();
        e.stopPropagation();
        e.preventDefault();
        let url_page;
        let data = new FormData();
        should_scroll = false;
        data.append('message_id', message_id);

        if (messenger_page === 'messenger_artist' || (messenger_page === 'top_page' && $('.pbanner-tab.active[data-show=messenger]').length)) {
            url_page = '/messenger/delete_message';
        } else if (messenger_page === 'top_page') {
            if ($('.project-tab-product-comment').hasClass('active')) {
                data.append('type', 'project');
                let project_id = $('.project-item.active').attr('data-project-id');
                data.append('project_id', project_id);
            } else {
                data.append('type', 'scene');
            }
            url_page = "/top/delete_comment";
        }

        bootbox.confirm({
            message: gettext('Do you really want to delete this?'),
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        type: "POST",
                        contentType: false,
                        processData: false,
                        cache: false,
                        data: data,
                        url: url_page,
                                                    success: function (data) {
                                // console.log('OK');
                                // autoLoadMore();
                        },
                        complete: function() {
                            resetFormContract();
                            setTimeout(() => {
                                should_scroll = true; 
                            }, 1500);
                        }
                    });
                }
            }
        });
    });

    $main_target.off('click', '.mmessage-resolve').on('click', '.mmessage-resolve', function (e) {
        let $message_target = $(this).parents('.mmessage');
        let message_id = $message_target.attr('data-message-id');
        $message_target.removeClass('editing');
        resetInputMessage($main_target);
        e.stopPropagation();
        e.preventDefault();
        let data = {};
        let resolved = $(this).hasClass('mmessage-resolved');

        let parent_dom = $main_target.parents('.pd-section');
        let $message_child = parent_dom.find('.mmessage[data-parent-id=' + message_id + ']');
        if (!resolved) {
            if (parent_dom.hasClass('show-comment-unresolved')) {
                if ($message_target.find('.s-audio-control.active').length) {
                    $message_target.find('.s-audio-control.active').trigger('click')
                }
                if ($message_child.find('.s-audio-control.active').length) {
                    $message_child.find('.s-audio-control.active').trigger('click')
                }
                $message_target.fadeOut('slow');
                $message_child.fadeOut('slow');
            }
            setTimeout(function () {
                $message_target.addClass('resolved');
                $message_child.addClass('resolved');
            }, 300);

            $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
            if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')){
                    $message_target.find('.mmessage-resolve .txt-item-comment').text('進行中に戻す')
            }
        } else {
            $message_target.css('display', 'flex');
            $message_child.css('display', 'flex');
            $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
            $message_target.removeClass('resolved');
            $message_child.removeClass('resolved');
                if ($('.owner-top').hasClass('scene-detail-page') || $('.owner-top').hasClass('prdt')){
                    $message_target.find('.mmessage-resolve .txt-item-comment').text('解決済みにする')
            }
        }

        if ($(this).parents('.pd-product-comment').length > 0) {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'project',
            };
        } else {
            data = {
                'comment_id': message_id,
                'resolved': resolved,
                'type': 'scene',
            };
        }

        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/top/resolve_comment",
            data: data,
            success: function () {
                // console.log('ok');
            },
            error: function () {
                if (resolved) {
                    $message_target.addClass('resolved');
                    $message_child.addClass('resolved');
                    $message_target.find('.mmessage-resolve ').addClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve ').addClass('mmessage-resolved');
                } else {
                    $message_target.css('display', 'flex');
                    $message_child.css('display', 'flex');
                    $message_target.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_child.find('.mmessage-resolve').removeClass('mmessage-resolved');
                    $message_target.removeClass('resolved');
                    $message_child.removeClass('resolved');
                }
                // autoLoadMore();
            }
        })

    });

    $main_target.off('click', '.mmessage-reply').on('click', '.mmessage-reply', function (e) {
        e.preventDefault();
        e.stopPropagation();
        list_file_remove = [];
        mzdrop["sended"] = true;
        mzdrop.removeAllFiles();
        list_file_id = {};
        $main_target.find('.mcommment .mcomment-input-text').val('');
        $('.mcommment-file').remove();
        $main_target.find('.mcomment-send.active').removeClass('active');
        $main_target.find('.mmessage').removeClass('editing');
        $main_target.find('.mcomment-send').removeClass('input-editing');
        $main_target.find('.mcommment').removeClass('border-editing');
        $main_target.find('.mcomment-pin').removeClass('active hide');
        if ($(document).width() > maxWidthIpadDevice) {
            $('.prdt .mmessage-list').removeClass('pd-main-message')
        }
        // $main_target.find('.mmessage-reply.active').removeClass('active');
        $('.cscene-vertical').removeClass('active');
        $main_target.find('.mmessage').removeClass('reply');
        $(this).toggleClass('active');
        if ($(this).hasClass('active')) {
            $main_target.find('.mmessage-reply').removeClass('active');
            $(this).addClass('active');

            var message_component_close = $(this).closest('.mmessage-component');
            // if (message_component_close.length < 1) {
            //     message_component_close = $(this).closest('.video-item-comment-content');
            // }
            let parent_id = $(this).parents('.mmessage').attr('data-message-id');
            $(this).parents('.mmessage').addClass('reply');
            $('.pd-comment').find('.mcomment-input').removeClass('is-pin').addClass('is-reply');
            $('.pd-comment').find('.mcomment-input').attr('data-parent-id', parent_id);
            $('.pd-comment').find('.mcomment-input-title').html('<i class="icon icon--sicon-reply"></i>');
            $('.pd-comment').find('.mcomment-bottom').trigger('click');
        } else {
            resetInputMessage($main_target);
        }
        let activeAudio = parseInt($(this).parents('.mmessage--received').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSceneDetailComment = $(this).parents('.mmessage--received').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudio);
        let activeAudioSent = parseInt($(this).parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        let activeAudioSentSceneDetailComment = $(this).parents('.mmessage--sent').find('.comments-audio-block .s-audio');
        for (let i = 0; i < activeAudioSentSceneDetailComment.length; i++) {
            checkActive($(this), parseInt(activeAudioSentSceneDetailComment[i].getAttribute('data-wavesurfer')));
        }
        checkActive($(this), activeAudioSent);
    });

    $(document).on('click', '.mcomment-input-close', function (e) {
        e.preventDefault();
        const editMessage = $(".mmessage-edit.active");
        const replyMessage = $(".mmessage-reply.active");
        let activeAudio = parseInt(editMessage.parents('.mmessage--sent').find('.s-audio').attr('data-wavesurfer'));
        if(editMessage.length > 0 ){
            editMessage.removeClass("active");
            checkActive(editMessage, activeAudio);
        } else if (replyMessage.length > 0) {
            replyMessage.removeClass("active");
            checkActive(replyMessage, activeAudio);
        }
        $('.mmessage-reply').removeClass('active');
        $(this).closest('.mcomment-input').removeClass('is-reply').removeClass('is-pin');
        $(this).closest('.mcomment-input').find('.mcomment-input-text').focus();
        resetInputMessage($main_target);

    });

    $main_target.off('click', '.mcomment-pin').on('click', '.mcomment-pin', function (e) {
        e.preventDefault();

        $('.mcomment-pin').toggleClass('active hide');
        var message_component_close = $(this).closest('.mmessage-component');

        message_component_close.find('.mcomment-input').toggleClass('is-pin');
        $('.cscene-vertical').toggleClass('active');
        $('.cscene-vertical').css({
            'border': 'none'
        })
            addBorderPreview(true);

        if ($(this).hasClass('active')) {
            let currentTime = '0:00';
            let video = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active video')[0];
            if (video) {
                currentTime = video.currentTime;
            } else {
                let audio_dom = $(this).parents('.pd-scene-title-detail').find('.cscene__variation.slick-current .cscene__version.slick-current.slick-active .s-audio--white');
                if (audio_dom.length > 0) {
                    let current_wave_index = audio_dom.attr('data-wavesurfer');
                    let current_wave = wavesurfer_arr[current_wave_index];
                    currentTime = current_wave.getCurrentTime();
                } else {
                    currentTime = '';
                }
            }

            message_component_close.find('.mcomment-input-title').html('<i class="icon material-symbols-rounded">pin_drop</i>'+ '<span>' + msToTime(currentTime) +'</span>');
            message_component_close.find('.mcomment-bottom').trigger('click');
        } else {
            let $main_target = $('.mcolumn.mcolumn--main, .pd-comment__main');
            resetInputMessage($main_target);
        }

    });

    $main_target.off('click', '.icon--sicon-pin').on('click', '.icon--sicon-pin', function () {
        $('.mcomment-pin').removeClass('active hide');
        $('.mcomment-input').removeClass('is-pin');
        $('.cscene-vertical').removeClass('active');

        addBorderPreview();
        $main_target.find('.mcomment-input-text.mcomment-autoExpand').focus();
    })
}

function checkActive(controlDom, activeAudio) {
    const parent = controlDom.closest(".mmessage-container.refactor");
    
    if(controlDom.hasClass('active')) {
        // controlDom.find('s-audio.s-audio--audio-wave.s-audio--black').addClass('active');
        setColorActive(wavesurfer_arr[activeAudio]);
    }else {
        if(parent.find(".s-audio.s-audio--audio-wave").hasClass("active")){
            setColorActive(wavesurfer_arr[activeAudio]);
        }else {
            setColorInActive(wavesurfer_arr[activeAudio]);
        }
        // if ($('.s-audio-control').hasClass('active')){
        //     $('.s-audio-control').removeClass('active')
        // }
    }
}

function calculateHeightCommentInput(inputElement ) {
    let editing = false;
    if ($('.mcomment-send.input-editing').length > 0){
        editing = true;
    }
            $(inputElement).css('height', 'auto');
            var height = parseInt(inputElement.scrollHeight)
                + parseInt($(inputElement).css('border-top-width'))
                - parseInt($(inputElement).css('padding-top'))
                + parseInt($(inputElement).css('border-bottom-width'))
                - parseInt($(inputElement).css('padding-bottom'));

            if ($(inputElement).val()) {
                $(inputElement).parents('.mcommment').find('.mcomment-send').addClass('active');
            } else {
                if (!$(inputElement).parents('.mcomment-top').find(".mattach-template").length && !$('.mcommment .mcomment-send').hasClass('input-editing')) {
                    $(inputElement).parents('.mcommment').find('.mcomment-send').removeClass('active');
                }
            }

        valInput = $(inputElement).val();
        var maxHeight = $(window).height() - 64 - 75 - 40 - 80 - 48;
        if ($('.prdt').length > 0) {
            //  $(inputElement).css('overflow', 'hidden')
            if (height > maxHeight) {
                const inputComment = $('.mcomment-input-text');
                inputComment.scrollTop(inputComment[0].scrollHeight)
                $(inputElement).height(maxHeight + 'px');
                $(inputElement).css('overflow', 'auto')
            } else if (height <= maxHeight) {
                $(inputElement).height(height + 'px');
            } else {
                if (editing) {
                    $(inputElement).height(height + 'px');
                } else {
                    $(inputElement).height(height + 'px');
                }
            }
        } else {
            $(inputElement).height(height + 'px');
            if (height > maxHeight) {
                $(inputElement).height(maxHeight + 'px');
            }
        }
    
}

function showInforMessage() {
    $(document).on('mouseenter mouseleave', '.mmessage', function (e) {
        let parent = $(this).parents('.mmessage-list');
        if (!parent.hasClass('view_only')) {
            if ($(this).parents('.martist').length && !$('.scene-title__action .button-edit_offer').length) {
                $('.button-edit_offer.message-first__message').remove();
            }
            removeButtonDelete();
            let message_active = $(this);

            if ($(this).hasClass('mmessage--sent') || !$('input[name="switch-checkbox-comment"]').is(':checked')) {
                message_active.find('.mmessage-status').toggleClass('clicked', e.type === 'mouseenter');
                if (message_active.height() <= 75) {
                    if(!!$('.message-actions-container').children().length && !!message_active.find('.message-actions-container .mmessage-action').children().length){
                        message_active.find('.mmessage-status').toggleClass('hide', e.type === 'mouseenter');
                    }
                } else {
                    message_active.css('cursor', 'default');
                    message_active.find('.mmessage-info').css({height: message_active.find('.mmessage-main').height()})
                }
                message_active.find('.mmessage-action').toggleClass('show-action-hover', e.type === 'mouseenter');
                if (e.type === 'mouseleave') {
                    message_active.find('.mmessage-action').addClass('hide');
                    message_active.find('.mmessage-status').removeClass('hide');
                }
            }
        }
    });
}

function removeButtonDelete() {
    if (file_contract) {
        let messageContractDom = $('.mmessenger[data-file-id=' + file_contract + ']').parents('.mmessage');
        messageContractDom.find('.mmessage-delete').remove()
    }
    if (file_bill) {
        let messageBillDom = $('.mmessenger[data-file-id=' + file_bill + ']').parents('.mmessage');
        messageBillDom.find('.mmessage-delete').remove()
    }
}