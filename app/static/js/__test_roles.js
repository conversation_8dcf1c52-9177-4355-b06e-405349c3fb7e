// 目的物のオブジェクト
const roles = [
  {
    category: "プロダクション",
    name: "監督",
    credits: ["Directed by"],
    description:
      "ビジュアルやストーリーテリングの方向性を指示・管理する役割を担い、全体的なビジョンを統括",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "プロデュース",
    credits: ["producer", "produced by"],
    description: "プロジェクトの全体的な管理者",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [200000, 400000, 800000, 1600000, 3200000],
    deadline: [14, 30, 60, 90, 120],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "ラインプロデュース",
    credits: ["Line Producer"],
    description: "制作の実務的な側面を管理する役割を担います。",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "撮影監督",
    credits: ["Director of Photography"],
    description: "映像のビジュアルスタイルとクオリティを監督。",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "美術監督",
    credits: ["Production Designer", "Art Director"],
    description: "プロジェクトのビジュアルアート面を監督",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "音響監督",
    credits: ["Audio Director"],
    description: "音響の全体的なデザインとクオリティを監督",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  {
    category: "プロダクション",
    name: "キャスティングディレクター",
    credits: ["Casting Director"],
    description: "俳優の選定とキャスティングを監督",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
  //   サウンド
  {
    category: "サウンド",
    name: "BGM",
    credits: ["Original Score Composed by", "Composed by"],
    description: "インストゥルメンタルの音楽を作曲および編曲",
    deliverables: ["48k24bit 2mix", "48k24bit stem"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "作編曲",
    credits: ["Original Song by", "Composed by"],
    description: "主にボーカルを中心とした楽曲の作編曲",
    deliverables: ["48k24bit 2mix", "48k24bit stem"],
    subcontracting: false,
    fee: [180000, 240000, 300000, 360000, 420000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "編曲",
    credits: ["Arranged by"],
    description:
      "既存のメロディ、コード進行を調整し、異なる楽器編成やスタイルに再構築",
    deliverables: [
      "48k24bit 2mix",
      "48k24bit stem",
      "Nuendoプロジェクト",
      "Protoolsセッション",
    ],
    subcontracting: false,
    fee: [180000, 240000, 300000, 360000, 420000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "仮歌",
    description: "デモ楽曲に対し、ガイドとなる仮歌を収録",
    deliverables: ["48k24bit Para", "48k24bit stem"],
    subcontracting: false,
    fee: [5000, 7500, 10000, 15000],
    deadline: [3, 5, 7, 10, 14],
    addendum: "デモ目的のみで利用するものとします。",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "SE",
    credits: ["Sound Designer"],
    description: "効果音ライブラリなどを駆使して効果音を制作",
    deliverables: ["Nuendoプロジェクト", "Protoolsセッション",'48k24bit stem'],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "フォーリーアーティスト",
    credits: ["Foley Artist"],
    description: "スタジオでフォーリーを実演",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "レコーディングエンジニア",
    credits: ["Recording Engineer"],
    description: "スタジオでレコーディングを行う",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "レコーディングエンジニア",
    credits: ["Recording Engineer"],
    description: "スタジオでレコーディングを行う",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "ミキシングエンジニア",
    credits: ["Recording Engineer"],
    description: "スタジオでレコーディングを行う",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "マスタリングエンジニア",
    credits: ["Recording Engineer"],
    description: "スタジオでレコーディングを行う",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },
  {
    category: "サウンド",
    name: "レコーディングスタジオ",
    credits: ["Recorded at"],
    description: "スタジオでレコーディングを行う",
    deliverables: ["スタジオにて実演"],
    subcontracting: false,
    fee: [35000, 70000, 10000, 150000, 200000],
    deadline: [5, 7, 10, 14, 21],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
  },


  {
    category: "ボイス",
    name: "キャラクターボイス",
    deliverables: [
      "Nuendoプロジェクト",
      "Protoolsセッション",
      "48k24bit wav(stem)",
    ],
    fee: [10000, 15000, 30000, 50000, 100000],
    deadline: [3, 5, 7, 10, 14],
    description: "スタジオでのフォーリー実演を行います。",
  },
  {
    category: "ボイス",
    name: "キャラクターボイス",
    credits: ["Directed by"],
    description:
      "ビジュアルやストーリーテリングの方向性を指示・管理する役割を担い、全体的なビジョンを統括します",
    deliverables: ["クライアントの指定する形式で成果物を納品"],
    subcontracting: true,
    fee: [400000, 800000, 1600000, 3200000, 4800000],
    deadline: [30, 60, 90, 120, 150],
    addendum: "なし",
    thumbnail: "{% static 'images/roles/produce.png' %}",
    order: 0,
    joinAs: "producer",
  },
];

// roles 配列から category のみを抽出して、それをリストとして HTML に追加する関数
const displayCategories = () => {
  // 重複を排除したカテゴリーのセットを作成
  const categories = new Set(roles.map((role) => role.category));
  const list = document.getElementById("categoryList");

  // カテゴリーごとにリストアイテムを作成して追加
  categories.forEach((category) => {
    const listItem = document.createElement("li");
    listItem.textContent = category;
    listItem.addEventListener("click", function () {
      // 他のすべてのリストアイテムのアクティブクラスを削除
      document.querySelectorAll("#categoryList li").forEach((li) => {
        li.classList.remove("c-parent-tag--active");
      });
      // 現在のリストアイテムにアクティブクラスを追加
      listItem.classList.add("c-parent-tag--active");
    });
    list.appendChild(listItem);
  });
};

const displayNamesForActiveCategory = () => {
  const activeCategory = document.querySelector(
    "#categoryList .active"
  ).textContent;
  const filteredNames = roles.filter(
    (role) => role.category === activeCategory
  );
  const listElement = document.querySelector(".c-child-tags");
  listElement.innerHTML = ""; // 既存のリストをクリア

  filteredNames.forEach((role) => {
    const listItem = document.createElement("li");
    listItem.textContent = role.name;
    listElement.appendChild(listItem);
  });
};

// カテゴリ選択時に名前を表示するイベントリスナーを追加
document
  .getElementById("categoryList")
  .addEventListener("click", (event) => {
    if (event.target.tagName === "LI") {
      document.querySelectorAll("#categoryList li").forEach((li) => {
        li.classList.remove("active");
      });
      event.target.classList.add("active");
      displayNamesForActiveCategory();
    }
  });



// すべてのリソース（画像、外部リソース等）の読み込みが完了した時点で実行したい処理をここに書く
window.onload = displayCategories;
