var searching = false;
var searching_keyword = '';
$(document).ready(function () {
    initSearch();
    autoLoadListNewWork();
    removeOverLayModal() ;
});

function initSearch() {
    $('input#gallery__search-album-input').on('keydown', function(e) {
        //if Enter => Search
        if (e.key !== undefined && e.key === 'Enter') {
            handleSearch($(this));
        } else if (e.keyCode !== undefined && e.keyCode === 13) {
            handleSearch($(this));
        }
        searching = false;
    })

    $('.gallery-search-delete').on('click', function() {
        closeSearch($(this));
    })
}

function handleSearch(targetInput) {
    let kw = targetInput.val().trim();
    if(kw.length && searching_keyword !== kw) {
        searching = true;
        searching_keyword = kw;
        $.ajax({
            type: 'GET',
            datatype: 'json',
            url: '/gallery/search_sale_content_in_gallery',
            data: {
                'key_word': kw
            },
            success: function (data) {
                hideGallery();
                showSearchResult(data.html);
            },
            complete: function(data) {
                searching_keyword = ''
                searching = false
            }
        });
    }
}

function closeSearch(closeButton) {
    $('input#gallery__search-album-input').val('')
    hideSearchResult()
    showGallery()
}

function hideGallery() {
    $('.gallery-search-delete').css('display', 'block');
    $('.container.gallery-container > div:not(.gallery__content-container)').addClass('hide');
}

function showGallery() {
    $('.gallery-search-delete').css('display', 'none');
    $('.container.gallery-container > div').removeClass('hide');
}

function showSearchResult(html) {
    $('.container.gallery-container').append(html);
}

function hideSearchResult() {
    $('.gallery-seach-result-container').remove();
}


var currentPageNewWork = 0;
var isSendingNewWork = false;
let listNewWorkIds = $('.gallery__new-works-container').attr('data-list-ids');
const listNewWork = listNewWorkIds.replace('[', '').replace(']', '').replaceAll(/'/g, '').replaceAll(' ', '').split(',');
const totalPageNewWork = $('.gallery__new-works-container').attr('data-total-page');
function autoLoadListNewWork() {
    ajaxLoadMoreNewWork(currentPageNewWork);
    function ajaxLoadMoreNewWork(currentPageNewWork) {
        if (isSendingNewWork || currentPageNewWork > totalPageNewWork) {
            return
        }
        isSendingNewWork = true;
        let list_new_work_id = listNewWork.slice(currentPageNewWork * 3, currentPageNewWork * 3 + 3);
        currentPageNewWork += 1;

        $.ajax({
            type: "GET",
            dataType: "json",
            data: {
                'current_page': currentPageNewWork,
                'list_new_work_id': list_new_work_id
            },
            url: '/gallery/load_more_list_new_work',
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            },
            success: function (data) {
                $('.gallery__new-works-container').append(data.html);
                dragableNewWorks();
                if (totalPageNewWork == currentPageNewWork && user_role === 'curator') {
                    $('.gallery__new-works-container').append(` <div class="list-new-works__button-add-work-theme">
                                                            <div class="list-new-works__button-add-work-theme__content">
                                                                <i class="icon icon--sicon-add-cirlce"></i>
                                                                    <p>WORKSテーマを追加</p>
                                                            </div>
                                                         </div>`);
                } else {
                    isSendingNewWork = false;
                    ajaxLoadMoreNewWork(currentPageNewWork);
                }
            },
            error: function () {
                currentPageNewWork -= 1;
                isSendingNewWork = false;
            },
        });
    }
}
