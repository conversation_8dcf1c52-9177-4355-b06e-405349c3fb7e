const currentDate = new Date();
// Chuyển đổi thành chuỗi ISO
let isoString = currentDate.toISOString();
// Lấy phần yyyy-mm-dd từ chuỗi ISO
let currentDateFormatted = isoString.split('T')[0];
let responseSchedule = [];
let dateSelected;
let changed_month = false;
let listScheduleOff = [];
let listDateOffers = [];
let listDateSceneTitles = [];

$(document).ready(function () {
    getDateRemaining();
    const status_circle_html = '<svg class="status_circle circle_grey" xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none"><circle cx="3" cy="3" r="2.125" fill="#A7A8A9" stroke="white" stroke-width="0.25"/></svg>';
    const status_circle_blue_html = '<svg class="status_circle circle_blue" xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none"><circle cx="3" cy="3" r="2.5" fill="#009ACE" stroke="white"/></svg>';
    const status_two_circle_html = '<div class="status-two-circle"><svg class="circle-blue" xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none"><circle cx="3" cy="3" r="2.125" fill="#A7A8A9" stroke="white" stroke-width="0.25"/></svg><svg class="circle-blue" xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none"><circle cx="3" cy="3" r="2.5" fill="#009ACE" stroke="white"/></svg></div>';
    let arr_appended_for_month = [];
    let listDateOffers_for_month = [];
    let listDateSceneTitles_for_month = [];
    // Lấy tháng và năm hiện tại
    let currentMonth = currentDate.getMonth() + 1; // Tháng bắt đầu từ 0 nên cần cộng thêm 1
    let currentYear = currentDate.getFullYear();
// Tháng và năm của tháng tiếp theo
    let nextMonthDate = new Date(currentDate);
    nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
    let nextMonth = nextMonthDate.getMonth() + 1; // Tháng bắt đầu từ 0 nên cần cộng thêm 1
    let nextYear = nextMonthDate.getFullYear();

// Tháng và năm của tháng trước đó
    let previousMonthDate = new Date(currentDate);
    previousMonthDate.setMonth(previousMonthDate.getMonth() - 1);
    let previousMonth = previousMonthDate.getMonth() + 1; // Tháng bắt đầu từ 0 nên cần cộng thêm 1
    let previousYear = previousMonthDate.getFullYear();

// Định dạng chuỗi kết quả
    let currentDateString = currentYear + '/' + currentMonth + '/20';
    let nextMonthDateString = nextYear + '/' + nextMonth + '/20';
    let previousMonthDateString = previousYear + '/' + previousMonth + '/20';
    let maxHeightUserInfoBlock2 = 88;
    let project_id = $('.project-item.active').attr('data-project-id')
    let scheduleCalendar = $('#scheduleCalendar');
    $('.schedule-project-detail').on('click', function () {
        scheduleCalendar.datepicker('destroy');
        $('.calendar-scene-1').empty();
        $('.calendar-scene-2').empty();
        calcHeightCalendarModal();
        getDataSchedule(previousMonthDateString, nextMonthDateString)
    })


    function getDataSchedule(start_date, end_date, changed_month = false) {
        calcHeightScheduleModal();
        console.log('start_date:', start_date)
        console.log('end_date:', end_date)
        $.ajax({
            type: "GET",
            url: `/top/project/${project_id}/project_schedule`,
            data: {
                // 'start_date': '2023/03/20',
                // 'end_date': '2024/03/20',
                'start_date': start_date,
                'end_date': end_date,
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $('.sc-content .modal-body').append(`<div id="loading_animation" class="loading_animation_container"></div>`);
                addLoadingAnimation();
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                let schedule_off = response.creator_off_schedule;
                let offers = response.offers;
                let scene_titles = response.scene_titles;
                $('.sc-content .modal-body').find(".loading_animation_container").remove();
                $('.status_circle, .status-two-circle').remove();
                const result_formatted = processData(offers, scene_titles);
                const sortedResultKeys = Object.keys(result_formatted).sort((a, b) => new Date(a) - new Date(b));
                let arr_appended = []
                $('.day').removeClass('disabled-date');
                listScheduleOff = schedule_off.map(function (item) {
                    return moment(item).locale('en').format("YYYY-MM-DD");
                });

                for (const key_schedule_off in listScheduleOff) {
                    if (listScheduleOff.includes(listScheduleOff[key_schedule_off])) {
                        let dateInMls2 = Date.parse(listScheduleOff[key_schedule_off]);
                        $(`.day[data-date="${dateInMls2}"]`).addClass('disabled-date')
                    }
                }

                listDateOffers = offers.map(function (item) {
                    return moment(item.offer_creator.deadline).locale('en').format("YYYY-MM-DD");
                });
                listDateSceneTitles = scene_titles.map(function (item) {
                    return moment(item.last_version.schedule_date).locale('en').format("YYYY-MM-DD");
                });
                // arr_appended_for_month = arr_appended;
                //  listDateOffers_for_month = listDateOffers;
                //  listDateSceneTitles_for_month = listDateSceneTitles;
                $.fn.datepicker.dates['en'] = {
                    days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
                    daysShort: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
                    daysMin: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
                    months: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                    monthsShort: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                    today: "Today",
                    clear: "Clear",
                    format: "mm/dd/yyyy",
                    titleFormat: "MM yyyy", /* Leverages same syntax as 'format' */
                    weekStart: 0
                };
                scheduleCalendar.datepicker({
                    format: 'dd-mm-yyyy',
                    locale: 'ja',
                    startDate: currentDate,
                    todayHighlight: true,
                    // daysOfWeekDisabled: [0, 6],
                    beforeShowDay: function (date) {
                        let timezoneOffsetHours = currentDate.getTimezoneOffset() / 60;
                        const hourTimeZoneMls = Math.abs(timezoneOffsetHours) * 3600 * 1000
                        let dateInMls = Date.parse(date);
                        dateInMls += hourTimeZoneMls;

                        let dateString = $.datepicker.formatDate('yy-mm-dd', date);
                        let day = date.getDay(); // Lấy ngày trong tuần (0-6)
                        let isWeekend = day === 0 || day === 6; // Thứ 7 (0) và Chủ nhật (6)

                        if (dateString >= currentDateFormatted && !arr_appended.includes(dateString)) {
                            if (listDateOffers.includes(dateString) && !listDateSceneTitles.includes(dateString)) {
                                arr_appended.push(dateString)
                                setTimeout(function () {
                                    $(`.day[data-date="${dateInMls}"]`).append(status_circle_html)
                                }, 10)

                            } else if (!listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                arr_appended.push(dateString)
                                setTimeout(function () {
                                    $(`.day[data-date="${dateInMls}"]`).append(status_circle_blue_html)
                                }, 10)
                            } else if (listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                arr_appended.push(dateString)
                                setTimeout(function () {
                                    $(`.day[data-date="${dateInMls}"]`).append(status_two_circle_html)
                                }, 10)
                            }
                        }
                        arr_appended_for_month = arr_appended;
                        listDateOffers_for_month = listDateOffers;
                        listDateSceneTitles_for_month = listDateSceneTitles;
                        if (listScheduleOff.includes(dateString) && !changed_month && dateString > currentDateFormatted) {
                            return {
                                classes: 'disabled-date',
                            };
                        }
                        if (dateString > currentDateFormatted && isWeekend) {
                            return {
                                classes: 'disabled-date-weekend',
                            };
                        }
                        if (dateString < currentDateFormatted) {
                             return {
                                classes: 'past-date',
                            };
                        }
                    },
                })
                if (!changed_month) {
                    setTimeout(function () {
                        let content = $('.datepicker-switch').html();
                        let arr = content.split(' ');
                        $('.datepicker-switch').html(`<span style="font-family:'A+mfCv-AXISラウンド 50 M StdN';">${arr[0]}</span> ${arr[1]}`);
                    }, 10)
                    scheduleCalendar.datepicker("setDate", 'now');
                } else {
                    $(".day").each(function () {
                        let dateInMls = $(this).data("date");
                        let dateString = $.datepicker.formatDate('yy-mm-dd', new Date(dateInMls));
                        // if (arr_appended_for_month.includes(dateString)) {
                            let dayEl = $(this)
                        if (dateString >= currentDateFormatted) {
                            if (listDateOffers.includes(dateString) && !listDateSceneTitles.includes(dateString)) {
                                dayEl.append(status_circle_html);
                            } else if (!listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                dayEl.append(status_circle_blue_html);
                            } else if (listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                dayEl.append(status_two_circle_html);
                            }
                        }
                    });
                }
                scheduleCalendar.on('changeDate', function (e) {
                    setTimeout(function () {
                        let content = $('.datepicker-switch').html();
                        let arr = content.split(' ');
                        $('.datepicker-switch').html(`<strong>${arr[0]}</strong> ${arr[1]}`);
                    }, 10)
                    let selectedDate = e.date;
                    // const currentDate = new Date();
                    let selectedDateString = selectedDate.getFullYear() + '-' + String(selectedDate.getMonth() + 1).padStart(2, '0') + '-' + String(selectedDate.getDate()).padStart(2, '0');
                    // const currentDateString = currentDate.getDate() + '/' + (currentDate.getMonth() + 1) + '/' + currentDate.getFullYear();
                    $(".day").each(function () {
                        let dateInMls = $(this).data("date");
                        let dateString = $.datepicker.formatDate('yy-mm-dd', new Date(dateInMls));
                        if (arr_appended.includes(dateString)) {
                            if (listDateOffers.includes(dateString) && !listDateSceneTitles.includes(dateString)) {
                                $(this).append(status_circle_html);
                            } else if (!listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                $(this).append(status_circle_blue_html);
                            } else if (listDateOffers.includes(dateString) && listDateSceneTitles.includes(dateString)) {
                                $(this).append(status_two_circle_html);
                            }
                        }
                    });
                    dateSelected = selectedDateString;
                    renderContentSchedule(sortedResultKeys, result_formatted, selectedDateString, false, listScheduleOff)
                });
                renderContentSchedule(sortedResultKeys, result_formatted, undefined, changed_month, listScheduleOff)
                setTimeout(function () {
                //     let heightModal = $('.modal-dialog.sc-block').height();
                //     let block2 = $('.sc-content-block-2');
                //     let heightTitleBlock1 = $('.sc-title').outerHeight(true);
                //     let heightCalendarBlock1 = $('.calendar-content').outerHeight(true);
                //     let heightContentBlock2 = heightModal - (heightTitleBlock1 + heightCalendarBlock1)
                //     block2.css('height', `${heightContentBlock2}px`)
                }, 500)
            },
            error: function (e) {
                toastr.error("Load data schedule failed!");
                console.log('error: ')
                console.log(e)
            },
            complete: function () {
            }
        })
    }

    scheduleCalendar.on('changeMonth', function (e) {
        changed_month = true;
        let selectedDate = e.date; // Lấy ngày được chọn từ sự kiện
        let currentMonth = selectedDate.getMonth() + 1; // Lấy tháng, lưu ý phải cộng thêm 1 vì các tháng bắt đầu từ 0
        let currentYear = selectedDate.getFullYear(); // Lấy năm

        // Ngày 20 của tháng được chọn
        let selectedMonthDate = new Date(currentYear, currentMonth - 2, 20); // Lưu ý trừ đi 1 vì tháng bắt đầu từ 0
        let formattedSelectedMonthDate = formatDate(selectedMonthDate);

        // Ngày 20 của tháng trước đó
        let previousMonthDate = new Date(currentYear, currentMonth - 2, 20); // Lưu ý trừ đi 2 để lấy tháng trước đó
        let formattedPreviousMonthDate = formatDate(previousMonthDate);

        // Ngày 20 của tháng sau đó
        let nextMonthDate = new Date(currentYear, currentMonth, 20); // Không cần trừ gì cả vì muốn lấy tháng sau đó
        let formattedNextMonthDate = formatDate(nextMonthDate);
        getDataSchedule(formattedSelectedMonthDate, formattedNextMonthDate, true)

        setTimeout(function () {
            let content = $('.datepicker-switch').html();
            let arr = content.split(' ');
            $('.datepicker-switch').html(`<strong>${arr[0]}</strong> ${arr[1]}`);
        }, 10)
    })

    $('.calendar-schedule').on('click', function () {
        let scheduleCalendarModal = $('.schedule-modal');
        if (scheduleCalendarModal.hasClass('d-none-schedule')) {
            setTimeout(function () {
                scheduleCalendarModal.removeClass('d-none-schedule')
            }, 100)
        } else {
            setTimeout(function () {
                scheduleCalendarModal.addClass('d-none-schedule')
            }, 100)
        }
    })
})

function renderContentSchedule(sortedResultKeys, result_formatted, date_selected, changed_month, listScheduleOff) {
    if (changed_month) {
        return;
    }
    let nextCurrentDay = new Date(currentDate);
    nextCurrentDay.setDate(nextCurrentDay.getDate() + 1);
    let nextCurrentDayFormatted = nextCurrentDay.toISOString().split('T')[0];
    let afterCurrent7Day = new Date(nextCurrentDay);
    afterCurrent7Day.setDate(afterCurrent7Day.getDate() + 6);
    let afterCurrent7DayFormatted = afterCurrent7Day.toISOString().split('T')[0];
    if (result_formatted) {
        // currentDateFormatted
        let calendar_scene1 = $('.calendar-scene-1');
        let calendar_scene2 = $('.calendar-scene-2');
        calendar_scene1.empty();
        calendar_scene2.empty();
        if (!date_selected) {
            if (currentDateFormatted in result_formatted) {
                let html_title_current_date = `<div class="calendar-title-txt">まもなく配信</div>`;
                calendar_scene1.append(html_title_current_date)
                const filteredKeys = Object.keys(result_formatted).filter(key => key < currentDateFormatted);
                const filteredData = {};
                filteredKeys.forEach(key => {
                    filteredData[key] = result_formatted[key];
                });


                if (result_formatted[currentDateFormatted].scene_titles && result_formatted[currentDateFormatted].scene_titles.length > 0) {
                    for (const filteredDataKey in filteredData) {
                        let filteredDataSceneTitles = filteredData[filteredDataKey].scene_titles
                        if (filteredDataSceneTitles && filteredDataSceneTitles.length > 0){
                            for (const dtst_key in filteredDataSceneTitles) {
                                let html_current_date = '';
                                if (filteredDataSceneTitles[dtst_key].take_number === 0) {
                                    if (filteredDataSceneTitles[dtst_key].take_number === 0) {
                                        html_current_date = `<div class="calendar-scene-item item-new">
                                    <div class="calendar-scene-thumbnail">
                                      <span class="txt-thumbnail">NEW</span>
                                    </div>
                                    <div class="calendar-scene-name">${filteredDataSceneTitles[dtst_key].title}</div>
                                    <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                                </div>`
                                    }
                                }
                                calendar_scene1.append(html_current_date)
                            }
                        }
                    }
                    const scene_title_arr = result_formatted[currentDateFormatted].scene_titles;
                    for (const key_scene_title in scene_title_arr) {
                        let scene_last_version = scene_title_arr[key_scene_title].last_version;
                        let has_scene = true;
                        if (scene_last_version.file_type === 'video' && !scene_last_version.movie && !scene_last_version.thumbnail) {
                            has_scene = false;
                        }
                        let html_current_date = '';
                        if (scene_title_arr[key_scene_title].take_number === 0) {
                            html_current_date = `<div class="calendar-scene-item item-new">
                                    <div class="calendar-scene-thumbnail">
                                      <span class="txt-thumbnail">NEW</span>
                                    </div>
                                    <div class="calendar-scene-name">${scene_title_arr[key_scene_title].title}</div>
                                    <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                                </div>`
                        } else {
                            html_current_date = `<div class="calendar-scene-item ${!has_scene ? 'item-new' : ''}">
                                    <div class="calendar-scene-thumbnail">
                                        ${
                                !has_scene ? '<span class="txt-thumbnail">NEW</span>'
                                    : `<img src="${getThumbnailScene(scene_last_version)}" alt="">`
                            }
                                        </div>
                                    <div class="calendar-scene-name">${scene_title_arr[key_scene_title].title}</div>
                                    <div class="calendar-scene-take take-color-${getColorClassNumber(scene_title_arr[key_scene_title].take_number)}">
                                        <span class="number-take">${scene_title_arr[key_scene_title].take_number}</span>
                                    </div>
                                </div>`
                        }
                        calendar_scene1.append(html_current_date);
                    }
                }
                if (result_formatted[currentDateFormatted].offers && result_formatted[currentDateFormatted].offers.length > 0) {
                    const offer_arr = result_formatted[currentDateFormatted].offers;
                    for (const key_offer in offer_arr) {
                        let html_other_date = '';
                        html_other_date = `<a href="${getUrlMessageOffer(offer_arr[key_offer].project, offer_arr[key_offer].offer_id)}"><div class="user-info-item">
                                <div class="user-avatar-block">
                                    <img src="${offer_arr[key_offer].offer_side === 'creator' ? offer_arr[key_offer].offer_creator.admin_avatar_url : offer_arr[key_offer].offer_creator.creator_avatar_url}"
                                         alt="avatar" class="user-avatar">
                                </div>
                                <div class="user-info-content">
                                    <span class="user-info-name">${offer_arr[key_offer].offer_creator.custom_contract}</span>
                                    <span class="user-info-description">${offer_arr[key_offer].offer_creator.scenes ? '（' + offer_arr[key_offer].offer_creator.scenes + ')' : ''}</span>
                                </div>
                                 <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                            </div></a>`
                        calendar_scene1.append(html_other_date);
                    }
                }
            }
        }
        sortedResultKeys.forEach(key => {
            if (!date_selected) {
                if (key !== currentDateFormatted && (key >= nextCurrentDayFormatted && key <= afterCurrent7DayFormatted)) {
                    calendar_scene2.append(`<div class="calendar-title-txt">${result_formatted[key].date_formatted}</div>`)
                    if (result_formatted[key].scene_titles && result_formatted[key].scene_titles.length > 0) {
                        const scene_title_arr = result_formatted[key].scene_titles;
                        for (const key_scene_title in scene_title_arr) {
                            let html_current_date = '';
                            let has_scene = true;
                            let scene_last_version = scene_title_arr[key_scene_title].last_version;
                            if (scene_last_version.file_type === 'video' && !scene_last_version.movie && !scene_last_version.thumbnail) {
                                has_scene = false;
                            }
                            if (scene_title_arr[key_scene_title].take_number === 0) {
                                html_current_date = `<div class="calendar-scene-item item-new">
                                    <div class="calendar-scene-thumbnail">
                                        <span class="txt-thumbnail">NEW</span>
                                    </div>
                                    <div class="calendar-scene-name">${scene_title_arr[key_scene_title].title}</div>
                                    <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                                </div>`
                            } else {
                                html_current_date = `<div class="calendar-scene-item ${!has_scene ? 'item-new' : ''}">
                                    <div class="calendar-scene-thumbnail">
                                     ${
                                    !has_scene ? '<span class="txt-thumbnail">NEW</span>'
                                        : `<img src="${getThumbnailScene(scene_last_version)}" alt="">`
                                }
                                </div>
                                    <div class="calendar-scene-name">${scene_title_arr[key_scene_title].title}</div>
                                    <div class="calendar-scene-take take-color-${getColorClassNumber(scene_title_arr[key_scene_title].take_number)}">
                                        <span class="number-take">${scene_title_arr[key_scene_title].take_number}</span>
                                    </div>
                                </div>`
                            }
                            calendar_scene2.append(html_current_date);
                        }
                    }
                    if (result_formatted[key].offers && result_formatted[key].offers.length > 0) {
                        const offer_arr = result_formatted[key].offers;
                        for (const key_offer in offer_arr) {
                            let html_other_date = '';
                            html_other_date = `<a href="${getUrlMessageOffer(offer_arr[key_offer].project, offer_arr[key_offer].offer_id)}"><div class="user-info-item">
                                <div class="user-avatar-block">
                                    <img src="${offer_arr[key_offer].offer_side === 'creator' ? offer_arr[key_offer].offer_creator.admin_avatar_url : offer_arr[key_offer].offer_creator.creator_avatar_url }"
                                         alt="avatar" class="user-avatar">
                                </div>
                                <div class="user-info-content">
                                    <span class="user-info-name">${offer_arr[key_offer].offer_creator.custom_contract}</span>
                                    <span class="user-info-description">${offer_arr[key_offer].offer_creator.scenes ? '（' + offer_arr[key_offer].offer_creator.scenes + '）' : ''}</span>
                                </div>
                                 <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                            </div></a>`
                            calendar_scene2.append(html_other_date);
                        }
                    }
                }
            } else {
                calendar_scene1.empty();
                calendar_scene2.empty();
                if (date_selected in result_formatted) {
                    const result_date_selected = result_formatted[date_selected];
                    calendar_scene1.append(`<div class="calendar-title-txt">${result_date_selected.date_formatted}</div>`)
                    if (result_date_selected.scene_titles && result_date_selected.scene_titles.length > 0) {
                        const scene_title_selected_arr = result_date_selected.scene_titles;
                        for (const key_scene_title_selected in scene_title_selected_arr) {
                            let html_current_date = '';
                            let has_scene = true;
                            let scene_last_version = scene_title_selected_arr[key_scene_title_selected].last_version;
                            if (scene_last_version.file_type === 'video' && !scene_last_version.movie && !scene_last_version.thumbnail) {
                                has_scene = false;
                            }
                            if (scene_title_selected_arr[key_scene_title_selected].take_number === 0) {
                                html_current_date = `<div class="calendar-scene-item item-new">
                                    <div class="calendar-scene-thumbnail">
                                        <span class="txt-thumbnail">NEW</span>
                                    </div>
                                    <div class="calendar-scene-name">${scene_title_selected_arr[key_scene_title_selected].title}</div>
                                    <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                                </div>`
                            } else {
                                html_current_date = `<div class="calendar-scene-item ${!has_scene ? 'item-new' : ''}">
                                    <div class="calendar-scene-thumbnail">
                                     ${
                                    !has_scene ? '<span class="txt-thumbnail">NEW</span>'
                                        : `<img src="${getThumbnailScene(scene_last_version)}" alt="">`
                                }
                            </div>
                                    <div class="calendar-scene-name">${scene_title_selected_arr[key_scene_title_selected].title}</div>
                                    <div class="calendar-scene-take take-color-${getColorClassNumber(scene_title_selected_arr[key_scene_title_selected].take_number)}">
                                        <span class="number-take">${scene_title_selected_arr[key_scene_title_selected].take_number}</span>
                                    </div>
                                </div>`
                            }
                            calendar_scene1.append(html_current_date);
                        }

                    }
                    if (result_date_selected.offers && result_date_selected.offers.length > 0) {
                        const offer_selected_arr = result_date_selected.offers;
                        for (const key_offer_selected in offer_selected_arr) {
                            let html_other_date = '';
                            html_other_date = `<a href="${getUrlMessageOffer(offer_selected_arr[key_offer_selected].project, offer_selected_arr[key_offer_selected].offer_id)}"><div class="user-info-item">
                                <div class="user-avatar-block">
                                    <img src="${offer_selected_arr[key_offer_selected].offer_side === 'creator' ? offer_selected_arr[key_offer_selected].offer_creator.admin_avatar_url : offer_selected_arr[key_offer_selected].offer_creator.creator_avatar_url}"
                                         alt="avatar" class="user-avatar">
                                </div>
                                <div class="user-info-content">
                                    <span class="user-info-name">${offer_selected_arr[key_offer_selected].offer_creator.custom_contract}</span>
                                    <span class="user-info-description">${offer_selected_arr[key_offer_selected].offer_creator.scenes ? '（' + offer_selected_arr[key_offer_selected].offer_creator.scenes + '）' : ''}</span>
                                </div>
                                 <span class="material-symbols-rounded icon-next-to-detail">navigate_next</span>
                            </div></a>`
                            calendar_scene1.append(html_other_date);
                        }
                    }
                }
            }
        })
        if ($('.calendar-scene-1 .calendar-title-txt').length === 0 && $('.calendar-scene-2 .calendar-title-txt').length > 0) {
            $('.calendar-scene-2 .calendar-title-txt:first-child').css({
                'border-top': 'none',
                'margin-top': '0px'
            })
        }
    }
    $('.user-info-item').hover(function () {
            $(this).find('.icon-next-to-detail').css('visibility', 'visible')
        },
        function () {
            $(this).find('.icon-next-to-detail').css('visibility', 'hidden')
        }
    )
}

function checkColorTakeSchedule(index) {
    if (parseInt(index) >= 1 && parseInt(index) <= 3) {
        return parseInt(index);
    }
    if (parseInt(index) % 3 !== 0) {
        return parseInt(index) % 3;
    }
    return 3;
}

// Hàm xử lý dữ liệu
// Hàm tính toán ngày định dạng
function formatDateTitle(dateString) {
    const date = new Date(dateString);
    const dayOfWeek = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'][date.getDay()];
    const year = date.getFullYear().toString().slice(-2);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}/${month}/${day} (${dayOfWeek})`;
}

// Hàm xử lý dữ liệu
function processData(offers, scene_titles) {
    let integratedData = {};

    // Xử lý offers
    offers.forEach(item => {
        const deadlineDate = item.offer_creator.deadline.split('T')[0];
        if (!integratedData[deadlineDate]) {
            integratedData[deadlineDate] = {offers: [], date_formatted: formatDateTitle(deadlineDate)};
        }
        integratedData[deadlineDate].offers.push(item);
    });

    // Xử lý scene_titles
    scene_titles.forEach(item => {
        let scheduleDate = item.last_version.schedule_date.split('T')[0];
        if (!integratedData[scheduleDate]) {
            integratedData[scheduleDate] = {scene_titles: [], date_formatted: formatDateTitle(scheduleDate)};
        }
        // Kiểm tra nếu integratedData[scheduleDate].scene_titles không phải mảng, hãy khởi tạo nó thành mảng trước khi push
        if (!Array.isArray(integratedData[scheduleDate].scene_titles)) {
            integratedData[scheduleDate].scene_titles = [];
        }
        integratedData[scheduleDate].scene_titles.push(item);
    });

    return integratedData;
}

function getColorClassNumber(index) {
    return ((index - 1) % 3) + 1;
}

function getUrlMessageOffer(project_id, offer_id) {
    const base_url = window.location.origin;
    const path_project = '/top/project/';
    const path_messenger = '?tab=messenger&offer=';
    const path_from_refer = '&from_refer=true';
    return base_url + path_project + project_id + path_messenger + offer_id + path_from_refer;
}

function getThumbnailScene(last_version) {

    switch (last_version.file_type) {
        case 'video':
        case 'image':
            return last_version.thumbnail;
        case 'document':
            return imagePDFPath;
        case 'audio':
            return imageAudioPath;
        default:
            return '';
    }
}

function getDateRemaining() {
    let projectDataElement = $('.project-item')
    let endTime = projectDataElement.attr("data-project-endtime")
    let mileStoneName = projectDataElement.attr("data-milestone-name")
    let mileStoneTime = projectDataElement.attr("data-milestone-time")
    var remainingDays;
    var headerText = '';
    let today = moment().startOf('day')
    if(mileStoneTime) {
       remainingDays = moment(mileStoneTime).diff(today, 'days');
       headerText = `${mileStoneName ||'マイルストーン'}まであと${remainingDays} 日`;
    } else if(today.isBefore(moment(endTime))) {
        remainingDays = moment(endTime).diff(today, 'days');
        headerText = `納期まであと${remainingDays}日`
    }
    if (!headerText) {
        $('.sc-title').css('display', 'none');
    }else {
        $('.sc-title-txt').text(headerText);
    }
}

