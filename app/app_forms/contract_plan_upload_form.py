import ast
import json

from django import forms

from app.models import FormContractAndPlan


class ContractPlanUploadForm(forms.ModelForm):
    file = forms.CharField(required=False)

    class Meta:
        model = FormContractAndPlan
        fields = ('subject', 'work_content', 'delivery_format', 'pick_up_method', 'delivery_place', 'release_time', 'start_schedule', 'end_schedule',
                  'deadline', 'note', 'allow_public_contract', 'owner_info', 'producer_info', 'creation_method', 'file', 'form_type', 'semi_delegate', 'note_type', 'valid_date', 'pre_deadline', 'owner_infor')

    def clean_work_content(self):
        return json.dumps(ast.literal_eval(self.cleaned_data['work_content']))

    def clean_owner_info(self):
        return json.dumps(ast.literal_eval(self.cleaned_data['owner_info']))

    def clean_producer_info(self):
        return json.dumps(ast.literal_eval(self.cleaned_data['producer_info']))

    def clean(self):
        # if self.cleaned_data['creation_method'] == FormContractAndPlan.UPLOAD_METHOD and \
        #         (not self.cleaned_data['file'] or self.cleaned_data['file'] == 'undefined'):
        #     self._errors['file'] = 'File is required with upload method'

        if not self.instance:
            return self.cleaned_data

        is_changed = False
        if self.cleaned_data['creation_method'] == FormContractAndPlan.UPLOAD_METHOD:
            for field in self.fields.keys():
                attr_names = [f.name for f in self.instance._meta.get_fields()]
                if field in attr_names and self.cleaned_data[field] != getattr(self.instance, field):
                    is_changed = True
                    break
            if not is_changed and (not self.cleaned_data['file'] or self.cleaned_data['file'] == 'undefined'):
                self._errors['base'] = 'Data not change'
        else:
            is_changed = True

        self.cleaned_data['is_changed'] = is_changed

        return self.cleaned_data

    def save(self, commit=True, **kwargs):
        for key, value in kwargs.items():
            setattr(self.instance, key, value)
        super().save(commit)
